<template>
  <div class="operation-log-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon><Document /></el-icon>
            操作日志
          </h1>
          <p class="page-description">查看和管理系统操作日志记录</p>
        </div>
        <div class="header-right">
          <el-button 
            type="primary" 
            @click="handleRefresh"
            :loading="loading"
          >
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
          <el-button 
            type="success" 
            @click="showExportDialog"
            :loading="exportLoading"
          >
            <el-icon><Download /></el-icon>
            导出日志
          </el-button>
        </div>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="filter-section">
      <el-card shadow="never">
        <div class="filter-header">
          <h3>搜索筛选</h3>
          <div class="filter-mode-switch">
            <el-radio-group v-model="searchMode" size="small">
              <el-radio-button value="simple">简单搜索</el-radio-button>
              <el-radio-button value="advanced">高级搜索</el-radio-button>
            </el-radio-group>
          </div>
          <el-button 
            text 
            @click="toggleFilterExpanded"
            :icon="filterExpanded ? ArrowUp : ArrowDown"
          >
            {{ filterExpanded ? '收起' : '展开' }}
          </el-button>
        </div>
        
        <el-collapse-transition>
          <div v-show="filterExpanded" class="filter-content">
            <!-- 简单搜索 -->
            <div v-if="searchMode === 'simple'" class="simple-search">
              <el-form 
                :model="filterForm" 
                inline 
                label-width="80px"
                @submit.prevent="handleSearch"
              >
                <el-row :gutter="12">
                  <el-col :span="8">
                    <el-form-item label="操作者">
                      <el-input 
                        v-model="filterForm.operatorName"
                        placeholder="输入操作者用户名"
                        clearable
                        size="default"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="目标用户">
                      <el-input 
                        v-model="filterForm.targetUsername"
                        placeholder="输入目标用户名"
                        clearable
                        size="default"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="操作结果">
                      <el-select 
                        v-model="filterForm.operationResult"
                        placeholder="选择操作结果"
                        clearable
                        size="default"
                        style="width: 100%; min-width: 120px;"
                      >
                        <el-option label="成功" value="SUCCESS" />
                        <el-option label="失败" value="FAILED" />
                        <el-option label="部分成功" value="PARTIAL_SUCCESS" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                
                <el-row :gutter="12">
                  <el-col :span="8">
                    <el-form-item label="关键词">
                      <el-input 
                        v-model="filterForm.keyword"
                        placeholder="搜索操作描述"
                        clearable
                        size="default"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="10">
                    <el-form-item label="时间范围">
                      <el-date-picker
                        v-model="filterForm.dateRange"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DDTHH:mm:ss"
                        size="default"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item>
                      <div class="filter-actions-inline">
                        <el-button 
                          type="primary" 
                          @click="handleSearch"
                          :loading="loading"
                          size="default"
                        >
                          <el-icon><Search /></el-icon>
                          搜索
                        </el-button>
                        <el-button 
                          @click="handleResetFilter"
                          size="default"
                        >
                          <el-icon><RefreshLeft /></el-icon>
                          重置
                        </el-button>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>

            <!-- 高级搜索 -->
            <div v-else class="advanced-search">
              <AdvancedSearchBuilder
                v-model="advancedSearchQuery"
                :operation-types="operationTypes"
                @search="handleAdvancedSearch"
              />
            </div>
          </div>
        </el-collapse-transition>
      </el-card>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section">
      <el-card shadow="never">
        <div class="table-header">
          <div class="table-title">
            <h3>操作日志列表</h3>
            <span class="table-count">共 {{ pagination.total }} 条记录</span>
          </div>
        </div>

        <el-table
          v-loading="loading"
          :data="logList"
          row-key="id"
          style="width: 100%"
          @sort-change="handleSortChange"
        >
          <el-table-column 
            prop="id" 
            label="ID" 
            width="80" 
            sortable="custom"
            show-overflow-tooltip
          />
          
          <el-table-column 
            prop="operatorName" 
            label="操作者" 
            width="120"
            show-overflow-tooltip
          />
          
          <el-table-column 
            prop="targetUsername" 
            label="目标用户" 
            width="120"
            show-overflow-tooltip
          />
          
          <el-table-column 
            prop="operationType" 
            label="操作类型" 
            width="150"
            show-overflow-tooltip
          >
            <template #default="scope">
              <el-tag 
                :type="getOperationTypeTagType(scope.row.operationType)"
                size="small"
              >
                {{ getOperationTypeDesc(scope.row.operationType) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column 
            prop="operationDesc" 
            label="操作描述" 
            min-width="200"
            show-overflow-tooltip
          />
          
          <el-table-column 
            prop="operationResult" 
            label="操作结果" 
            width="100"
            align="center"
          >
            <template #default="scope">
              <el-tag 
                :type="getResultTagType(scope.row.operationResult)"
                size="small"
              >
                {{ getResultText(scope.row.operationResult) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column 
            prop="ipAddress" 
            label="IP地址" 
            width="120"
            show-overflow-tooltip
          />
          
          <el-table-column 
            prop="createdAt" 
            label="操作时间" 
            width="180"
            sortable="custom"
            show-overflow-tooltip
          >
            <template #default="scope">
              {{ formatDateTime(scope.row.createdAt) }}
            </template>
          </el-table-column>
          
          <el-table-column 
            label="操作" 
            width="120" 
            fixed="right"
            align="center"
          >
            <template #default="scope">
              <el-button 
                type="primary" 
                size="small" 
                text
                @click="handleViewDetail(scope.row)"
              >
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 操作日志详情对话框 -->
    <OperationLogDetailDialog 
      v-model="detailDialogVisible"
      :log-data="selectedLog"
      @refresh="handleRefresh"
    />

    <!-- 导出选项对话框 -->
    <ExportOptionsDialog
      v-model="exportDialogVisible"
      :current-filters="getCurrentFilters()"
      :total-records="pagination.total"
      @export="handleConfirmExport"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Document, Refresh, Download, Search, RefreshLeft,
  ArrowUp, ArrowDown
} from '@element-plus/icons-vue'
import { request } from '@/utils/request'
import OperationLogDetailDialog from './components/OperationLogDetailDialog.vue'
import AdvancedSearchBuilder from './components/AdvancedSearchBuilder.vue'
import ExportOptionsDialog from './components/ExportOptionsDialog.vue'

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const exportDialogVisible = ref(false)
const filterExpanded = ref(false)
const detailDialogVisible = ref(false)
const selectedLog = ref(null)
const searchMode = ref('simple') // 'simple' | 'advanced'
const advancedSearchQuery = ref(null)

// 日志列表数据
const logList = ref([])

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 筛选表单
const filterForm = reactive({
  operatorName: '',
  targetUsername: '',
  operationResult: '',
  keyword: '',
  dateRange: [],
  sortBy: 'createdAt',
  sortDirection: 'DESC'
})

// 计算属性
const computedSortBy = computed(() => filterForm.sortBy)
const computedSortDirection = computed(() => filterForm.sortDirection)

// 操作类型数据
const operationTypes = ref({
  'VIEW_USER_LIST': '查看用户列表',
  'VIEW_USER_DETAIL': '查看用户详情',
  'VIEW_USER_STATS': '查看用户统计',
  'UPDATE_USER_INFO': '更新用户信息',
  'UPDATE_USER_STATUS': '变更用户状态',
  'BATCH_ENABLE_USERS': '批量启用用户',
  'BATCH_DISABLE_USERS': '批量禁用用户',
  'BATCH_UPGRADE_VIP': '批量升级VIP',
  'BATCH_DOWNGRADE_BASIC': '批量降级基础版'
})

// 生命周期
onMounted(() => {
  loadLogList()
})

// 方法定义
const handleRefresh = () => {
  loadLogList()
}

const toggleFilterExpanded = () => {
  filterExpanded.value = !filterExpanded.value
}

const handleSearch = () => {
  pagination.page = 1
  loadLogList()
}

const handleResetFilter = () => {
  Object.assign(filterForm, {
    operatorName: '',
    targetUsername: '',
    operationResult: '',
    keyword: '',
    dateRange: [],
    sortBy: 'createdAt',
    sortDirection: 'DESC'
  })
  pagination.page = 1
  loadLogList()
}

const handleSortChange = ({ prop, order }) => {
  filterForm.sortBy = prop || 'createdAt'
  filterForm.sortDirection = order === 'ascending' ? 'ASC' : 'DESC'
  loadLogList()
}

const handlePageChange = (page: number) => {
  pagination.page = page
  loadLogList()
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  loadLogList()
}

const showExportDialog = () => {
  exportDialogVisible.value = true
}

const getCurrentFilters = () => {
  if (searchMode.value === 'simple') {
    return {
      operatorName: filterForm.operatorName,
      targetUsername: filterForm.targetUsername,
      operationResult: filterForm.operationResult,
      keyword: filterForm.keyword,
      dateRange: filterForm.dateRange
    }
  } else if (searchMode.value === 'advanced' && advancedSearchQuery.value) {
    return convertAdvancedQueryToParams(advancedSearchQuery.value)
  }
  return {}
}

const handleConfirmExport = async (exportOptions: any) => {
  try {
    exportLoading.value = true
    
    // 构建导出参数
    const params: any = {
      format: exportOptions.format,
      maxRecords: exportOptions.maxRecords
    }
    
    // 添加当前筛选条件
    const currentFilters = getCurrentFilters()
    Object.assign(params, currentFilters)
    
    // 使用 request 发送带认证头的请求
    const response = await request.get('/usermgmt/logs/export', { 
      params,
      responseType: 'blob' // 重要：指定响应类型为blob
    })
    
    // 创建下载链接
    const blob = new Blob([response], { 
      type: exportOptions.format === 'excel' 
        ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
        : 'text/csv; charset=utf-8' 
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `操作日志_${new Date().toISOString().slice(0, 10)}.${exportOptions.format === 'excel' ? 'xlsx' : 'csv'}`
    link.style.display = 'none'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    
    ElMessage.success(`操作日志导出已开始，导出格式：${exportOptions.format.toUpperCase()}`)
    
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  } finally {
    exportLoading.value = false
  }
}

const handleViewDetail = (log: any) => {
  selectedLog.value = log
  detailDialogVisible.value = true
}

const handleAdvancedSearch = (query: any) => {
  // 保存高级搜索查询以便后续使用
  advancedSearchQuery.value = query
  // 将高级搜索条件转换为API参数
  const searchParams = convertAdvancedQueryToParams(query)
  pagination.page = 1
  loadLogList(searchParams)
}

const convertAdvancedQueryToParams = (query: any) => {
  const params: any = {}
  
  if (!query || !query.groups || query.groups.length === 0) {
    return params
  }
  
  // 简化处理：目前只处理第一个条件组的条件
  const firstGroup = query.groups[0]
  if (firstGroup && firstGroup.conditions) {
    firstGroup.conditions.forEach((condition: any) => {
      const { field, comparison, value } = condition
      
      if (!field || value === null || value === undefined || value === '') return
      
      switch (field) {
        case 'operatorId':
          params.operatorId = value
          break
        case 'operatorName':
          if (comparison === 'CONTAINS' || comparison === 'EQUALS') {
            params.operatorName = value
          }
          break
        case 'targetUserId':
          params.targetUserId = value
          break
        case 'targetUsername':
          if (comparison === 'CONTAINS' || comparison === 'EQUALS') {
            params.targetUsername = value
          }
          break
        case 'operationType':
          params.operationType = value
          break
        case 'operationResult':
          params.operationResult = value
          break
        case 'operationDesc':
          params.keyword = value
          break
        case 'ipAddress':
          if (comparison === 'CONTAINS' || comparison === 'EQUALS') {
            params.ipAddress = value
          } else if (comparison === 'IN' && Array.isArray(value)) {
            // 将数组转换为逗号分隔的字符串
            params.ipAddress = value.join(',')
          } else if (comparison === 'IN' && typeof value === 'string') {
            params.ipAddress = value
          }
          break
        case 'requestUri':
          if (comparison === 'CONTAINS' || comparison === 'EQUALS') {
            params.requestUri = value
          } else if (comparison === 'IN' && Array.isArray(value)) {
            // 将数组转换为逗号分隔的字符串
            params.requestUri = value.join(',')
          } else if (comparison === 'IN' && typeof value === 'string') {
            params.requestUri = value
          }
          break
        case 'requestMethod':
          if (comparison === 'EQUALS') {
            params.requestMethod = value
          } else if (comparison === 'IN' && Array.isArray(value)) {
            // 将数组转换为逗号分隔的字符串
            params.requestMethod = value.join(',')
          } else if (comparison === 'IN' && typeof value === 'string') {
            params.requestMethod = value
          }
          break
        case 'createdAt':
          if (comparison === 'DATE_RANGE' && Array.isArray(value) && value.length === 2) {
            params.startTime = value[0]
            params.endTime = value[1]
          } else if (comparison === 'TODAY') {
            const today = new Date()
            const startOfDay = new Date(today.setHours(0, 0, 0, 0))
            const endOfDay = new Date(today.setHours(23, 59, 59, 999))
            params.startTime = startOfDay.toISOString()
            params.endTime = endOfDay.toISOString()
          } else if (comparison === 'YESTERDAY') {
            const yesterday = new Date()
            yesterday.setDate(yesterday.getDate() - 1)
            const startOfDay = new Date(yesterday.setHours(0, 0, 0, 0))
            const endOfDay = new Date(yesterday.setHours(23, 59, 59, 999))
            params.startTime = startOfDay.toISOString()
            params.endTime = endOfDay.toISOString()
          } else if (comparison === 'AFTER') {
            params.startTime = value
          } else if (comparison === 'BEFORE') {
            params.endTime = value
          }
          break
        default:
          // 未处理的字段
      }
    })
  }
  
  return params
}

// API调用方法
const loadLogList = async (extraParams: any = {}) => {
  try {
    loading.value = true
    
    const params: any = {
      page: pagination.page,
      size: pagination.size,
      sortBy: filterForm.sortBy,
      sortDirection: filterForm.sortDirection
    }
    
    // 如果传入了额外参数（通常来自高级搜索），直接使用
    if (Object.keys(extraParams).length > 0) {
      Object.assign(params, extraParams)
    } else if (searchMode.value === 'simple') {
      // 简单搜索模式下，添加简单搜索的筛选条件
      if (filterForm.operatorName) params.operatorName = filterForm.operatorName
      if (filterForm.targetUsername) params.targetUsername = filterForm.targetUsername
      if (filterForm.operationResult) params.operationResult = filterForm.operationResult
      if (filterForm.keyword) params.keyword = filterForm.keyword
      
      // 处理时间范围
      if (filterForm.dateRange && filterForm.dateRange.length === 2) {
        params.startTime = filterForm.dateRange[0]
        params.endTime = filterForm.dateRange[1]
      }
    }
    
    const response = await request.get('/usermgmt/logs/list', { params })
    
    if (response.code === 200) {
      logList.value = response.data.content
      pagination.total = response.data.totalElements
    } else {
      ElMessage.error(response.message || '加载操作日志失败')
    }
  } catch (error) {
    console.error('加载操作日志失败:', error)
    ElMessage.error('加载操作日志失败，请重试')
  } finally {
    loading.value = false
  }
}

// 辅助方法
const getOperationTypeTagType = (operationType: string) => {
  if (operationType.includes('BATCH')) return 'warning'
  if (operationType.includes('DELETE') || operationType.includes('DISABLE')) return 'danger'
  if (operationType.includes('UPDATE') || operationType.includes('ENABLE')) return 'primary'
  if (operationType.includes('VIEW') || operationType.includes('SEARCH')) return 'info'
  return 'success'
}

const getOperationTypeDesc = (operationType: string) => {
  // 直接返回操作类型，不再依赖operationTypes映射
  const typeMap: Record<string, string> = {
    'VIEW_USER_LIST': '查看用户列表',
    'VIEW_USER_DETAIL': '查看用户详情',
    'VIEW_USER_STATS': '查看用户统计',
    'UPDATE_USER_INFO': '更新用户信息',
    'UPDATE_USER_STATUS': '变更用户状态',
    'BATCH_ENABLE_USERS': '批量启用用户',
    'BATCH_DISABLE_USERS': '批量禁用用户',
    'BATCH_UPGRADE_VIP': '批量升级VIP',
    'BATCH_DOWNGRADE_BASIC': '批量降级基础版'
  }
  return typeMap[operationType] || operationType
}

const getResultTagType = (result: string) => {
  switch (result) {
    case 'SUCCESS': return 'success'
    case 'FAILED': return 'danger'
    case 'PARTIAL_SUCCESS': return 'warning'
    default: return 'info'
  }
}

const getResultText = (result: string) => {
  switch (result) {
    case 'SUCCESS': return '成功'
    case 'FAILED': return '失败'
    case 'PARTIAL_SUCCESS': return '部分成功'
    default: return result
  }
}

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '--'
  return new Date(dateTime).toLocaleString('zh-CN')
}
</script>

<style scoped>
.operation-log-page {
  padding: 24px;
  background-color: var(--el-bg-color-page);
  min-height: calc(100vh - 64px);
}

.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--el-bg-color);
  padding: 24px;
  border-radius: 8px;
  box-shadow: var(--el-box-shadow-light);
  border: 1px solid var(--el-border-color-light);
}

.header-left {
  display: flex;
  flex-direction: column;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-description {
  margin: 0;
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.filter-section {
  margin-bottom: 24px;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.filter-header h3 {
  margin: 0;
  font-size: 16px;
  color: var(--el-text-color-primary);
}

.filter-mode-switch {
  flex: 1;
  display: flex;
  justify-content: center;
}

.filter-content {
  padding-top: 16px;
  border-top: 1px solid var(--el-border-color-lighter);
}

.simple-search {
  /* 简单搜索样式 */
}

.advanced-search {
  /* 高级搜索样式 */
}

.filter-actions {
  text-align: center;
  padding-top: 16px;
}

.filter-actions-inline {
  display: flex;
  gap: 8px;
}

.table-section {
  background: var(--el-bg-color);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--el-border-color-light);
}

.table-header {
  padding: 20px 20px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.table-title h3 {
  margin: 0;
  font-size: 16px;
  color: var(--el-text-color-primary);
}

.table-count {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.pagination-container {
  padding: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-table) {
  border: none;
}

:deep(.el-table th) {
  background-color: var(--el-fill-color-lighter);
  color: var(--el-text-color-primary);
  font-weight: 600;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style>