package com.rickpan.service;

import com.rickpan.dto.SysMonitorDataDTO;
import com.rickpan.entity.SysMonitorAlertConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 告警规则引擎
 * 负责评估监控指标是否超过配置的阈值
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@Slf4j
public class AlertRuleEngine {

    /**
     * 评估指标是否超过阈值
     * 
     * @param currentValue 当前指标值
     * @param config 告警配置
     * @return 是否超过阈值
     */
    public boolean evaluateThreshold(Double currentValue, SysMonitorAlertConfig config) {
        if (currentValue == null || config == null) {
            return false;
        }

        // 获取触发的阈值
        BigDecimal threshold = getTriggeredThreshold(currentValue, config);
        if (threshold == null) {
            return false;
        }

        // 根据比较操作符进行比较
        return compareValue(currentValue, threshold.doubleValue(), config.getComparisonOperator());
    }

    /**
     * 确定告警级别
     * 
     * @param currentValue 当前指标值
     * @param config 告警配置
     * @return 告警级别 (WARNING/CRITICAL/EMERGENCY)
     */
    public String determineAlertLevel(Double currentValue, SysMonitorAlertConfig config) {
        if (currentValue == null || config == null) {
            return null;
        }

        // 按照严重程度从高到低检查
        if (isThresholdExceeded(currentValue, config.getEmergencyThreshold(), config.getComparisonOperator())) {
            return "EMERGENCY";
        }
        
        if (isThresholdExceeded(currentValue, config.getCriticalThreshold(), config.getComparisonOperator())) {
            return "CRITICAL";
        }
        
        if (isThresholdExceeded(currentValue, config.getWarningThreshold(), config.getComparisonOperator())) {
            return "WARNING";
        }

        return null;
    }

    /**
     * 从监控数据中提取指定指标的值
     * 
     * @param monitorData 监控数据
     * @param metricType 指标类型
     * @param metricName 指标名称
     * @return 指标值
     */
    public Double extractMetricValue(SysMonitorDataDTO monitorData, String metricType, String metricName) {
        if (monitorData == null || metricType == null || metricName == null) {
            return null;
        }

        try {
            switch (metricType.toUpperCase()) {
                case "CPU":
                    return extractCpuMetric(monitorData, metricName);
                case "MEMORY":
                    return extractMemoryMetric(monitorData, metricName);
                case "DISK":
                    return extractDiskMetric(monitorData, metricName);
                case "JVM":
                    return extractJvmMetric(monitorData, metricName);
                case "THREAD":
                    return extractThreadMetric(monitorData, metricName);
                case "GC":
                    return extractGcMetric(monitorData, metricName);
                default:
                    log.warn("未知的指标类型: {}", metricType);
                    return null;
            }
        } catch (Exception e) {
            log.error("提取指标值失败: metricType={}, metricName={}", metricType, metricName, e);
            return null;
        }
    }

    /**
     * 获取触发的阈值（返回最接近当前值的阈值）
     */
    private BigDecimal getTriggeredThreshold(Double currentValue, SysMonitorAlertConfig config) {
        // 按照严重程度从高到低检查，返回第一个被触发的阈值
        if (isThresholdExceeded(currentValue, config.getEmergencyThreshold(), config.getComparisonOperator())) {
            return config.getEmergencyThreshold();
        }
        
        if (isThresholdExceeded(currentValue, config.getCriticalThreshold(), config.getComparisonOperator())) {
            return config.getCriticalThreshold();
        }
        
        if (isThresholdExceeded(currentValue, config.getWarningThreshold(), config.getComparisonOperator())) {
            return config.getWarningThreshold();
        }

        return null;
    }

    /**
     * 检查是否超过指定阈值
     */
    private boolean isThresholdExceeded(Double currentValue, BigDecimal threshold, String operator) {
        if (threshold == null) {
            return false;
        }
        return compareValue(currentValue, threshold.doubleValue(), operator);
    }

    /**
     * 根据操作符比较值
     */
    private boolean compareValue(Double currentValue, Double thresholdValue, String operator) {
        if (currentValue == null || thresholdValue == null || operator == null) {
            return false;
        }

        return switch (operator.toUpperCase()) {
            case "GT" -> currentValue > thresholdValue;
            case "GTE" -> currentValue >= thresholdValue;
            case "LT" -> currentValue < thresholdValue;
            case "LTE" -> currentValue <= thresholdValue;
            case "EQ" -> currentValue.equals(thresholdValue);
            default -> {
                log.warn("未知的比较操作符: {}", operator);
                yield false;
            }
        };
    }

    /**
     * 提取CPU相关指标
     */
    private Double extractCpuMetric(SysMonitorDataDTO monitorData, String metricName) {
        if (monitorData.getCpu() == null) {
            return null;
        }

        return switch (metricName.toLowerCase()) {
            case "cpu_usage", "usage" -> monitorData.getCpu().getUsage() != null ? 
                monitorData.getCpu().getUsage().doubleValue() : null;
            case "cpu_cores", "cores" -> (double) monitorData.getCpu().getCores();
            case "cpu_process_usage", "process_usage" -> monitorData.getCpu().getProcessUsage() != null ? 
                monitorData.getCpu().getProcessUsage().doubleValue() : null;
            case "cpu_load_1min", "load_1min" -> monitorData.getCpu().getLoadAverage() != null && 
                monitorData.getCpu().getLoadAverage().length > 0 ? 
                monitorData.getCpu().getLoadAverage()[0].doubleValue() : null;
            case "cpu_load_5min", "load_5min" -> monitorData.getCpu().getLoadAverage() != null && 
                monitorData.getCpu().getLoadAverage().length > 1 ? 
                monitorData.getCpu().getLoadAverage()[1].doubleValue() : null;
            case "cpu_load_15min", "load_15min" -> monitorData.getCpu().getLoadAverage() != null && 
                monitorData.getCpu().getLoadAverage().length > 2 ? 
                monitorData.getCpu().getLoadAverage()[2].doubleValue() : null;
            default -> null;
        };
    }

    /**
     * 提取内存相关指标
     */
    private Double extractMemoryMetric(SysMonitorDataDTO monitorData, String metricName) {
        if (monitorData.getMemory() == null) {
            return null;
        }

        return switch (metricName.toLowerCase()) {
            case "memory_usage", "usage" -> monitorData.getMemory().getUsage() != null ? 
                monitorData.getMemory().getUsage().doubleValue() : null;
            case "memory_total", "total" -> (double) monitorData.getMemory().getTotal();
            case "memory_used", "used" -> (double) monitorData.getMemory().getUsed();
            case "memory_free", "free" -> (double) monitorData.getMemory().getFree();
            case "memory_available", "available" -> (double) monitorData.getMemory().getAvailable();
            default -> null;
        };
    }

    /**
     * 提取磁盘相关指标
     */
    private Double extractDiskMetric(SysMonitorDataDTO monitorData, String metricName) {
        if (monitorData.getDisk() == null) {
            return null;
        }

        return switch (metricName.toLowerCase()) {
            case "disk_usage", "usage" -> monitorData.getDisk().getUsage() != null ? 
                monitorData.getDisk().getUsage().doubleValue() : null;
            case "disk_total", "total" -> (double) monitorData.getDisk().getTotal();
            case "disk_used", "used" -> (double) monitorData.getDisk().getUsed();
            case "disk_available", "available" -> (double) monitorData.getDisk().getAvailable();
            default -> null;
        };
    }

    /**
     * 提取JVM相关指标
     */
    private Double extractJvmMetric(SysMonitorDataDTO monitorData, String metricName) {
        if (monitorData.getJvm() == null) {
            return null;
        }

        return switch (metricName.toLowerCase()) {
            case "jvm_heap_usage", "heap_usage" -> monitorData.getJvm().getHeapUsage() != null ? 
                monitorData.getJvm().getHeapUsage().doubleValue() : null;
            case "jvm_heap_used", "heap_used" -> (double) monitorData.getJvm().getHeapUsed();
            case "jvm_heap_max", "heap_max" -> (double) monitorData.getJvm().getHeapMax();
            case "jvm_heap_committed", "heap_committed" -> (double) monitorData.getJvm().getHeapCommitted();
            case "jvm_non_heap_used", "non_heap_used" -> (double) monitorData.getJvm().getNonHeapUsed();
            case "jvm_non_heap_max", "non_heap_max" -> (double) monitorData.getJvm().getNonHeapMax();
            case "jvm_non_heap_committed", "non_heap_committed" -> (double) monitorData.getJvm().getNonHeapCommitted();
            case "jvm_non_heap_usage", "non_heap_usage" -> monitorData.getJvm().getNonHeapUsage() != null ? 
                monitorData.getJvm().getNonHeapUsage().doubleValue() : null;
            case "jvm_uptime", "uptime" -> (double) monitorData.getJvm().getUptime();
            default -> null;
        };
    }

    /**
     * 提取线程相关指标
     */
    private Double extractThreadMetric(SysMonitorDataDTO monitorData, String metricName) {
        if (monitorData.getThreads() == null) {
            return null;
        }

        return switch (metricName.toLowerCase()) {
            case "thread_count", "active" -> (double) monitorData.getThreads().getActive();
            case "thread_peak_count", "peak" -> (double) monitorData.getThreads().getPeak();
            case "thread_daemon_count", "daemon" -> (double) monitorData.getThreads().getDaemon();
            case "thread_total_started", "total_started" -> (double) monitorData.getThreads().getTotalStarted();
            case "thread_user", "user" -> (double) monitorData.getThreads().getUser();
            case "thread_deadlocked", "deadlocked" -> (double) monitorData.getThreads().getDeadlocked();
            default -> null;
        };
    }

    /**
     * 提取GC相关指标
     */
    private Double extractGcMetric(SysMonitorDataDTO monitorData, String metricName) {
        if (monitorData.getGc() == null) {
            return null;
        }

        return switch (metricName.toLowerCase()) {
            case "gc_count", "count" -> (double) monitorData.getGc().getCount();
            case "gc_time", "time" -> (double) monitorData.getGc().getTime();
            case "gc_avg_time", "avg_time" -> monitorData.getGc().getAvgTime() != null ? 
                monitorData.getGc().getAvgTime().doubleValue() : null;
            case "gc_max_time", "max_time" -> (double) monitorData.getGc().getMaxTime();
            default -> null;
        };
    }
}