package com.rickpan.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.concurrent.Executor;

/**
 * 系统监控模块配置类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class SysMonitorConfig {

    /**
     * 监控任务执行器配置
     * 专门用于执行监控相关的异步任务
     */
    @Bean("monitorTaskExecutor")
    public Executor monitorTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 基础配置
        executor.setCorePoolSize(2);           // 核心线程数
        executor.setMaxPoolSize(4);            // 最大线程数
        executor.setQueueCapacity(100);        // 队列容量
        executor.setKeepAliveSeconds(60);      // 线程空闲时间
        
        // 线程命名
        executor.setThreadNamePrefix("SysMonitor-");
        
        // 拒绝策略：调用者运行策略
        executor.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        
        executor.initialize();
        return executor;
    }

    /**
     * 系统监控属性配置
     */
    @Component
    @ConfigurationProperties(prefix = "sys.monitor")
    @Data
    public static class SysMonitorProperties {
        
        /**
         * 是否启用监控功能
         */
        private boolean enabled = true;
        
        /**
         * 实时数据采集间隔(毫秒)
         */
        private long realtimeCollectInterval = 5000;
        
        /**
         * 历史数据保存间隔(毫秒)
         */
        private long historyCollectInterval = 300000;
        
        /**
         * 系统信息更新间隔(毫秒)
         */
        private long systemInfoUpdateInterval = 3600000;
        
        /**
         * 数据采集超时时间(毫秒)
         */
        private long collectTimeoutMs = 3000;
        
        /**
         * 最大并发采集数
         */
        private int maxConcurrentCollections = 3;
        
        /**
         * 历史数据保留天数
         */
        private int historyRetentionDays = 30;
        
        /**
         * 是否启用数据压缩
         */
        private boolean enableDataCompression = false;
        
        /**
         * 是否启用告警功能
         */
        private boolean enableAlert = true;
        
        /**
         * 告警检查间隔(毫秒)
         */
        private long alertCheckInterval = 30000;
        
        /**
         * 单次查询最大数据点数
         */
        private int maxQueryDataPoints = 1000;
        
        /**
         * 是否启用性能统计
         */
        private boolean enablePerformanceStats = true;
        
        /**
         * JMX连接超时时间(毫秒)
         */
        private long jmxConnectionTimeout = 5000;
        
        /**
         * 错误重试次数
         */
        private int errorRetryTimes = 3;
        
        /**
         * 错误重试间隔(毫秒)
         */
        private long errorRetryInterval = 1000;
        
        /**
         * 是否启用调试日志
         */
        private boolean enableDebugLog = false;
    }
}