package com.rickpan.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 系统监控告警配置实体类
 * 对应数据库表：sys_monitor_alert_config
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "sys_monitor_alert_config", 
       uniqueConstraints = {
           @UniqueConstraint(name = "uk_sys_monitor_alert_metric", 
                           columnNames = {"metric_type", "metric_name"})
       },
       indexes = {
           @Index(name = "idx_sys_monitor_alert_config_enabled", columnList = "enabled"),
           @Index(name = "idx_sys_monitor_alert_config_metric_type", columnList = "metric_type")
       })
@EntityListeners(AuditingEntityListener.class)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysMonitorAlertConfig {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 指标类型(CPU/MEMORY/DISK/JVM/THREAD)
     */
    @Column(name = "metric_type", length = 50, nullable = false)
    private String metricType;

    /**
     * 指标名称(cpu_usage/memory_usage等)
     */
    @Column(name = "metric_name", length = 100, nullable = false)
    private String metricName;

    /**
     * 指标显示名称
     */
    @Column(name = "metric_display_name", length = 100)
    private String metricDisplayName;

    // ==================== 阈值配置 ====================
    
    /**
     * 警告阈值
     */
    @Column(name = "warning_threshold", precision = 8, scale = 2)
    private BigDecimal warningThreshold;

    /**
     * 严重阈值
     */
    @Column(name = "critical_threshold", precision = 8, scale = 2)
    private BigDecimal criticalThreshold;

    /**
     * 紧急阈值
     */
    @Column(name = "emergency_threshold", precision = 8, scale = 2)
    private BigDecimal emergencyThreshold;

    /**
     * 比较操作符：GT(大于)/LT(小于)/EQ(等于)/GTE(大于等于)/LTE(小于等于)
     */
    @Column(name = "comparison_operator", length = 10)
    @Builder.Default
    private String comparisonOperator = "GT";

    // ==================== 单位和描述 ====================
    
    /**
     * 单位(%/MB/GB/count等)
     */
    @Column(name = "unit", length = 20)
    private String unit;

    /**
     * 描述信息
     */
    @Column(name = "description", length = 500)
    private String description;

    // ==================== 状态控制 ====================
    
    /**
     * 是否启用(0:禁用,1:启用)
     */
    @Column(name = "enabled", nullable = false)
    @Builder.Default
    private Boolean enabled = true;

    /**
     * 是否启用通知(0:禁用,1:启用)
     */
    @Column(name = "notification_enabled", nullable = false)
    @Builder.Default
    private Boolean notificationEnabled = true;

    // ==================== 时间字段 ====================
    
    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // ==================== 枚举定义 ====================
    
    /**
     * 指标类型枚举
     */
    public enum MetricType {
        CPU("CPU", "CPU相关指标"),
        MEMORY("MEMORY", "内存相关指标"),
        DISK("DISK", "磁盘相关指标"),
        JVM("JVM", "JVM相关指标"),
        THREAD("THREAD", "线程相关指标");

        private final String code;
        private final String description;

        MetricType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() { return code; }
        public String getDescription() { return description; }
    }

    /**
     * 比较操作符枚举
     */
    public enum ComparisonOperator {
        GT("GT", "大于", ">"),
        LT("LT", "小于", "<"),
        EQ("EQ", "等于", "="),
        GTE("GTE", "大于等于", ">="),
        LTE("LTE", "小于等于", "<=");

        private final String code;
        private final String description;
        private final String symbol;

        ComparisonOperator(String code, String description, String symbol) {
            this.code = code;
            this.description = description;
            this.symbol = symbol;
        }

        public String getCode() { return code; }
        public String getDescription() { return description; }
        public String getSymbol() { return symbol; }
    }
}