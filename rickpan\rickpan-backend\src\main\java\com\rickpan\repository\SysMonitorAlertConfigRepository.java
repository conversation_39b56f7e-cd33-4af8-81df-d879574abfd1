package com.rickpan.repository;

import com.rickpan.entity.SysMonitorAlertConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 系统监控告警配置数据访问层
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface SysMonitorAlertConfigRepository extends JpaRepository<SysMonitorAlertConfig, Long> {

    /**
     * 根据指标类型和指标名称查找配置
     * 
     * @param metricType 指标类型
     * @param metricName 指标名称
     * @return 告警配置
     */
    Optional<SysMonitorAlertConfig> findByMetricTypeAndMetricName(String metricType, String metricName);

    /**
     * 根据指标类型查找所有配置
     * 
     * @param metricType 指标类型
     * @return 告警配置列表
     */
    List<SysMonitorAlertConfig> findByMetricType(String metricType);

    /**
     * 查找所有启用的告警配置
     * 
     * @return 启用的告警配置列表
     */
    List<SysMonitorAlertConfig> findByEnabledTrue();

    /**
     * 根据指标类型查找启用的告警配置
     * 
     * @param metricType 指标类型
     * @return 启用的告警配置列表
     */
    List<SysMonitorAlertConfig> findByMetricTypeAndEnabledTrue(String metricType);

    /**
     * 查找所有启用通知的告警配置
     * 
     * @return 启用通知的告警配置列表
     */
    List<SysMonitorAlertConfig> findByNotificationEnabledTrue();

    /**
     * 根据指标类型统计配置数量
     * 
     * @param metricType 指标类型
     * @return 配置数量
     */
    Long countByMetricType(String metricType);

    /**
     * 统计启用的配置数量
     * 
     * @return 启用的配置数量
     */
    Long countByEnabledTrue();

    /**
     * 检查指标是否已存在配置
     * 
     * @param metricType 指标类型
     * @param metricName 指标名称
     * @return 是否存在
     */
    boolean existsByMetricTypeAndMetricName(String metricType, String metricName);

    /**
     * 查询所有配置按指标类型分组统计
     * 
     * @return 统计结果
     */
    @Query("SELECT c.metricType, COUNT(c), SUM(CASE WHEN c.enabled = true THEN 1 ELSE 0 END) " +
           "FROM SysMonitorAlertConfig c " +
           "GROUP BY c.metricType " +
           "ORDER BY c.metricType")
    List<Object[]> getConfigStatsByMetricType();

    /**
     * 查找需要监控的所有指标配置（启用且有阈值设置）
     * 
     * @return 需要监控的配置列表
     */
    @Query("SELECT c FROM SysMonitorAlertConfig c " +
           "WHERE c.enabled = true " +
           "AND (c.warningThreshold IS NOT NULL OR c.criticalThreshold IS NOT NULL OR c.emergencyThreshold IS NOT NULL) " +
           "ORDER BY c.metricType, c.metricName")
    List<SysMonitorAlertConfig> findMonitorableConfigs();

    /**
     * 根据指标类型和比较操作符查找配置
     * 
     * @param metricType         指标类型
     * @param comparisonOperator 比较操作符
     * @return 配置列表
     */
    List<SysMonitorAlertConfig> findByMetricTypeAndComparisonOperator(String metricType, String comparisonOperator);
}