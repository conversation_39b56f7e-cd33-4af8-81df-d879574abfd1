-- =====================================================
-- SysMonitor 系统监控模块测试数据生成脚本
-- 版本: v1.0
-- 创建时间: 2025-01-15
-- 描述: 生成测试和演示用的模拟监控数据
-- 注意: 此脚本仅用于开发测试，生产环境请勿执行
-- =====================================================

-- 使用现有数据库
USE rick_pan;

-- =====================================================
-- 1. 生成历史监控数据（最近24小时）
-- =====================================================

-- 清空现有测试数据
DELETE FROM sys_monitor_history WHERE record_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR);

-- 创建存储过程生成测试数据
DELIMITER //

DROP PROCEDURE IF EXISTS sp_generate_test_monitor_data//
CREATE PROCEDURE sp_generate_test_monitor_data()
BEGIN
    DECLARE i INT DEFAULT 0;
    DECLARE test_time TIMESTAMP;
    DECLARE cpu_base DECIMAL(5,2) DEFAULT 30.0;
    DECLARE memory_base DECIMAL(5,2) DEFAULT 50.0;
    DECLARE disk_base DECIMAL(5,2) DEFAULT 60.0;
    DECLARE jvm_base DECIMAL(5,2) DEFAULT 40.0;
    
    -- 生成最近24小时的数据，每5分钟一个数据点 (288个数据点)
    WHILE i < 288 DO
        -- 计算时间点（从24小时前开始，每次增加5分钟）
        SET test_time = DATE_SUB(NOW(), INTERVAL (288 - i) * 5 MINUTE);
        
        -- 生成模拟的监控数据（添加随机波动）
        -- CPU使用率：基础值30% ± 15%的随机波动
        SET cpu_base = 30.0 + (RAND() - 0.5) * 30.0;
        IF cpu_base < 5.0 THEN SET cpu_base = 5.0; END IF;
        IF cpu_base > 95.0 THEN SET cpu_base = 95.0; END IF;
        
        -- 内存使用率：基础值50% ± 20%的随机波动
        SET memory_base = 50.0 + (RAND() - 0.5) * 40.0;
        IF memory_base < 10.0 THEN SET memory_base = 10.0; END IF;
        IF memory_base > 90.0 THEN SET memory_base = 90.0; END IF;
        
        -- 磁盘使用率：基础值60% ± 10%的缓慢变化
        SET disk_base = 60.0 + (RAND() - 0.5) * 20.0;
        IF disk_base < 45.0 THEN SET disk_base = 45.0; END IF;
        IF disk_base > 80.0 THEN SET disk_base = 80.0; END IF;
        
        -- JVM使用率：基础值40% ± 25%的随机波动
        SET jvm_base = 40.0 + (RAND() - 0.5) * 50.0;
        IF jvm_base < 10.0 THEN SET jvm_base = 10.0; END IF;
        IF jvm_base > 85.0 THEN SET jvm_base = 85.0; END IF;
        
        -- 插入历史监控数据
        INSERT INTO sys_monitor_history (
            record_time,
            cpu_usage, memory_usage, disk_usage, jvm_heap_usage,
            memory_total, memory_used,
            disk_total, disk_used,
            jvm_heap_used, jvm_heap_max,
            gc_count, gc_time,
            thread_count, cpu_cores
        ) VALUES (
            test_time,
            cpu_base, memory_base, disk_base, jvm_base,
            -- 内存相关（16GB总内存）
            17179869184, 
            ROUND(17179869184 * memory_base / 100),
            -- 磁盘相关（1TB总磁盘）
            1099511627776,
            ROUND(1099511627776 * disk_base / 100),
            -- JVM相关（2GB最大堆内存）
            ROUND(2147483648 * jvm_base / 100),
            2147483648,
            -- GC相关（随机数据）
            ROUND(10 + RAND() * 50),
            ROUND(100 + RAND() * 500),
            -- 线程和CPU
            ROUND(30 + RAND() * 40),
            8
        );
        
        SET i = i + 1;
    END WHILE;
END//

DELIMITER ;

-- 执行存储过程生成测试数据
CALL sp_generate_test_monitor_data();

-- 删除临时存储过程
DROP PROCEDURE sp_generate_test_monitor_data;

-- =====================================================
-- 2. 生成告警日志测试数据
-- =====================================================

-- 清空现有告警日志测试数据
DELETE FROM sys_monitor_alert_log WHERE alert_message LIKE '%测试%';

-- 插入各种类型的告警日志
INSERT INTO sys_monitor_alert_log (
    alert_config_id, alert_type, metric_type, metric_name,
    alert_level, alert_status, alert_title, alert_message,
    metric_value, threshold_value, unit, alert_time,
    first_alert_time, occurrence_count
) VALUES 
-- CPU告警
(
    (SELECT id FROM sys_monitor_alert_config WHERE metric_name = 'cpu_usage' LIMIT 1),
    'THRESHOLD_EXCEEDED', 'CPU', 'cpu_usage',
    'WARNING', 'RESOLVED', 'CPU使用率告警', 
    'CPU使用率达到75.5%，超过警告阈值70%，请关注系统负载。（测试数据）',
    75.50, 70.00, '%', DATE_SUB(NOW(), INTERVAL 2 HOUR),
    DATE_SUB(NOW(), INTERVAL 2 HOUR), 1
),
-- 内存告警
(
    (SELECT id FROM sys_monitor_alert_config WHERE metric_name = 'memory_usage' LIMIT 1),
    'THRESHOLD_EXCEEDED', 'MEMORY', 'memory_usage', 
    'CRITICAL', 'ACTIVE', '内存使用率严重告警',
    '系统内存使用率达到92.3%，超过严重阈值90%，请立即检查内存使用情况。（测试数据）',
    92.30, 90.00, '%', DATE_SUB(NOW(), INTERVAL 30 MINUTE),
    DATE_SUB(NOW(), INTERVAL 30 MINUTE), 1
),
-- 磁盘告警
(
    (SELECT id FROM sys_monitor_alert_config WHERE metric_name = 'disk_usage' LIMIT 1),
    'THRESHOLD_EXCEEDED', 'DISK', 'disk_usage',
    'WARNING', 'ACTIVE', '磁盘空间告警',
    '磁盘使用率达到87.2%，超过警告阈值85%，建议清理磁盘空间。（测试数据）',
    87.20, 85.00, '%', DATE_SUB(NOW(), INTERVAL 15 MINUTE),
    DATE_SUB(NOW(), INTERVAL 15 MINUTE), 1
),
-- JVM告警
(
    (SELECT id FROM sys_monitor_alert_config WHERE metric_name = 'jvm_heap_usage' LIMIT 1),
    'THRESHOLD_EXCEEDED', 'JVM', 'jvm_heap_usage',
    'WARNING', 'RESOLVED', 'JVM堆内存告警',
    'JVM堆内存使用率达到78.5%，超过警告阈值75%，建议检查内存泄漏。（测试数据）',
    78.50, 75.00, '%', DATE_SUB(NOW(), INTERVAL 1 HOUR),
    DATE_SUB(NOW(), INTERVAL 1 HOUR), 1
),
-- 线程告警
(
    (SELECT id FROM sys_monitor_alert_config WHERE metric_name = 'thread_count' LIMIT 1),
    'THRESHOLD_EXCEEDED', 'THREAD', 'thread_count',
    'WARNING', 'ACTIVE', '线程数量告警',
    '活跃线程数达到225，超过警告阈值200，请检查线程池配置。（测试数据）',
    225.00, 200.00, '个', DATE_SUB(NOW(), INTERVAL 10 MINUTE),
    DATE_SUB(NOW(), INTERVAL 10 MINUTE), 1
),
-- 系统维护信息
(
    NULL, 'SYSTEM_MAINTENANCE', 'SYSTEM', 'data_generation',
    'INFO', 'RESOLVED', '测试数据生成完成',
    '系统监控测试数据生成完成，包含24小时历史数据和告警日志。',
    1.00, 1.00, 'status', NOW(),
    NOW(), 1
);

-- =====================================================
-- 3. 更新实时监控数据
-- =====================================================

-- 清空现有实时数据
DELETE FROM sys_monitor_realtime;

-- 插入当前的实时监控数据（模拟当前状态）
INSERT INTO sys_monitor_realtime (
    cpu_usage, cpu_cores, cpu_load_1min, cpu_load_5min, cpu_load_15min,
    memory_total, memory_used, memory_free, memory_usage,
    disk_total, disk_used, disk_available, disk_usage,
    jvm_heap_used, jvm_heap_max, jvm_heap_committed, jvm_heap_usage,
    jvm_non_heap_used, jvm_non_heap_max, jvm_non_heap_committed,
    gc_count, gc_time, gc_avg_time, gc_max_time,
    thread_count, thread_peak_count, thread_daemon_count, thread_total_started,
    system_uptime, jvm_uptime
) VALUES (
    -- CPU信息：45.5%使用率，8核，适中负载
    45.50, 8, 1.2, 1.5, 1.8,
    -- 系统内存：16GB总，8GB已用，50%使用率  
    17179869184, 8589934592, 8589934592, 50.00,
    -- 磁盘：1TB总，600GB已用，60%使用率
    1099511627776, 659756548096, 439755079680, 60.00,
    -- JVM堆内存：2GB最大，800MB已用，40%使用率
    838860800, 2147483648, 1073741824, 40.00,
    -- JVM非堆内存：256MB最大，128MB已用
    134217728, 268435456, 201326592,
    -- GC信息：35次，总耗时350ms，平均10ms，最大50ms
    35, 350, 10.00, 50,
    -- 线程信息：65活跃，80峰值，25守护，500总启动
    65, 80, 25, 500,
    -- 运行时间：系统和JVM都运行了6小时（21600秒）
    21600000, 21600000
);

-- =====================================================
-- 4. 更新系统基础信息
-- =====================================================

-- 更新系统基础信息为模拟的真实数据
UPDATE sys_monitor_system_info SET
    os_name = 'Windows 11',
    os_version = '10.0.22000',
    os_arch = 'amd64',
    java_version = '17.0.2',
    java_vendor = 'Oracle Corporation',
    java_home = 'C:\\Program Files\\Java\\jdk-17.0.2',
    jvm_name = 'Java HotSpot(TM) 64-Bit Server VM',
    jvm_version = '17.0.2+8-LTS-86',
    app_name = 'RickPan',
    app_version = '2.1.0',
    app_start_time = DATE_SUB(NOW(), INTERVAL 6 HOUR),
    processor_count = 8,
    total_physical_memory = 17179869184,  -- 16GB
    total_disk_space = 1099511627776,     -- 1TB
    updated_at = NOW();

-- =====================================================
-- 5. 生成更多历史数据（可选，用于压力测试）
-- =====================================================

-- 如果需要更多历史数据用于测试，可以取消注释以下代码

/*
-- 生成过去7天的数据（每小时一个数据点）
INSERT INTO sys_monitor_history (record_time, cpu_usage, memory_usage, disk_usage, jvm_heap_usage, 
    memory_total, memory_used, disk_total, disk_used, jvm_heap_used, jvm_heap_max, 
    gc_count, gc_time, thread_count, cpu_cores)
SELECT 
    DATE_SUB(NOW(), INTERVAL seq HOUR) as record_time,
    30 + (RAND() - 0.5) * 40 as cpu_usage,
    50 + (RAND() - 0.5) * 30 as memory_usage, 
    60 + (RAND() - 0.5) * 10 as disk_usage,
    40 + (RAND() - 0.5) * 30 as jvm_heap_usage,
    17179869184 as memory_total,
    ROUND(17179869184 * (50 + (RAND() - 0.5) * 30) / 100) as memory_used,
    1099511627776 as disk_total,
    ROUND(1099511627776 * (60 + (RAND() - 0.5) * 10) / 100) as disk_used,
    ROUND(2147483648 * (40 + (RAND() - 0.5) * 30) / 100) as jvm_heap_used,
    2147483648 as jvm_heap_max,
    ROUND(50 + RAND() * 100) as gc_count,
    ROUND(500 + RAND() * 1000) as gc_time,
    ROUND(50 + RAND() * 30) as thread_count,
    8 as cpu_cores
FROM (
    SELECT @row := @row + 1 as seq
    FROM (SELECT 0 UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 
          UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) t1
    CROSS JOIN (SELECT 0 UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 
                UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) t2
    CROSS JOIN (SELECT @row := 0) r
    WHERE @row < 168  -- 7天 * 24小时
) numbers;
*/

-- =====================================================
-- 6. 验证测试数据
-- =====================================================

-- 检查历史数据数量
SELECT 
    '历史监控数据统计' as item,
    COUNT(*) as total_records,
    MIN(record_time) as earliest_time,
    MAX(record_time) as latest_time,
    ROUND(AVG(cpu_usage), 2) as avg_cpu_usage,
    ROUND(AVG(memory_usage), 2) as avg_memory_usage
FROM sys_monitor_history;

-- 检查实时数据
SELECT 
    '实时监控数据检查' as item,
    cpu_usage, memory_usage, disk_usage, jvm_heap_usage,
    thread_count, created_at
FROM sys_monitor_realtime;

-- 检查告警日志
SELECT 
    '告警日志统计' as item,
    alert_level,
    alert_status,
    COUNT(*) as count
FROM sys_monitor_alert_log 
GROUP BY alert_level, alert_status
ORDER BY alert_level, alert_status;

-- 检查告警配置
SELECT 
    '告警配置统计' as item,
    metric_type,
    COUNT(*) as config_count,
    SUM(CASE WHEN enabled = 1 THEN 1 ELSE 0 END) as enabled_count
FROM sys_monitor_alert_config
GROUP BY metric_type;

-- =====================================================
-- 7. 性能测试查询
-- =====================================================

-- 测试历史数据查询性能
SELECT 
    '查询性能测试' as test_type,
    'CPU使用率趋势查询' as test_name,
    COUNT(*) as result_count,
    NOW() as test_time
FROM sys_monitor_history 
WHERE record_time >= DATE_SUB(NOW(), INTERVAL 6 HOUR)
AND cpu_usage > 40.0
ORDER BY record_time DESC
LIMIT 50;

-- 测试告警统计查询
SELECT 
    '告警统计测试' as test_type,
    metric_type,
    alert_level,
    COUNT(*) as alert_count,
    AVG(metric_value) as avg_metric_value
FROM sys_monitor_alert_log
WHERE alert_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
GROUP BY metric_type, alert_level;

-- =====================================================
-- 测试数据生成完成
-- =====================================================

SELECT 
    'SysMonitor测试数据生成完成!' as message,
    NOW() as generated_at,
    (SELECT COUNT(*) FROM sys_monitor_history) as history_records,
    (SELECT COUNT(*) FROM sys_monitor_alert_log) as alert_records,
    (SELECT COUNT(*) FROM sys_monitor_realtime) as realtime_records,
    'SUCCESS' as status;