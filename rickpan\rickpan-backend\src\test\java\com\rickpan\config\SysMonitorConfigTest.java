package com.rickpan.config;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import jakarta.annotation.Resource;
import java.util.concurrent.Executor;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 系统监控配置类单元测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest
@SpringJUnitConfig
class SysMonitorConfigTest {

    @Resource(name = "monitorTaskExecutor")
    private Executor monitorTaskExecutor;

    @Resource
    private SysMonitorConfig.SysMonitorProperties sysMonitorProperties;

    @Test
    void testMonitorTaskExecutorBean() {
        // 验证Bean是否正确注入
        assertNotNull(monitorTaskExecutor);
        
        // 验证线程池可以执行任务
        assertDoesNotThrow(() -> {
            monitorTaskExecutor.execute(() -> {
                // 简单测试任务
                System.out.println("监控任务执行器测试");
            });
        });
    }

    @Test
    void testSysMonitorProperties() {
        // 验证属性配置Bean是否正确注入
        assertNotNull(sysMonitorProperties);
        
        // 验证默认配置值
        assertTrue(sysMonitorProperties.isEnabled());
        assertEquals(5000L, sysMonitorProperties.getRealtimeCollectInterval());
        assertEquals(300000L, sysMonitorProperties.getHistoryCollectInterval());
        assertEquals(3600000L, sysMonitorProperties.getSystemInfoUpdateInterval());
        assertEquals(3000L, sysMonitorProperties.getCollectTimeoutMs());
        assertEquals(3, sysMonitorProperties.getMaxConcurrentCollections());
        assertEquals(30, sysMonitorProperties.getHistoryRetentionDays());
        assertFalse(sysMonitorProperties.isEnableDataCompression());
        assertTrue(sysMonitorProperties.isEnableAlert());
        assertEquals(30000L, sysMonitorProperties.getAlertCheckInterval());
        assertEquals(1000, sysMonitorProperties.getMaxQueryDataPoints());
        assertTrue(sysMonitorProperties.isEnablePerformanceStats());
        assertEquals(5000L, sysMonitorProperties.getJmxConnectionTimeout());
        assertEquals(3, sysMonitorProperties.getErrorRetryTimes());
        assertEquals(1000L, sysMonitorProperties.getErrorRetryInterval());
        assertFalse(sysMonitorProperties.isEnableDebugLog());
    }

    @Test
    void testPropertiesSettersAndGetters() {
        // 测试属性的设置和获取
        SysMonitorConfig.SysMonitorProperties properties = new SysMonitorConfig.SysMonitorProperties();
        
        // 测试enabled属性
        properties.setEnabled(false);
        assertFalse(properties.isEnabled());
        
        // 测试collectInterval属性
        properties.setRealtimeCollectInterval(10000L);
        assertEquals(10000L, properties.getRealtimeCollectInterval());
        
        // 测试historyRetentionDays属性
        properties.setHistoryRetentionDays(60);
        assertEquals(60, properties.getHistoryRetentionDays());
        
        // 测试enableAlert属性
        properties.setEnableAlert(false);
        assertFalse(properties.isEnableAlert());
        
        // 测试maxQueryDataPoints属性
        properties.setMaxQueryDataPoints(2000);
        assertEquals(2000, properties.getMaxQueryDataPoints());
        
        // 测试jmxConnectionTimeout属性
        properties.setJmxConnectionTimeout(8000L);
        assertEquals(8000L, properties.getJmxConnectionTimeout());
        
        // 测试errorRetryTimes属性
        properties.setErrorRetryTimes(5);
        assertEquals(5, properties.getErrorRetryTimes());
        
        // 测试enableDebugLog属性
        properties.setEnableDebugLog(true);
        assertTrue(properties.isEnableDebugLog());
    }

    @Test
    void testConfigurationDefaults() {
        // 创建新的配置实例，验证默认值
        SysMonitorConfig.SysMonitorProperties newProperties = new SysMonitorConfig.SysMonitorProperties();
        
        assertTrue(newProperties.isEnabled());
        assertEquals(5000L, newProperties.getRealtimeCollectInterval());
        assertEquals(300000L, newProperties.getHistoryCollectInterval());
        assertEquals(3600000L, newProperties.getSystemInfoUpdateInterval());
        assertEquals(3000L, newProperties.getCollectTimeoutMs());
        assertEquals(3, newProperties.getMaxConcurrentCollections());
        assertEquals(30, newProperties.getHistoryRetentionDays());
        assertFalse(newProperties.isEnableDataCompression());
        assertTrue(newProperties.isEnableAlert());
        assertEquals(30000L, newProperties.getAlertCheckInterval());
        assertEquals(1000, newProperties.getMaxQueryDataPoints());
        assertTrue(newProperties.isEnablePerformanceStats());
        assertEquals(5000L, newProperties.getJmxConnectionTimeout());
        assertEquals(3, newProperties.getErrorRetryTimes());
        assertEquals(1000L, newProperties.getErrorRetryInterval());
        assertFalse(newProperties.isEnableDebugLog());
    }
}