package com.rickpan.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 系统历史监控数据实体类
 * 对应数据库表：sys_monitor_history
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "sys_monitor_history", indexes = {
    @Index(name = "idx_sys_monitor_history_record_time", columnList = "record_time"),
    @Index(name = "idx_sys_monitor_history_cpu_usage", columnList = "record_time,cpu_usage"),
    @Index(name = "idx_sys_monitor_history_memory_usage", columnList = "record_time,memory_usage"),
    @Index(name = "idx_sys_monitor_history_disk_usage", columnList = "record_time,disk_usage")
})
@EntityListeners(AuditingEntityListener.class)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysMonitorHistory {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 记录时间
     */
    @Column(name = "record_time", nullable = false)
    private LocalDateTime recordTime;

    // ==================== 主要监控指标 ====================
    
    /**
     * CPU使用率(%)
     */
    @Column(name = "cpu_usage", precision = 5, scale = 2)
    private BigDecimal cpuUsage;

    /**
     * 系统内存使用率(%)
     */
    @Column(name = "memory_usage", precision = 5, scale = 2)
    private BigDecimal memoryUsage;

    /**
     * 磁盘使用率(%)
     */
    @Column(name = "disk_usage", precision = 5, scale = 2)
    private BigDecimal diskUsage;

    /**
     * JVM堆内存使用率(%)
     */
    @Column(name = "jvm_heap_usage", precision = 5, scale = 2)
    private BigDecimal jvmHeapUsage;

    // ==================== 详细统计信息 ====================
    
    /**
     * 系统总内存(字节)
     */
    @Column(name = "memory_total")
    private Long memoryTotal;

    /**
     * 系统已用内存(字节)
     */
    @Column(name = "memory_used")
    private Long memoryUsed;

    /**
     * 磁盘总空间(字节)
     */
    @Column(name = "disk_total")
    private Long diskTotal;

    /**
     * 磁盘已用空间(字节)
     */
    @Column(name = "disk_used")
    private Long diskUsed;

    /**
     * JVM堆内存已用(字节)
     */
    @Column(name = "jvm_heap_used")
    private Long jvmHeapUsed;

    /**
     * JVM堆内存最大(字节)
     */
    @Column(name = "jvm_heap_max")
    private Long jvmHeapMax;

    // ==================== 性能指标 ====================
    
    /**
     * GC总次数
     */
    @Column(name = "gc_count")
    private Long gcCount;

    /**
     * GC总耗时(毫秒)
     */
    @Column(name = "gc_time")
    private Long gcTime;

    /**
     * 线程数
     */
    @Column(name = "thread_count")
    private Integer threadCount;

    /**
     * CPU核心数
     */
    @Column(name = "cpu_cores")
    private Integer cpuCores;

    // ==================== 时间戳 ====================
    
    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
}