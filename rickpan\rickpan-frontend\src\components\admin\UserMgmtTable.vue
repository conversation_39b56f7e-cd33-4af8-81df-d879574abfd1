<template>
  <div class="user-mgmt-table">
    <el-table
      ref="tableRef"
      :data="userList"
      v-loading="loading"
      row-key="id"
      stripe
      border
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
      style="width: 100%"
    >
      <!-- 多选列 -->
      <el-table-column type="selection" width="50" align="center" />

      <!-- 序号列 -->
      <el-table-column type="index" label="序号" width="60" align="center" />

      <!-- 用户信息列 -->
      <el-table-column label="用户信息" width="280" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="user-info">
            <div class="user-avatar">
              <el-avatar 
                :size="40" 
                :src="row.avatarUrl" 
                :icon="UserFilled"
                :alt="row.username"
              >
                {{ row.username.charAt(0).toUpperCase() }}
              </el-avatar>
              <!-- 在线状态指示器 -->
              <div 
                v-if="row.onlineStatus" 
                class="online-indicator"
                :class="getOnlineStatusClass(row.onlineStatus.status)"
                :title="getOnlineStatusText(row.onlineStatus.status)"
              ></div>
            </div>
            <div class="user-details">
              <div class="username">{{ row.username }}</div>
              <div class="email">{{ row.email }}</div>
              <div v-if="row.realName" class="real-name">{{ row.realName }}</div>
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- 用户类型列 -->
      <el-table-column label="类型" width="100" align="center" sortable="custom" prop="userType">
        <template #default="{ row }">
          <el-tag :type="getUserTypeTagType(row.userType)" size="small">
            {{ getUserTypeText(row.userType) }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 用户状态列 -->
      <el-table-column label="状态" width="100" align="center" sortable="custom" prop="status">
        <template #default="{ row }">
          <el-tag :type="getUserStatusTagType(row.status)" size="small">
            {{ getUserStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 存储使用情况列 -->
      <el-table-column label="存储使用" width="180" align="center">
        <template #default="{ row }">
          <div class="storage-info">
            <div class="storage-text">
              {{ formatFileSize(row.storageUsed) }} / {{ formatFileSize(row.storageQuota) }}
            </div>
            <el-progress 
              :percentage="row.storageUsagePercentage" 
              :stroke-width="6"
              :show-text="false"
              :color="getStorageProgressColor(row.storageUsagePercentage)"
            />
            <div class="storage-percentage">{{ row.storageUsagePercentage }}%</div>
          </div>
        </template>
      </el-table-column>

      <!-- VIP过期时间列 -->
      <el-table-column label="VIP到期" width="120" align="center" sortable="custom" prop="vipExpireTime">
        <template #default="{ row }">
          <div v-if="row.userType === 'VIP' && row.vipExpireTime">
            <div class="vip-expire-time">{{ formatDate(row.vipExpireTime) }}</div>
            <el-tag 
              v-if="isVipExpiringSoon(row.vipExpireTime)" 
              type="warning" 
              size="small"
              effect="plain"
            >
              即将过期
            </el-tag>
          </div>
          <span v-else-if="row.userType === 'VIP'">永久VIP</span>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <!-- 最后登录时间列 -->
      <el-table-column label="最后登录" width="160" align="center" sortable="custom" prop="lastLoginAt">
        <template #default="{ row }">
          <div v-if="row.lastLoginAt">
            <div class="last-login-time">{{ formatDate(row.lastLoginAt) }}</div>
            <div class="last-login-relative">{{ getRelativeTime(row.lastLoginAt) }}</div>
          </div>
          <span v-else class="text-muted">从未登录</span>
        </template>
      </el-table-column>

      <!-- 注册时间列 -->
      <el-table-column label="注册时间" width="160" align="center" sortable="custom" prop="createdAt">
        <template #default="{ row }">
          <div class="created-time">{{ formatDate(row.createdAt) }}</div>
          <div class="created-relative">{{ getRelativeTime(row.createdAt) }}</div>
        </template>
      </el-table-column>

      <!-- 操作列 -->
      <el-table-column label="操作" width="120" align="center" fixed="right">
        <template #default="{ row }">
          <div class="table-actions">
            <el-button 
              type="primary" 
              link 
              size="small" 
              @click="handleViewUser(row)"
            >
              <el-icon><View /></el-icon>
              查看
            </el-button>
            
            <el-button 
              type="warning" 
              link 
              size="small" 
              @click="handleEditUser(row)"
            >
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <div class="table-pagination">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :total="totalUsers"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  UserFilled, 
  View, 
  Edit
} from '@element-plus/icons-vue'
import type { 
  UserMgmtUserWithStatus, 
  UserType, 
  UserStatus, 
  OnlineStatus 
} from '@/types/userMgmt'

// ========== Props & Emits ==========
interface Props {
  userList: UserMgmtUserWithStatus[]
  loading?: boolean
  totalUsers: number
  currentPage: number
  pageSize: number
}

interface Emits {
  (e: 'selection-change', selection: UserMgmtUserWithStatus[]): void
  (e: 'sort-change', sortBy: string, sortDirection: 'ASC' | 'DESC'): void
  (e: 'page-change', page: number): void
  (e: 'size-change', size: number): void
  (e: 'view-user', user: UserMgmtUserWithStatus): void
  (e: 'edit-user', user: UserMgmtUserWithStatus): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

// ========== 响应式数据 ==========
const tableRef = ref()

// ========== 工具方法 ==========

/**
 * 格式化文件大小
 */
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 格式化日期
 */
const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

/**
 * 获取相对时间
 */
const getRelativeTime = (dateString: string): string => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) return '今天'
  if (days === 1) return '昨天'
  if (days < 7) return `${days}天前`
  if (days < 30) return `${Math.floor(days / 7)}周前`
  if (days < 365) return `${Math.floor(days / 30)}个月前`
  return `${Math.floor(days / 365)}年前`
}

/**
 * 获取用户类型标签类型
 */
const getUserTypeTagType = (userType: UserType): string => {
  const typeMap = {
    'BASIC': 'info',
    'VIP': 'warning',
    'ADMIN': 'danger'
  }
  return typeMap[userType] || 'info'
}

/**
 * 获取用户类型文本
 */
const getUserTypeText = (userType: UserType): string => {
  const textMap = {
    'BASIC': '普通用户',
    'VIP': 'VIP用户',
    'ADMIN': '管理员'
  }
  return textMap[userType] || '未知'
}

/**
 * 获取用户状态标签类型
 */
const getUserStatusTagType = (status: UserStatus): string => {
  const typeMap = {
    'ACTIVE': 'success',
    'DISABLED': 'danger'
  }
  return typeMap[status] || ''
}

/**
 * 获取用户状态文本
 */
const getUserStatusText = (status: UserStatus): string => {
  const textMap = {
    'ACTIVE': '正常',
    'DISABLED': '禁用'
  }
  return textMap[status] || '未知'
}

/**
 * 获取在线状态CSS类
 */
const getOnlineStatusClass = (status: OnlineStatus): string => {
  const classMap = {
    'ONLINE': 'online',
    'OFFLINE': 'offline',
    'AWAY': 'away',
    'BUSY': 'busy'
  }
  return classMap[status] || 'offline'
}

/**
 * 获取在线状态文本
 */
const getOnlineStatusText = (status: OnlineStatus): string => {
  const textMap = {
    'ONLINE': '在线',
    'OFFLINE': '离线',
    'AWAY': '离开',
    'BUSY': '忙碌'
  }
  return textMap[status] || '未知'
}

/**
 * 获取存储进度条颜色
 */
const getStorageProgressColor = (percentage: number): string => {
  if (percentage < 50) return '#67c23a'
  if (percentage < 80) return '#e6a23c'
  return '#f56c6c'
}

/**
 * 判断VIP是否即将过期
 */
const isVipExpiringSoon = (expireTime: string): boolean => {
  const expire = new Date(expireTime)
  const now = new Date()
  const diffDays = Math.ceil((expire.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
  return diffDays <= 7 && diffDays > 0
}

// ========== 事件处理 ==========

/**
 * 处理选择变化
 */
const handleSelectionChange = (selection: UserMgmtUserWithStatus[]) => {
  emit('selection-change', selection)
}

/**
 * 处理排序变化
 */
const handleSortChange = ({ prop, order }: { prop: string; order: string | null }) => {
  if (order && prop) {
    const sortDirection = order === 'ascending' ? 'ASC' : 'DESC'
    emit('sort-change', prop, sortDirection)
  }
}

/**
 * 处理页码变化
 */
const handleCurrentChange = (page: number) => {
  emit('page-change', page)
}

/**
 * 处理页大小变化
 */
const handleSizeChange = (size: number) => {
  emit('size-change', size)
}

/**
 * 查看用户
 */
const handleViewUser = (user: UserMgmtUserWithStatus) => {
  emit('view-user', user)
}

/**
 * 编辑用户
 */
const handleEditUser = (user: UserMgmtUserWithStatus) => {
  emit('edit-user', user)
}
</script>

<style scoped lang="scss">
.user-mgmt-table {
  .user-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .user-avatar {
      position: relative;

      .online-indicator {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid white;
        
        &.online {
          background-color: #67c23a;
        }
        
        &.offline {
          background-color: #909399;
        }
        
        &.away {
          background-color: #e6a23c;
        }
        
        &.busy {
          background-color: #f56c6c;
        }
      }
    }

    .user-details {
      flex: 1;
      min-width: 0;

      .username {
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin-bottom: 2px;
      }

      .email {
        font-size: 12px;
        color: var(--el-text-color-secondary);
        margin-bottom: 2px;
      }

      .real-name {
        font-size: 12px;
        color: var(--el-text-color-regular);
      }
    }
  }

  .storage-info {
    .storage-text {
      font-size: 12px;
      color: var(--el-text-color-regular);
      margin-bottom: 4px;
    }

    .storage-percentage {
      font-size: 12px;
      color: var(--el-text-color-secondary);
      margin-top: 2px;
      text-align: center;
    }
  }

  .vip-expire-time,
  .last-login-time,
  .created-time {
    font-size: 13px;
    color: var(--el-text-color-primary);
    margin-bottom: 2px;
  }

  .last-login-relative,
  .created-relative {
    font-size: 11px;
    color: var(--el-text-color-secondary);
  }

  .text-muted {
    color: var(--el-text-color-placeholder);
    font-style: italic;
  }

  .table-actions {
    display: flex;
    justify-content: center;
    gap: 4px;
  }

  .table-pagination {
    margin-top: 16px;
    display: flex;
    justify-content: center;
  }
}

// 表格样式优化
:deep(.el-table) {
  .el-table__cell {
    padding: 12px 0;
  }

  .el-table__row:hover {
    background-color: var(--el-table-row-hover-bg-color);
  }
}
</style>