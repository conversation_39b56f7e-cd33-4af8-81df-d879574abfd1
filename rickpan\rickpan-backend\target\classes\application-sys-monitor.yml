# 系统监控模块配置
sys:
  monitor:
    # 是否启用监控功能
    enabled: true
    
    # 数据采集配置
    realtime-collect-interval: 5000      # 实时数据采集间隔(毫秒) - 5秒
    history-collect-interval: 300000     # 历史数据保存间隔(毫秒) - 5分钟
    system-info-update-interval: 3600000 # 系统信息更新间隔(毫秒) - 1小时
    
    # 超时配置
    collect-timeout-ms: 3000             # 数据采集超时时间(毫秒)
    jmx-connection-timeout: 5000         # JMX连接超时时间(毫秒)
    
    # 并发配置
    max-concurrent-collections: 3       # 最大并发采集数
    
    # 数据保留配置
    history-retention-days: 30          # 历史数据保留天数
    enable-data-compression: false      # 是否启用数据压缩
    
    # 告警配置
    enable-alert: true                  # 是否启用告警功能
    alert-check-interval: 30000         # 告警检查间隔(毫秒) - 30秒
    
    # 查询配置
    max-query-data-points: 1000         # 单次查询最大数据点数
    
    # 性能配置
    enable-performance-stats: true      # 是否启用性能统计
    
    # 错误处理配置
    error-retry-times: 3                # 错误重试次数
    error-retry-interval: 1000          # 错误重试间隔(毫秒)
    
    # 日志配置
    enable-debug-log: false             # 是否启用调试日志

---
# 开发环境特定配置
spring:
  profiles: dev
  
sys:
  monitor:
    # 开发环境更频繁的采集
    realtime-collect-interval: 3000     # 3秒采集一次
    history-collect-interval: 180000    # 3分钟保存一次历史数据
    
    # 启用调试日志
    enable-debug-log: true
    
    # 更短的数据保留期
    history-retention-days: 7

---
# 生产环境特定配置
spring:
  profiles: prod
  
sys:
  monitor:
    # 生产环境标准采集频率
    realtime-collect-interval: 5000     # 5秒采集一次
    history-collect-interval: 300000    # 5分钟保存一次历史数据
    
    # 关闭调试日志
    enable-debug-log: false
    
    # 更长的数据保留期
    history-retention-days: 90
    
    # 启用数据压缩
    enable-data-compression: true
    
    # 更多的重试次数
    error-retry-times: 5

---
# 测试环境特定配置  
spring:
  profiles: test
  
sys:
  monitor:
    # 测试环境可以禁用监控
    enabled: false
    
    # 或者使用更长的采集间隔
    realtime-collect-interval: 10000    # 10秒采集一次
    history-collect-interval: 600000    # 10分钟保存一次
    
    # 短期数据保留
    history-retention-days: 1