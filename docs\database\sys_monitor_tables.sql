-- =====================================================
-- SysMonitor 系统监控模块数据库表结构
-- 版本: v1.0
-- 创建时间: 2025-01-15
-- 描述: 系统监控功能相关的数据库表定义
-- =====================================================

-- 使用现有数据库
USE rick_pan;

-- 临时禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 删除表（按依赖关系倒序删除）
-- =====================================================
DROP TABLE IF EXISTS sys_monitor_alert_log;        -- 子表先删除
DROP TABLE IF EXISTS sys_monitor_alert_config;     -- 父表后删除
DROP TABLE IF EXISTS sys_monitor_history;
DROP TABLE IF EXISTS sys_monitor_realtime;
DROP TABLE IF EXISTS sys_monitor_system_info;

-- =====================================================
-- 1. 实时监控数据表
-- =====================================================
CREATE TABLE sys_monitor_realtime (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    
    -- CPU相关字段
    cpu_usage DECIMAL(5,2) COMMENT 'CPU使用率(%)',
    cpu_cores INT COMMENT 'CPU核心数',
    cpu_load_1min DECIMAL(5,2) COMMENT '1分钟平均负载',
    cpu_load_5min DECIMAL(5,2) COMMENT '5分钟平均负载',
    cpu_load_15min DECIMAL(5,2) COMMENT '15分钟平均负载',
    
    -- 系统内存相关字段
    memory_total BIGINT COMMENT '系统总内存(字节)',
    memory_used BIGINT COMMENT '系统已用内存(字节)',
    memory_free BIGINT COMMENT '系统空闲内存(字节)',
    memory_usage DECIMAL(5,2) COMMENT '系统内存使用率(%)',
    
    -- 磁盘相关字段
    disk_total BIGINT COMMENT '磁盘总空间(字节)',
    disk_used BIGINT COMMENT '磁盘已用空间(字节)',
    disk_available BIGINT COMMENT '磁盘可用空间(字节)',
    disk_usage DECIMAL(5,2) COMMENT '磁盘使用率(%)',
    
    -- JVM内存相关字段
    jvm_heap_used BIGINT COMMENT 'JVM堆内存已用(字节)',
    jvm_heap_max BIGINT COMMENT 'JVM堆内存最大(字节)',
    jvm_heap_committed BIGINT COMMENT 'JVM堆内存已分配(字节)',
    jvm_heap_usage DECIMAL(5,2) COMMENT 'JVM堆内存使用率(%)',
    jvm_non_heap_used BIGINT COMMENT 'JVM非堆内存已用(字节)',
    jvm_non_heap_max BIGINT COMMENT 'JVM非堆内存最大(字节)',
    jvm_non_heap_committed BIGINT COMMENT 'JVM非堆内存已分配(字节)',
    
    -- 垃圾回收相关字段
    gc_count BIGINT COMMENT 'GC总次数',
    gc_time BIGINT COMMENT 'GC总耗时(毫秒)',
    gc_avg_time DECIMAL(8,2) COMMENT 'GC平均耗时(毫秒)',
    gc_max_time BIGINT COMMENT 'GC最大耗时(毫秒)',
    
    -- 线程相关字段
    thread_count INT COMMENT '活跃线程数',
    thread_peak_count INT COMMENT '峰值线程数',
    thread_daemon_count INT COMMENT '守护线程数',
    thread_total_started BIGINT COMMENT '总启动线程数',
    
    -- 系统信息字段
    system_uptime BIGINT COMMENT '系统运行时间(毫秒)',
    jvm_uptime BIGINT COMMENT 'JVM运行时间(毫秒)',
    
    -- 时间戳字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='系统实时监控数据表';

-- 创建索引
CREATE INDEX idx_sys_monitor_realtime_created_at ON sys_monitor_realtime(created_at);
CREATE INDEX idx_sys_monitor_realtime_cpu_usage ON sys_monitor_realtime(cpu_usage);
CREATE INDEX idx_sys_monitor_realtime_memory_usage ON sys_monitor_realtime(memory_usage);

-- =====================================================
-- 2. 历史监控数据表（按时间分区）
-- =====================================================
CREATE TABLE sys_monitor_history (
    id BIGINT AUTO_INCREMENT COMMENT '主键ID',
    record_time TIMESTAMP NOT NULL COMMENT '记录时间',
    
    -- 主要监控指标
    cpu_usage DECIMAL(5,2) COMMENT 'CPU使用率(%)',
    memory_usage DECIMAL(5,2) COMMENT '系统内存使用率(%)',
    disk_usage DECIMAL(5,2) COMMENT '磁盘使用率(%)',
    jvm_heap_usage DECIMAL(5,2) COMMENT 'JVM堆内存使用率(%)',
    
    -- 详细统计信息
    memory_total BIGINT COMMENT '系统总内存(字节)',
    memory_used BIGINT COMMENT '系统已用内存(字节)',
    disk_total BIGINT COMMENT '磁盘总空间(字节)',
    disk_used BIGINT COMMENT '磁盘已用空间(字节)',
    jvm_heap_used BIGINT COMMENT 'JVM堆内存已用(字节)',
    jvm_heap_max BIGINT COMMENT 'JVM堆内存最大(字节)',
    
    -- 性能指标
    gc_count BIGINT COMMENT 'GC总次数',
    gc_time BIGINT COMMENT 'GC总耗时(毫秒)',
    thread_count INT COMMENT '线程数',
    cpu_cores INT COMMENT 'CPU核心数',
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    -- 联合主键，包含分区字段
    PRIMARY KEY (id, record_time)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='系统历史监控数据表'
PARTITION BY RANGE (UNIX_TIMESTAMP(record_time)) (
    PARTITION p_202501 VALUES LESS THAN (UNIX_TIMESTAMP('2025-02-01 00:00:00')),
    PARTITION p_202502 VALUES LESS THAN (UNIX_TIMESTAMP('2025-03-01 00:00:00')),
    PARTITION p_202503 VALUES LESS THAN (UNIX_TIMESTAMP('2025-04-01 00:00:00')),
    PARTITION p_202504 VALUES LESS THAN (UNIX_TIMESTAMP('2025-05-01 00:00:00')),
    PARTITION p_202505 VALUES LESS THAN (UNIX_TIMESTAMP('2025-06-01 00:00:00')),
    PARTITION p_202506 VALUES LESS THAN (UNIX_TIMESTAMP('2025-07-01 00:00:00')),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 创建索引（注意分区表索引必须包含分区键）
CREATE INDEX idx_sys_monitor_history_record_time ON sys_monitor_history(record_time);
CREATE INDEX idx_sys_monitor_history_cpu_usage ON sys_monitor_history(record_time, cpu_usage);
CREATE INDEX idx_sys_monitor_history_memory_usage ON sys_monitor_history(record_time, memory_usage);
CREATE INDEX idx_sys_monitor_history_disk_usage ON sys_monitor_history(record_time, disk_usage);

-- =====================================================
-- 3. 告警配置表
-- =====================================================
CREATE TABLE sys_monitor_alert_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    metric_type VARCHAR(50) NOT NULL COMMENT '指标类型(CPU/MEMORY/DISK/JVM/THREAD)',
    metric_name VARCHAR(100) NOT NULL COMMENT '指标名称(cpu_usage/memory_usage等)',
    metric_display_name VARCHAR(100) COMMENT '指标显示名称',
    
    -- 阈值配置
    warning_threshold DECIMAL(8,2) COMMENT '警告阈值',
    critical_threshold DECIMAL(8,2) COMMENT '严重阈值',
    emergency_threshold DECIMAL(8,2) COMMENT '紧急阈值',
    
    -- 比较操作符：GT(大于)/LT(小于)/EQ(等于)/GTE(大于等于)/LTE(小于等于)
    comparison_operator VARCHAR(10) DEFAULT 'GT' COMMENT '比较操作符',
    
    -- 单位和描述
    unit VARCHAR(20) COMMENT '单位(%/MB/GB/count等)',
    description VARCHAR(500) COMMENT '描述信息',
    
    -- 状态控制
    enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用(0:禁用,1:启用)',
    notification_enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用通知(0:禁用,1:启用)',
    
    -- 时间字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 唯一约束
    UNIQUE KEY uk_sys_monitor_alert_metric (metric_type, metric_name)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='系统监控告警配置表';

-- 创建索引
CREATE INDEX idx_sys_monitor_alert_config_enabled ON sys_monitor_alert_config(enabled);
CREATE INDEX idx_sys_monitor_alert_config_metric_type ON sys_monitor_alert_config(metric_type);

-- =====================================================
-- 4. 告警日志表
-- =====================================================
CREATE TABLE sys_monitor_alert_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    
    -- 告警基本信息
    alert_config_id BIGINT COMMENT '告警配置ID',
    alert_type VARCHAR(50) NOT NULL COMMENT '告警类型(THRESHOLD_EXCEEDED/SYSTEM_ERROR等)',
    metric_type VARCHAR(50) NOT NULL COMMENT '指标类型',
    metric_name VARCHAR(100) NOT NULL COMMENT '指标名称',
    
    -- 告警级别和状态
    alert_level VARCHAR(20) NOT NULL COMMENT '告警级别(WARNING/CRITICAL/EMERGENCY)',
    alert_status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '告警状态(ACTIVE/RESOLVED/IGNORED)',
    
    -- 告警内容
    alert_title VARCHAR(200) COMMENT '告警标题',
    alert_message TEXT COMMENT '告警消息详情',
    
    -- 指标数据
    metric_value DECIMAL(12,4) COMMENT '触发告警时的指标值',
    threshold_value DECIMAL(12,4) COMMENT '触发的阈值',
    unit VARCHAR(20) COMMENT '数值单位',
    
    -- 时间信息
    alert_time TIMESTAMP NOT NULL COMMENT '告警触发时间',
    first_alert_time TIMESTAMP COMMENT '首次告警时间（用于告警聚合）',
    resolved_time TIMESTAMP NULL COMMENT '告警解决时间',
    
    -- 告警处理信息
    resolved_by VARCHAR(100) COMMENT '解决人',
    resolve_note TEXT COMMENT '解决备注',
    
    -- 告警统计
    occurrence_count INT DEFAULT 1 COMMENT '告警发生次数（用于告警聚合）',
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 外键约束
    FOREIGN KEY (alert_config_id) REFERENCES sys_monitor_alert_config(id) ON DELETE CASCADE
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='系统监控告警日志表';

-- 创建索引
CREATE INDEX idx_sys_monitor_alert_log_alert_time ON sys_monitor_alert_log(alert_time);
CREATE INDEX idx_sys_monitor_alert_log_metric_type ON sys_monitor_alert_log(metric_type);
CREATE INDEX idx_sys_monitor_alert_log_alert_level ON sys_monitor_alert_log(alert_level);
CREATE INDEX idx_sys_monitor_alert_log_alert_status ON sys_monitor_alert_log(alert_status);
CREATE INDEX idx_sys_monitor_alert_log_config_id ON sys_monitor_alert_log(alert_config_id);

-- =====================================================
-- 5. 系统信息表（可选，用于缓存系统基础信息）
-- =====================================================
CREATE TABLE sys_monitor_system_info (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    
    -- 操作系统信息
    os_name VARCHAR(100) COMMENT '操作系统名称',
    os_version VARCHAR(100) COMMENT '操作系统版本',
    os_arch VARCHAR(50) COMMENT '系统架构',
    
    -- Java信息
    java_version VARCHAR(100) COMMENT 'Java版本',
    java_vendor VARCHAR(100) COMMENT 'Java厂商',
    java_home VARCHAR(500) COMMENT 'Java安装路径',
    jvm_name VARCHAR(100) COMMENT 'JVM名称',
    jvm_version VARCHAR(100) COMMENT 'JVM版本',
    
    -- 应用信息
    app_name VARCHAR(100) DEFAULT 'RickPan' COMMENT '应用名称',
    app_version VARCHAR(50) COMMENT '应用版本',
    app_start_time TIMESTAMP COMMENT '应用启动时间',
    
    -- 硬件信息
    total_physical_memory BIGINT COMMENT '物理内存总量(字节)',
    total_disk_space BIGINT COMMENT '磁盘总空间(字节)',
    processor_count INT COMMENT '处理器数量',
    
    -- 时间字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='系统基础信息表';

-- =====================================================
-- 6. 创建视图 - 系统监控概览
-- =====================================================
CREATE OR REPLACE VIEW v_sys_monitor_overview AS
SELECT 
    r.id,
    r.cpu_usage,
    r.memory_usage,
    r.disk_usage,
    r.jvm_heap_usage,
    r.gc_count,
    r.gc_time,
    r.thread_count,
    r.created_at as last_update_time,
    -- 计算系统健康状态
    CASE 
        WHEN r.cpu_usage > 90 OR r.memory_usage > 95 OR r.disk_usage > 95 THEN 'CRITICAL'
        WHEN r.cpu_usage > 70 OR r.memory_usage > 80 OR r.disk_usage > 85 THEN 'WARNING'
        ELSE 'HEALTHY'
    END as system_health_status,
    -- 计算JVM健康状态
    CASE 
        WHEN r.jvm_heap_usage > 90 THEN 'CRITICAL'
        WHEN r.jvm_heap_usage > 80 THEN 'WARNING'
        ELSE 'HEALTHY'
    END as jvm_health_status
FROM sys_monitor_realtime r
ORDER BY r.created_at DESC
LIMIT 1;

-- =====================================================
-- 7. 创建视图 - 告警统计
-- =====================================================
CREATE OR REPLACE VIEW v_sys_monitor_alert_stats AS
SELECT 
    DATE(alert_time) as alert_date,
    alert_level,
    metric_type,
    COUNT(*) as alert_count,
    COUNT(CASE WHEN alert_status = 'RESOLVED' THEN 1 END) as resolved_count,
    COUNT(CASE WHEN alert_status = 'ACTIVE' THEN 1 END) as active_count
FROM sys_monitor_alert_log 
WHERE alert_time >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY DATE(alert_time), alert_level, metric_type
ORDER BY alert_date DESC, alert_level, metric_type;

-- =====================================================
-- 8. 创建视图 - 历史数据趋势
-- =====================================================
CREATE OR REPLACE VIEW v_sys_monitor_trend_24h AS
SELECT 
    DATE_FORMAT(record_time, '%Y-%m-%d %H:00:00') as time_hour,
    ROUND(AVG(cpu_usage), 2) as avg_cpu_usage,
    ROUND(AVG(memory_usage), 2) as avg_memory_usage,
    ROUND(AVG(disk_usage), 2) as avg_disk_usage,
    ROUND(AVG(jvm_heap_usage), 2) as avg_jvm_usage,
    MAX(cpu_usage) as max_cpu_usage,
    MAX(memory_usage) as max_memory_usage,
    MAX(disk_usage) as max_disk_usage,
    MAX(jvm_heap_usage) as max_jvm_usage,
    COUNT(*) as data_points
FROM sys_monitor_history 
WHERE record_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
GROUP BY DATE_FORMAT(record_time, '%Y-%m-%d %H:00:00')
ORDER BY time_hour;

-- =====================================================
-- 9. 创建存储过程 - 数据清理
-- =====================================================
DROP PROCEDURE IF EXISTS sp_sys_monitor_cleanup;

CREATE PROCEDURE sp_sys_monitor_cleanup(
    IN retention_days INT
)
BEGIN
    DECLARE cleanup_date TIMESTAMP;
    DECLARE deleted_rows INT DEFAULT 0;
    
    -- 设置默认值
    IF retention_days IS NULL OR retention_days <= 0 THEN
        SET retention_days = 30;
    END IF;
    
    -- 计算清理时间点
    SET cleanup_date = DATE_SUB(NOW(), INTERVAL retention_days DAY);
    
    -- 开始事务
    START TRANSACTION;
    
    -- 清理历史数据
    DELETE FROM sys_monitor_history 
    WHERE record_time < cleanup_date;
    
    SET deleted_rows = ROW_COUNT();
    
    -- 清理已解决的告警日志（保留90天）
    DELETE FROM sys_monitor_alert_log 
    WHERE alert_status = 'RESOLVED' 
    AND resolved_time < DATE_SUB(NOW(), INTERVAL 90 DAY);
    
    -- 优化表
    OPTIMIZE TABLE sys_monitor_history;
    OPTIMIZE TABLE sys_monitor_alert_log;
    
    -- 提交事务
    COMMIT;
    
    -- 返回清理结果
    SELECT 
        retention_days as retention_days,
        cleanup_date as cleanup_before_date,
        deleted_rows as deleted_history_rows,
        'SUCCESS' as status;
        
END;

-- =====================================================
-- 10. 创建事件 - 自动数据清理
-- =====================================================
-- 删除已存在的事件
DROP EVENT IF EXISTS evt_sys_monitor_auto_cleanup;

-- 创建自动清理事件（每天凌晨2点执行）
CREATE EVENT evt_sys_monitor_auto_cleanup
ON SCHEDULE EVERY 1 DAY
STARTS TIMESTAMP(CURRENT_DATE + INTERVAL 1 DAY, '02:00:00')
COMMENT '系统监控数据自动清理'
DO
BEGIN
    -- 调用清理存储过程，保留30天数据
    CALL sp_sys_monitor_cleanup(30);
    
    -- 记录清理日志
    INSERT INTO sys_monitor_alert_log (
        alert_type, metric_type, metric_name, alert_level, 
        alert_title, alert_message, alert_time
    ) VALUES (
        'SYSTEM_MAINTENANCE', 'SYSTEM', 'data_cleanup', 'INFO',
        '数据清理完成', '系统监控历史数据清理完成', NOW()
    );
END;

-- 确保事件调度器已启用
SET GLOBAL event_scheduler = ON;

-- =====================================================
-- 脚本执行完成
-- =====================================================
-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 完成提示信息
SELECT 'SysMonitor数据库表结构创建完成!' as message,
       NOW() as created_at,
       DATABASE() as database_name;