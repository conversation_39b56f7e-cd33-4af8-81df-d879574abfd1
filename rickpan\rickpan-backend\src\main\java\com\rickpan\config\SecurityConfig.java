package com.rickpan.config;

import com.rickpan.security.JwtAuthenticationFilter;
import com.rickpan.security.UserDetailsServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;
import java.util.List;

/**
 * Spring Security 配置
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
public class SecurityConfig {

    @Autowired
    private UserDetailsServiceImpl userDetailsService;

    @Autowired
    private JwtAuthenticationFilter jwtAuthenticationFilter;

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public DaoAuthenticationProvider authenticationProvider() {
        DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
        authProvider.setUserDetailsService(userDetailsService);
        authProvider.setPasswordEncoder(passwordEncoder());
        return authProvider;
    }

    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.cors(cors -> cors.configurationSource(corsConfigurationSource()))
            .csrf(AbstractHttpConfigurer::disable)
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(authz -> authz
                // 公开接口
                .requestMatchers("/api/auth/**").permitAll()
                .requestMatchers("/api/captcha/**").permitAll()  // 验证码接口无需认证
                .requestMatchers("/api/public/**").permitAll()
                // 测试接口
                .requestMatchers("/api/test/**").permitAll()
                // 分享访问接口 - 无需登录认证
                .requestMatchers("/api/shares/*/info").permitAll()
                .requestMatchers("/api/shares/*/access").permitAll()
                .requestMatchers("/api/shares/*/download").permitAll()
                .requestMatchers("/api/shares/*/preview").permitAll()
                // AI聊天SSE流式传输接口 - 豁免Spring Security权限验证
                .requestMatchers("/api/ai-chat/send").permitAll()
                .requestMatchers("/api/ai-chat/web-search").permitAll()
                // 临时允许文件上传接口（开发阶段）
                .requestMatchers("/api/files/upload/**").permitAll()
                .requestMatchers("/api/files/**").permitAll()
                .requestMatchers("/api/preview/**").permitAll()
                // Swagger文档
                .requestMatchers("/swagger-ui/**", "/swagger-ui.html", "/v3/api-docs/**", "/swagger-resources/**", "/webjars/**").permitAll()
                // 支付相关接口 - 允许匿名访问
                .requestMatchers("/api/payment/alipay/return").permitAll()
                .requestMatchers("/api/payment/alipay/notify").permitAll()
                .requestMatchers("/api/payment/mock/return").permitAll()
                .requestMatchers("/api/vip/callback/payment").permitAll()
                // 健康检查
                .requestMatchers("/actuator/health").permitAll()
                // 系统监控API - 管理员权限
                .requestMatchers("/api/sys-monitor/health").permitAll()
                .requestMatchers("/api/sys-monitor/realtime").hasAnyRole("ADMIN", "MONITOR")
                .requestMatchers("/api/sys-monitor/history/**").hasAnyRole("ADMIN", "MONITOR")
                .requestMatchers("/api/sys-monitor/statistics").hasAnyRole("ADMIN", "MONITOR")
                .requestMatchers("/api/sys-monitor/collector/status").hasAnyRole("ADMIN", "MONITOR")
                .requestMatchers("/api/sys-monitor/collector/enable").hasRole("ADMIN")
                .requestMatchers("/api/sys-monitor/collector/disable").hasRole("ADMIN")
                .requestMatchers("/api/sys-monitor/collector/manual").hasRole("ADMIN")
                .requestMatchers("/api/sys-monitor/collector/reset-stats").hasRole("ADMIN")
                // 静态资源
                .requestMatchers("/static/**", "/public/**").permitAll()
                // 其他接口需要认证
                .anyRequest().authenticated()
            );

        http.authenticationProvider(authenticationProvider());
        http.addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        // 明确指定允许的源，包括HTTPS和HTTP协议
        configuration.setAllowedOriginPatterns(Arrays.asList(
            "http://localhost:5173",
            "https://localhost:5173",
            "http://127.0.0.1:5173", 
            "https://127.0.0.1:5173",
            "http://localhost:3000",
            "https://localhost:3000",
            "http://127.0.0.1:3000",
            "https://127.0.0.1:3000"
        ));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setAllowCredentials(true);
        configuration.setMaxAge(3600L);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
}
