package com.rickpan.service;

import com.rickpan.dto.SysMonitorDataDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 系统监控数据采集服务单元测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class SysMonitorCollectorServiceTest {

    @Mock
    private SysMonitorService sysMonitorService;

    @InjectMocks
    private SysMonitorCollectorService collectorService;

    private SysMonitorDataDTO mockMonitorData;

    @BeforeEach
    void setUp() {
        mockMonitorData = createMockMonitorData();
        
        // 设置enabled状态为true
        ReflectionTestUtils.setField(collectorService, "enabled", 
                new java.util.concurrent.atomic.AtomicBoolean(true));
    }

    @Test
    void testCollectRealtimeData_Success() {
        // 模拟SysMonitorService行为
        when(sysMonitorService.getRealtimeData()).thenReturn(mockMonitorData);
        doNothing().when(sysMonitorService).saveRealtimeData(any(SysMonitorDataDTO.class));

        // 执行测试
        assertDoesNotThrow(() -> collectorService.collectRealtimeData());

        // 验证方法调用
        verify(sysMonitorService).getRealtimeData();
        verify(sysMonitorService).saveRealtimeData(mockMonitorData);
    }

    @Test
    void testCollectRealtimeData_ServiceException() {
        // 模拟SysMonitorService抛出异常
        when(sysMonitorService.getRealtimeData())
                .thenThrow(new RuntimeException("JMX连接失败"));

        // 执行测试 - 不应该抛出异常（异常被内部处理）
        assertDoesNotThrow(() -> collectorService.collectRealtimeData());

        // 验证方法调用
        verify(sysMonitorService).getRealtimeData();
        verify(sysMonitorService, never()).saveRealtimeData(any());
    }

    @Test
    void testCollectRealtimeData_WhenDisabled() {
        // 禁用采集
        collectorService.disableCollection();

        // 执行测试
        collectorService.collectRealtimeData();

        // 验证没有调用任何服务方法
        verify(sysMonitorService, never()).getRealtimeData();
        verify(sysMonitorService, never()).saveRealtimeData(any());
    }

    @Test
    void testCollectRealtimeData_WhenCollecting() {
        // 设置isCollecting状态为true
        ReflectionTestUtils.setField(collectorService, "isCollecting", 
                new java.util.concurrent.atomic.AtomicBoolean(true));

        // 执行测试
        collectorService.collectRealtimeData();

        // 验证没有调用任何服务方法
        verify(sysMonitorService, never()).getRealtimeData();
        verify(sysMonitorService, never()).saveRealtimeData(any());
    }

    @Test
    void testSaveHistoryData_Success() {
        // 模拟SysMonitorService行为
        when(sysMonitorService.getRealtimeData()).thenReturn(mockMonitorData);
        doNothing().when(sysMonitorService).saveHistoryData(any(SysMonitorDataDTO.class));

        // 执行测试
        assertDoesNotThrow(() -> collectorService.saveHistoryData());

        // 验证方法调用
        verify(sysMonitorService).getRealtimeData();
        verify(sysMonitorService).saveHistoryData(mockMonitorData);
    }

    @Test
    void testSaveHistoryData_ServiceException() {
        // 模拟SysMonitorService抛出异常
        when(sysMonitorService.getRealtimeData())
                .thenThrow(new RuntimeException("数据库连接失败"));

        // 执行测试 - 不应该抛出异常（异常被内部处理）
        assertDoesNotThrow(() -> collectorService.saveHistoryData());

        // 验证方法调用
        verify(sysMonitorService).getRealtimeData();
        verify(sysMonitorService, never()).saveHistoryData(any());
    }

    @Test
    void testUpdateSystemInfo_Success() {
        // 模拟SysMonitorService行为
        when(sysMonitorService.getRealtimeData()).thenReturn(mockMonitorData);
        doNothing().when(sysMonitorService).updateSystemInfo(any(SysMonitorDataDTO.class));

        // 执行测试
        assertDoesNotThrow(() -> collectorService.updateSystemInfo());

        // 验证方法调用
        verify(sysMonitorService).getRealtimeData();
        verify(sysMonitorService).updateSystemInfo(mockMonitorData);
    }

    @Test
    void testUpdateSystemInfo_ServiceException() {
        // 模拟SysMonitorService抛出异常
        when(sysMonitorService.getRealtimeData()).thenReturn(mockMonitorData);
        doThrow(new RuntimeException("系统信息更新失败"))
                .when(sysMonitorService).updateSystemInfo(any(SysMonitorDataDTO.class));

        // 执行测试 - 不应该抛出异常（异常被内部处理）
        assertDoesNotThrow(() -> collectorService.updateSystemInfo());

        // 验证方法调用
        verify(sysMonitorService).getRealtimeData();
        verify(sysMonitorService).updateSystemInfo(mockMonitorData);
    }

    @Test
    void testManualCollect_Success() {
        // 模拟SysMonitorService行为
        when(sysMonitorService.getRealtimeData()).thenReturn(mockMonitorData);
        doNothing().when(sysMonitorService).saveRealtimeData(any(SysMonitorDataDTO.class));

        // 执行测试
        SysMonitorDataDTO result = collectorService.manualCollect();

        // 验证结果
        assertNotNull(result);
        assertEquals(mockMonitorData, result);

        // 验证方法调用
        verify(sysMonitorService).getRealtimeData();
        verify(sysMonitorService).saveRealtimeData(mockMonitorData);
    }

    @Test
    void testManualCollect_ServiceException() {
        // 模拟SysMonitorService抛出异常
        when(sysMonitorService.getRealtimeData())
                .thenThrow(new RuntimeException("手动采集失败"));

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, 
                () -> collectorService.manualCollect());
        
        assertTrue(exception.getMessage().contains("监控数据采集失败"));

        // 验证方法调用
        verify(sysMonitorService).getRealtimeData();
        verify(sysMonitorService, never()).saveRealtimeData(any());
    }

    @Test
    void testEnableDisableCollection() {
        // 测试禁用
        collectorService.disableCollection();
        assertFalse(collectorService.isEnabled());

        // 测试启用
        collectorService.enableCollection();
        assertTrue(collectorService.isEnabled());
    }

    @Test
    void testGetStatistics() {
        // 执行测试
        SysMonitorCollectorService.CollectionStatistics stats = collectorService.getStatistics();

        // 验证结果
        assertNotNull(stats);
        assertTrue(stats.getTotalCollections() >= 0);
        assertTrue(stats.getSuccessCollections() >= 0);
        assertTrue(stats.getErrorCollections() >= 0);
        assertTrue(stats.getSuccessRate() >= 0 && stats.getSuccessRate() <= 100);
    }

    @Test
    void testResetStatistics() {
        // 先进行一次手动采集（增加统计数据）
        when(sysMonitorService.getRealtimeData()).thenReturn(mockMonitorData);
        doNothing().when(sysMonitorService).saveRealtimeData(any(SysMonitorDataDTO.class));
        collectorService.manualCollect();

        // 获取重置前的统计
        SysMonitorCollectorService.CollectionStatistics beforeReset = collectorService.getStatistics();
        assertTrue(beforeReset.getTotalCollections() > 0);

        // 重置统计
        collectorService.resetStatistics();

        // 获取重置后的统计
        SysMonitorCollectorService.CollectionStatistics afterReset = collectorService.getStatistics();
        assertEquals(0, afterReset.getTotalCollections());
        assertEquals(0, afterReset.getSuccessCollections());
        assertEquals(0, afterReset.getErrorCollections());
    }

    @Test
    void testHealthCheck() {
        // 执行健康检查 - 不应该抛出异常
        assertDoesNotThrow(() -> collectorService.healthCheck());
    }

    @Test
    void testCleanupOldData() {
        // 执行数据清理 - 不应该抛出异常
        assertDoesNotThrow(() -> collectorService.cleanupOldData());
    }

    @Test
    void testCollectionStatistics() {
        // 创建统计信息实例
        LocalDateTime now = LocalDateTime.now();
        SysMonitorCollectorService.CollectionStatistics stats = 
                new SysMonitorCollectorService.CollectionStatistics(
                        100L, 95L, 5L, now, now, true, false);

        // 验证Getter方法
        assertEquals(100L, stats.getTotalCollections());
        assertEquals(95L, stats.getSuccessCollections());
        assertEquals(5L, stats.getErrorCollections());
        assertEquals(now, stats.getLastCollectionTime());
        assertEquals(now, stats.getLastSuccessTime());
        assertTrue(stats.isEnabled());
        assertFalse(stats.isCollecting());
        assertEquals(95.0, stats.getSuccessRate(), 0.01);
    }

    @Test
    void testCollectionStatistics_ZeroCollections() {
        // 创建零采集次数的统计信息
        SysMonitorCollectorService.CollectionStatistics stats = 
                new SysMonitorCollectorService.CollectionStatistics(
                        0L, 0L, 0L, null, null, true, false);

        // 验证成功率计算
        assertEquals(0.0, stats.getSuccessRate(), 0.01);
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 创建模拟监控数据
     */
    private SysMonitorDataDTO createMockMonitorData() {
        return SysMonitorDataDTO.builder()
                .timestamp(LocalDateTime.now())
                .cpu(SysMonitorDataDTO.CpuInfo.builder()
                        .usage(BigDecimal.valueOf(65.5))
                        .cores(8)
                        .loadAverage(new BigDecimal[]{
                                BigDecimal.valueOf(1.2), 
                                BigDecimal.valueOf(1.1), 
                                BigDecimal.valueOf(1.0)
                        })
                        .processUsage(BigDecimal.valueOf(25.3))
                        .build())
                .memory(SysMonitorDataDTO.MemoryInfo.builder()
                        .total(16777216000L)
                        .used(12079595520L)
                        .free(4697620480L)
                        .available(4697620480L)
                        .usage(BigDecimal.valueOf(72.0))
                        .build())
                .disk(SysMonitorDataDTO.DiskInfo.builder()
                        .total(1000204886016L)
                        .used(550000000000L)
                        .available(450204886016L)
                        .usage(BigDecimal.valueOf(55.0))
                        .build())
                .jvm(SysMonitorDataDTO.JvmInfo.builder()
                        .heapUsed(536870912L)
                        .heapMax(1073741824L)
                        .heapCommitted(1073741824L)
                        .heapUsage(BigDecimal.valueOf(50.0))
                        .nonHeapUsed(134217728L)
                        .nonHeapMax(-1L)
                        .nonHeapCommitted(134217728L)
                        .nonHeapUsage(BigDecimal.valueOf(0.0))
                        .uptime(3600000L)
                        .build())
                .gc(SysMonitorDataDTO.GcInfo.builder()
                        .count(100L)
                        .time(5000L)
                        .avgTime(BigDecimal.valueOf(50.0))
                        .maxTime(200L)
                        .collectors(new SysMonitorDataDTO.GcCollectorInfo[0])
                        .build())
                .threads(SysMonitorDataDTO.ThreadInfo.builder()
                        .active(50)
                        .peak(75)
                        .daemon(25)
                        .totalStarted(1000L)
                        .user(25)
                        .deadlocked(0)
                        .build())
                .system(SysMonitorDataDTO.SystemInfo.builder()
                        .osName("Windows 10")
                        .osVersion("10.0")
                        .osArch("amd64")
                        .javaVersion("17.0.2")
                        .javaVendor("Eclipse Adoptium")
                        .jvmName("OpenJDK 64-Bit Server VM")
                        .jvmVersion("17.0.2+8")
                        .appName("RickPan")
                        .appVersion("2.1.0")
                        .appStartTime(LocalDateTime.now().minusHours(1))
                        .systemUptime(86400000L)
                        .jvmUptime(3600000L)
                        .build())
                .build();
    }
}