package com.rickpan.exception;

/**
 * 系统监控模块异常类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class SysMonitorException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 错误代码
     */
    private final String errorCode;

    /**
     * 错误类型
     */
    private final ErrorType errorType;

    public SysMonitorException(String message) {
        super(message);
        this.errorCode = "SYS_MONITOR_ERROR";
        this.errorType = ErrorType.GENERAL;
    }

    public SysMonitorException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "SYS_MONITOR_ERROR";
        this.errorType = ErrorType.GENERAL;
    }

    public SysMonitorException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.errorType = ErrorType.GENERAL;
    }

    public SysMonitorException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.errorType = ErrorType.GENERAL;
    }

    public SysMonitorException(ErrorType errorType, String message) {
        super(message);
        this.errorCode = errorType.getCode();
        this.errorType = errorType;
    }

    public SysMonitorException(ErrorType errorType, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorType.getCode();
        this.errorType = errorType;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public ErrorType getErrorType() {
        return errorType;
    }

    /**
     * 错误类型枚举
     */
    public enum ErrorType {
        GENERAL("SYS_MONITOR_ERROR", "通用错误"),
        DATA_COLLECTION_ERROR("DATA_COLLECTION_ERROR", "数据采集错误"),
        JMX_CONNECTION_ERROR("JMX_CONNECTION_ERROR", "JMX连接错误"),
        DATABASE_ERROR("DATABASE_ERROR", "数据库操作错误"),
        CONFIGURATION_ERROR("CONFIGURATION_ERROR", "配置错误"),
        PERMISSION_ERROR("PERMISSION_ERROR", "权限错误"),
        TIMEOUT_ERROR("TIMEOUT_ERROR", "超时错误"),
        VALIDATION_ERROR("VALIDATION_ERROR", "数据验证错误"),
        ALERT_ERROR("ALERT_ERROR", "告警处理错误"),
        EXPORT_ERROR("EXPORT_ERROR", "数据导出错误");

        private final String code;
        private final String description;

        ErrorType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}

/**
 * 数据采集异常
 */
class DataCollectionException extends SysMonitorException {
    public DataCollectionException(String message) {
        super(ErrorType.DATA_COLLECTION_ERROR, message);
    }

    public DataCollectionException(String message, Throwable cause) {
        super(ErrorType.DATA_COLLECTION_ERROR, message, cause);
    }
}

/**
 * JMX连接异常
 */
class JmxConnectionException extends SysMonitorException {
    public JmxConnectionException(String message) {
        super(ErrorType.JMX_CONNECTION_ERROR, message);
    }

    public JmxConnectionException(String message, Throwable cause) {
        super(ErrorType.JMX_CONNECTION_ERROR, message, cause);
    }
}

/**
 * 监控配置异常
 */
class MonitorConfigurationException extends SysMonitorException {
    public MonitorConfigurationException(String message) {
        super(ErrorType.CONFIGURATION_ERROR, message);
    }

    public MonitorConfigurationException(String message, Throwable cause) {
        super(ErrorType.CONFIGURATION_ERROR, message, cause);
    }
}

/**
 * 监控权限异常
 */
class MonitorPermissionException extends SysMonitorException {
    public MonitorPermissionException(String message) {
        super(ErrorType.PERMISSION_ERROR, message);
    }

    public MonitorPermissionException(String message, Throwable cause) {
        super(ErrorType.PERMISSION_ERROR, message, cause);
    }
}

/**
 * 监控超时异常
 */
class MonitorTimeoutException extends SysMonitorException {
    public MonitorTimeoutException(String message) {
        super(ErrorType.TIMEOUT_ERROR, message);
    }

    public MonitorTimeoutException(String message, Throwable cause) {
        super(ErrorType.TIMEOUT_ERROR, message, cause);
    }
}