package com.rickpan.repository;

import com.rickpan.entity.SysMonitorSystemInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 系统基础信息数据访问层
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface SysMonitorSystemInfoRepository extends JpaRepository<SysMonitorSystemInfo, Long> {

    /**
     * 获取最新的系统信息
     * 
     * @return 最新的系统信息
     */
    @Query("SELECT s FROM SysMonitorSystemInfo s ORDER BY s.updatedAt DESC")
    Optional<SysMonitorSystemInfo> findLatest();

    /**
     * 根据应用名称查找系统信息
     * 
     * @param appName 应用名称
     * @return 系统信息
     */
    Optional<SysMonitorSystemInfo> findByAppName(String appName);

    /**
     * 检查是否存在系统信息记录
     * 
     * @return 是否存在记录
     */
    @Query("SELECT COUNT(s) > 0 FROM SysMonitorSystemInfo s")
    boolean existsAny();
}