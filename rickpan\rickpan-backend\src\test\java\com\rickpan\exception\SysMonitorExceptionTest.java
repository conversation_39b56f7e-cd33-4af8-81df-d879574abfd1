package com.rickpan.exception;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 系统监控异常类单元测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class SysMonitorExceptionTest {

    @Test
    void testBasicConstructor() {
        String message = "监控系统异常";
        SysMonitorException exception = new SysMonitorException(message);
        
        assertEquals(message, exception.getMessage());
        assertEquals("SYS_MONITOR_ERROR", exception.getErrorCode());
        assertEquals(SysMonitorException.ErrorType.GENERAL, exception.getErrorType());
    }

    @Test
    void testConstructorWithCause() {
        String message = "监控系统异常";
        Exception cause = new RuntimeException("根本原因");
        SysMonitorException exception = new SysMonitorException(message, cause);
        
        assertEquals(message, exception.getMessage());
        assertEquals(cause, exception.getCause());
        assertEquals("SYS_MONITOR_ERROR", exception.getErrorCode());
        assertEquals(SysMonitorException.ErrorType.GENERAL, exception.getErrorType());
    }

    @Test
    void testConstructorWithErrorCode() {
        String errorCode = "CUSTOM_ERROR";
        String message = "自定义错误";
        SysMonitorException exception = new SysMonitorException(errorCode, message);
        
        assertEquals(message, exception.getMessage());
        assertEquals(errorCode, exception.getErrorCode());
        assertEquals(SysMonitorException.ErrorType.GENERAL, exception.getErrorType());
    }

    @Test
    void testConstructorWithErrorCodeAndCause() {
        String errorCode = "CUSTOM_ERROR";
        String message = "自定义错误";
        Exception cause = new RuntimeException("根本原因");
        SysMonitorException exception = new SysMonitorException(errorCode, message, cause);
        
        assertEquals(message, exception.getMessage());
        assertEquals(cause, exception.getCause());
        assertEquals(errorCode, exception.getErrorCode());
        assertEquals(SysMonitorException.ErrorType.GENERAL, exception.getErrorType());
    }

    @Test
    void testConstructorWithErrorType() {
        String message = "数据采集错误";
        SysMonitorException exception = new SysMonitorException(
                SysMonitorException.ErrorType.DATA_COLLECTION_ERROR, message);
        
        assertEquals(message, exception.getMessage());
        assertEquals("DATA_COLLECTION_ERROR", exception.getErrorCode());
        assertEquals(SysMonitorException.ErrorType.DATA_COLLECTION_ERROR, exception.getErrorType());
    }

    @Test
    void testConstructorWithErrorTypeAndCause() {
        String message = "JMX连接错误";
        Exception cause = new RuntimeException("连接失败");
        SysMonitorException exception = new SysMonitorException(
                SysMonitorException.ErrorType.JMX_CONNECTION_ERROR, message, cause);
        
        assertEquals(message, exception.getMessage());
        assertEquals(cause, exception.getCause());
        assertEquals("JMX_CONNECTION_ERROR", exception.getErrorCode());
        assertEquals(SysMonitorException.ErrorType.JMX_CONNECTION_ERROR, exception.getErrorType());
    }

    @Test
    void testAllErrorTypes() {
        SysMonitorException.ErrorType[] errorTypes = SysMonitorException.ErrorType.values();
        
        // 验证所有错误类型
        assertEquals(10, errorTypes.length);
        
        // 验证各个错误类型
        assertEquals("SYS_MONITOR_ERROR", SysMonitorException.ErrorType.GENERAL.getCode());
        assertEquals("通用错误", SysMonitorException.ErrorType.GENERAL.getDescription());
        
        assertEquals("DATA_COLLECTION_ERROR", SysMonitorException.ErrorType.DATA_COLLECTION_ERROR.getCode());
        assertEquals("数据采集错误", SysMonitorException.ErrorType.DATA_COLLECTION_ERROR.getDescription());
        
        assertEquals("JMX_CONNECTION_ERROR", SysMonitorException.ErrorType.JMX_CONNECTION_ERROR.getCode());
        assertEquals("JMX连接错误", SysMonitorException.ErrorType.JMX_CONNECTION_ERROR.getDescription());
        
        assertEquals("DATABASE_ERROR", SysMonitorException.ErrorType.DATABASE_ERROR.getCode());
        assertEquals("数据库操作错误", SysMonitorException.ErrorType.DATABASE_ERROR.getDescription());
        
        assertEquals("CONFIGURATION_ERROR", SysMonitorException.ErrorType.CONFIGURATION_ERROR.getCode());
        assertEquals("配置错误", SysMonitorException.ErrorType.CONFIGURATION_ERROR.getDescription());
        
        assertEquals("PERMISSION_ERROR", SysMonitorException.ErrorType.PERMISSION_ERROR.getCode());
        assertEquals("权限错误", SysMonitorException.ErrorType.PERMISSION_ERROR.getDescription());
        
        assertEquals("TIMEOUT_ERROR", SysMonitorException.ErrorType.TIMEOUT_ERROR.getCode());
        assertEquals("超时错误", SysMonitorException.ErrorType.TIMEOUT_ERROR.getDescription());
        
        assertEquals("VALIDATION_ERROR", SysMonitorException.ErrorType.VALIDATION_ERROR.getCode());
        assertEquals("数据验证错误", SysMonitorException.ErrorType.VALIDATION_ERROR.getDescription());
        
        assertEquals("ALERT_ERROR", SysMonitorException.ErrorType.ALERT_ERROR.getCode());
        assertEquals("告警处理错误", SysMonitorException.ErrorType.ALERT_ERROR.getDescription());
        
        assertEquals("EXPORT_ERROR", SysMonitorException.ErrorType.EXPORT_ERROR.getCode());
        assertEquals("数据导出错误", SysMonitorException.ErrorType.EXPORT_ERROR.getDescription());
    }

    @Test
    void testDataCollectionException() {
        String message = "数据采集失败";
        DataCollectionException exception = new DataCollectionException(message);
        
        assertEquals(message, exception.getMessage());
        assertEquals("DATA_COLLECTION_ERROR", exception.getErrorCode());
        assertEquals(SysMonitorException.ErrorType.DATA_COLLECTION_ERROR, exception.getErrorType());
    }

    @Test
    void testDataCollectionExceptionWithCause() {
        String message = "数据采集失败";
        Exception cause = new RuntimeException("JMX获取失败");
        DataCollectionException exception = new DataCollectionException(message, cause);
        
        assertEquals(message, exception.getMessage());
        assertEquals(cause, exception.getCause());
        assertEquals("DATA_COLLECTION_ERROR", exception.getErrorCode());
        assertEquals(SysMonitorException.ErrorType.DATA_COLLECTION_ERROR, exception.getErrorType());
    }

    @Test
    void testJmxConnectionException() {
        String message = "JMX连接失败";
        JmxConnectionException exception = new JmxConnectionException(message);
        
        assertEquals(message, exception.getMessage());
        assertEquals("JMX_CONNECTION_ERROR", exception.getErrorCode());
        assertEquals(SysMonitorException.ErrorType.JMX_CONNECTION_ERROR, exception.getErrorType());
    }

    @Test
    void testJmxConnectionExceptionWithCause() {
        String message = "JMX连接失败";
        Exception cause = new RuntimeException("连接超时");
        JmxConnectionException exception = new JmxConnectionException(message, cause);
        
        assertEquals(message, exception.getMessage());
        assertEquals(cause, exception.getCause());
        assertEquals("JMX_CONNECTION_ERROR", exception.getErrorCode());
        assertEquals(SysMonitorException.ErrorType.JMX_CONNECTION_ERROR, exception.getErrorType());
    }

    @Test
    void testMonitorConfigurationException() {
        String message = "监控配置错误";
        MonitorConfigurationException exception = new MonitorConfigurationException(message);
        
        assertEquals(message, exception.getMessage());
        assertEquals("CONFIGURATION_ERROR", exception.getErrorCode());
        assertEquals(SysMonitorException.ErrorType.CONFIGURATION_ERROR, exception.getErrorType());
    }

    @Test
    void testMonitorPermissionException() {
        String message = "监控权限不足";
        MonitorPermissionException exception = new MonitorPermissionException(message);
        
        assertEquals(message, exception.getMessage());
        assertEquals("PERMISSION_ERROR", exception.getErrorCode());
        assertEquals(SysMonitorException.ErrorType.PERMISSION_ERROR, exception.getErrorType());
    }

    @Test
    void testMonitorTimeoutException() {
        String message = "监控操作超时";
        MonitorTimeoutException exception = new MonitorTimeoutException(message);
        
        assertEquals(message, exception.getMessage());
        assertEquals("TIMEOUT_ERROR", exception.getErrorCode());
        assertEquals(SysMonitorException.ErrorType.TIMEOUT_ERROR, exception.getErrorType());
    }

    @Test
    void testMonitorTimeoutExceptionWithCause() {
        String message = "监控操作超时";
        Exception cause = new RuntimeException("网络超时");
        MonitorTimeoutException exception = new MonitorTimeoutException(message, cause);
        
        assertEquals(message, exception.getMessage());
        assertEquals(cause, exception.getCause());
        assertEquals("TIMEOUT_ERROR", exception.getErrorCode());
        assertEquals(SysMonitorException.ErrorType.TIMEOUT_ERROR, exception.getErrorType());
    }

    @Test
    void testExceptionInheritance() {
        // 验证异常继承关系
        SysMonitorException baseException = new SysMonitorException("基础异常");
        assertTrue(baseException instanceof RuntimeException);
        
        DataCollectionException dataException = new DataCollectionException("数据异常");
        assertTrue(dataException instanceof SysMonitorException);
        assertTrue(dataException instanceof RuntimeException);
        
        JmxConnectionException jmxException = new JmxConnectionException("JMX异常");
        assertTrue(jmxException instanceof SysMonitorException);
        assertTrue(jmxException instanceof RuntimeException);
    }
}