package com.rickpan.controller;

import com.rickpan.dto.SysMonitorDataDTO;
import com.rickpan.dto.SysMonitorQueryDTO;
import com.rickpan.entity.SysMonitorHistory;
import com.rickpan.service.SysMonitorCollectorService;
import com.rickpan.service.SysMonitorService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 系统监控控制器单元测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@WebMvcTest(SysMonitorController.class)
class SysMonitorControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private SysMonitorService sysMonitorService;

    @MockBean
    private SysMonitorCollectorService collectorService;

    @Autowired
    private ObjectMapper objectMapper;

    private SysMonitorDataDTO mockMonitorData;
    private List<SysMonitorHistory> mockHistoryList;
    private SysMonitorCollectorService.CollectionStatistics mockStatistics;

    @BeforeEach
    void setUp() {
        mockMonitorData = createMockMonitorData();
        mockHistoryList = createMockHistoryList();
        mockStatistics = createMockStatistics();
    }

    // ==================== 实时数据API测试 ====================

    @Test
    void testGetRealtimeData_Success() throws Exception {
        // 模拟服务返回
        when(sysMonitorService.getRealtimeData()).thenReturn(mockMonitorData);

        // 执行请求并验证
        mockMvc.perform(get("/sys-monitor/realtime"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.data.cpu.usage").value(65.5))
                .andExpect(jsonPath("$.data.memory.usage").value(72.0))
                .andExpect(jsonPath("$.message").value("获取实时监控数据成功"));

        // 验证服务调用
        verify(sysMonitorService).getRealtimeData();
    }

    @Test
    void testGetRealtimeData_ServiceException() throws Exception {
        // 模拟服务抛出异常
        when(sysMonitorService.getRealtimeData())
                .thenThrow(new RuntimeException("JMX连接失败"));

        // 执行请求并验证
        mockMvc.perform(get("/sys-monitor/realtime"))
                .andExpect(status().isInternalServerError())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("获取实时监控数据失败: JMX连接失败"));

        // 验证服务调用
        verify(sysMonitorService).getRealtimeData();
    }

    // ==================== 历史数据API测试 ====================

    @Test
    void testGetHistoryData_Success() throws Exception {
        // 准备请求数据
        SysMonitorQueryDTO queryDTO = new SysMonitorQueryDTO();
        queryDTO.setTimeRange("1h");
        queryDTO.setPage(1);
        queryDTO.setSize(20);
        queryDTO.setSort("desc");

        Page<SysMonitorHistory> mockPage = new PageImpl<>(mockHistoryList);

        // 模拟服务返回
        when(sysMonitorService.getHistoryData(any(SysMonitorQueryDTO.class)))
                .thenReturn(mockPage);

        // 执行请求并验证
        mockMvc.perform(post("/sys-monitor/history")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(queryDTO)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpectAll(jsonPath("$.data").isNotEmpty())
                .andExpect(jsonPath("$.total").value(mockHistoryList.size()))
                .andExpect(jsonPath("$.message").value("查询历史监控数据成功"));

        // 验证服务调用
        verify(sysMonitorService).getHistoryData(any(SysMonitorQueryDTO.class));
    }

    @Test
    void testGetHistoryDataSimple_Success() throws Exception {
        Page<SysMonitorHistory> mockPage = new PageImpl<>(mockHistoryList);

        // 模拟服务返回
        when(sysMonitorService.getHistoryData(any(SysMonitorQueryDTO.class)))
                .thenReturn(mockPage);

        // 执行请求并验证
        mockMvc.perform(get("/sys-monitor/history")
                        .param("timeRange", "6h")
                        .param("page", "1")
                        .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray());

        // 验证服务调用
        verify(sysMonitorService).getHistoryData(any(SysMonitorQueryDTO.class));
    }

    @Test
    void testGetHistoryData_ServiceException() throws Exception {
        SysMonitorQueryDTO queryDTO = new SysMonitorQueryDTO();
        queryDTO.setTimeRange("1h");

        // 模拟服务抛出异常
        when(sysMonitorService.getHistoryData(any(SysMonitorQueryDTO.class)))
                .thenThrow(new RuntimeException("数据库连接失败"));

        // 执行请求并验证
        mockMvc.perform(post("/sys-monitor/history")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(queryDTO)))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("查询历史监控数据失败: 数据库连接失败"));
    }

    // ==================== 统计数据API测试 ====================

    @Test
    void testGetStatistics_Success() throws Exception {
        // 模拟统计数据
        Object mockStats = mockMonitorData;
        when(sysMonitorService.getMonitorStatistics("24h")).thenReturn(mockStats);

        // 执行请求并验证
        mockMvc.perform(get("/sys-monitor/statistics")
                        .param("timeRange", "24h"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.timeRange").value("24h"))
                .andExpect(jsonPath("$.message").value("获取监控统计信息成功"));

        // 验证服务调用
        verify(sysMonitorService).getMonitorStatistics("24h");
    }

    @Test
    void testGetStatistics_ServiceException() throws Exception {
        // 模拟服务抛出异常
        when(sysMonitorService.getMonitorStatistics("24h"))
                .thenThrow(new RuntimeException("统计查询失败"));

        // 执行请求并验证
        mockMvc.perform(get("/sys-monitor/statistics")
                        .param("timeRange", "24h"))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("获取监控统计信息失败: 统计查询失败"));
    }

    // ==================== 采集控制API测试 ====================

    @Test
    void testGetCollectorStatus_Success() throws Exception {
        // 模拟采集服务状态
        when(collectorService.getStatistics()).thenReturn(mockStatistics);

        // 执行请求并验证
        mockMvc.perform(get("/sys-monitor/collector/status"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.enabled").value(true))
                .andExpect(jsonPath("$.data.totalCollections").value(100))
                .andExpect(jsonPath("$.data.successCollections").value(95))
                .andExpect(jsonPath("$.data.errorCollections").value(5))
                .andExpect(jsonPath("$.data.successRate").value(95.0))
                .andExpect(jsonPath("$.message").value("获取采集服务状态成功"));

        // 验证服务调用
        verify(collectorService).getStatistics();
    }

    @Test
    void testEnableCollection_Success() throws Exception {
        // 模拟启用采集
        doNothing().when(collectorService).enableCollection();

        // 执行请求并验证
        mockMvc.perform(post("/sys-monitor/collector/enable"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("数据采集已启用"));

        // 验证服务调用
        verify(collectorService).enableCollection();
    }

    @Test
    void testDisableCollection_Success() throws Exception {
        // 模拟禁用采集
        doNothing().when(collectorService).disableCollection();

        // 执行请求并验证
        mockMvc.perform(post("/sys-monitor/collector/disable"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("数据采集已禁用"));

        // 验证服务调用
        verify(collectorService).disableCollection();
    }

    @Test
    void testManualCollection_Success() throws Exception {
        // 模拟手动采集
        when(collectorService.manualCollect()).thenReturn(mockMonitorData);

        // 执行请求并验证
        mockMvc.perform(post("/sys-monitor/collector/manual"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.data.cpu.usage").value(65.5))
                .andExpect(jsonPath("$.message").value("手动数据采集完成"));

        // 验证服务调用
        verify(collectorService).manualCollect();
    }

    @Test
    void testManualCollection_ServiceException() throws Exception {
        // 模拟服务抛出异常
        when(collectorService.manualCollect())
                .thenThrow(new RuntimeException("采集失败"));

        // 执行请求并验证
        mockMvc.perform(post("/sys-monitor/collector/manual"))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("手动数据采集失败: 采集失败"));
    }

    @Test
    void testResetStatistics_Success() throws Exception {
        // 模拟重置统计
        doNothing().when(collectorService).resetStatistics();

        // 执行请求并验证
        mockMvc.perform(post("/sys-monitor/collector/reset-stats"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("采集统计已重置"));

        // 验证服务调用
        verify(collectorService).resetStatistics();
    }

    // ==================== 健康检查API测试 ====================

    @Test
    void testHealthCheck_Success() throws Exception {
        // 模拟健康状态
        when(collectorService.getStatistics()).thenReturn(mockStatistics);
        when(sysMonitorService.getRealtimeData()).thenReturn(mockMonitorData);

        // 执行请求并验证
        mockMvc.perform(get("/sys-monitor/health"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.status").value("UP"))
                .andExpect(jsonPath("$.data.collectorEnabled").value(true))
                .andExpect(jsonPath("$.data.dataCollectionWorking").value(true))
                .andExpect(jsonPath("$.message").value("系统监控健康"));

        // 验证服务调用
        verify(collectorService).getStatistics();
        verify(sysMonitorService).getRealtimeData();
    }

    @Test
    void testHealthCheck_ServiceException() throws Exception {
        // 模拟服务异常
        when(collectorService.getStatistics()).thenReturn(mockStatistics);
        when(sysMonitorService.getRealtimeData())
                .thenThrow(new RuntimeException("监控服务异常"));

        // 执行请求并验证
        mockMvc.perform(get("/sys-monitor/health"))
                .andExpect(status().isOk()) // 健康检查返回200
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.data.status").value("DOWN"))
                .andExpect(jsonPath("$.data.error").value("监控服务异常"))
                .andExpect(jsonPath("$.message").value("系统监控异常"));
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 创建模拟监控数据
     */
    private SysMonitorDataDTO createMockMonitorData() {
        return SysMonitorDataDTO.builder()
                .timestamp(LocalDateTime.now())
                .cpu(SysMonitorDataDTO.CpuInfo.builder()
                        .usage(BigDecimal.valueOf(65.5))
                        .cores(8)
                        .loadAverage(new BigDecimal[]{
                                BigDecimal.valueOf(1.2), 
                                BigDecimal.valueOf(1.1), 
                                BigDecimal.valueOf(1.0)
                        })
                        .processUsage(BigDecimal.valueOf(25.3))
                        .build())
                .memory(SysMonitorDataDTO.MemoryInfo.builder()
                        .total(16777216000L)
                        .used(12079595520L)
                        .free(4697620480L)
                        .available(4697620480L)
                        .usage(BigDecimal.valueOf(72.0))
                        .build())
                .disk(SysMonitorDataDTO.DiskInfo.builder()
                        .total(1000204886016L)
                        .used(550000000000L)
                        .available(450204886016L)
                        .usage(BigDecimal.valueOf(55.0))
                        .build())
                .jvm(SysMonitorDataDTO.JvmInfo.builder()
                        .heapUsed(536870912L)
                        .heapMax(1073741824L)
                        .heapCommitted(1073741824L)
                        .heapUsage(BigDecimal.valueOf(50.0))
                        .nonHeapUsed(134217728L)
                        .nonHeapMax(-1L)
                        .nonHeapCommitted(134217728L)
                        .nonHeapUsage(BigDecimal.valueOf(0.0))
                        .uptime(3600000L)
                        .build())
                .gc(SysMonitorDataDTO.GcInfo.builder()
                        .count(100L)
                        .time(5000L)
                        .avgTime(BigDecimal.valueOf(50.0))
                        .maxTime(200L)
                        .collectors(new SysMonitorDataDTO.GcCollectorInfo[0])
                        .build())
                .threads(SysMonitorDataDTO.ThreadInfo.builder()
                        .active(50)
                        .peak(75)
                        .daemon(25)
                        .totalStarted(1000L)
                        .user(25)
                        .deadlocked(0)
                        .build())
                .system(SysMonitorDataDTO.SystemInfo.builder()
                        .osName("Windows 10")
                        .osVersion("10.0")
                        .osArch("amd64")
                        .javaVersion("17.0.2")
                        .javaVendor("Eclipse Adoptium")
                        .jvmName("OpenJDK 64-Bit Server VM")
                        .jvmVersion("17.0.2+8")
                        .appName("RickPan")
                        .appVersion("2.1.0")
                        .appStartTime(LocalDateTime.now().minusHours(1))
                        .systemUptime(86400000L)
                        .jvmUptime(3600000L)
                        .build())
                .build();
    }

    /**
     * 创建模拟历史数据列表
     */
    private List<SysMonitorHistory> createMockHistoryList() {
        List<SysMonitorHistory> historyList = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            SysMonitorHistory history = SysMonitorHistory.builder()
                    .id((long) (i + 1))
                    .recordTime(LocalDateTime.now().minusMinutes(i * 5))
                    .cpuUsage(BigDecimal.valueOf(60 + i * 2))
                    .memoryUsage(BigDecimal.valueOf(70 + i))
                    .diskUsage(BigDecimal.valueOf(50 + i))
                    .jvmHeapUsage(BigDecimal.valueOf(45 + i * 3))
                    .build();
            historyList.add(history);
        }
        return historyList;
    }

    /**
     * 创建模拟统计信息
     */
    private SysMonitorCollectorService.CollectionStatistics createMockStatistics() {
        return new SysMonitorCollectorService.CollectionStatistics(
                100L, 95L, 5L, 
                LocalDateTime.now(), LocalDateTime.now(), 
                true, false);
    }
}