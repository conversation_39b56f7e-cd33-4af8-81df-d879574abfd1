-- =====================================================
-- SysMonitor 系统监控模块初始化数据脚本
-- 版本: v1.0
-- 创建时间: 2025-01-15
-- 描述: 系统监控功能的初始化数据，包含默认告警配置等
-- =====================================================

-- 使用现有数据库
USE rick_pan;

-- =====================================================
-- 1. 插入默认告警配置
-- =====================================================

-- 清空现有告警配置（如果存在）
DELETE FROM sys_monitor_alert_config WHERE metric_type IN ('CPU', 'MEMORY', 'DISK', 'JVM', 'THREAD');

-- CPU相关告警配置
INSERT INTO sys_monitor_alert_config (
    metric_type, metric_name, metric_display_name, 
    warning_threshold, critical_threshold, emergency_threshold,
    comparison_operator, unit, description, enabled, notification_enabled
) VALUES 
-- CPU使用率监控
('CPU', 'cpu_usage', 'CPU使用率', 70.00, 85.00, 95.00, 'GT', '%', 'CPU使用率监控，防止系统负载过高', 1, 1),

-- CPU负载监控
('CPU', 'cpu_load_1min', '1分钟负载', 2.00, 4.00, 8.00, 'GT', '', '1分钟平均负载监控', 1, 1),
('CPU', 'cpu_load_5min', '5分钟负载', 1.50, 3.00, 6.00, 'GT', '', '5分钟平均负载监控', 1, 1),
('CPU', 'cpu_load_15min', '15分钟负载', 1.20, 2.50, 5.00, 'GT', '', '15分钟平均负载监控', 1, 1);

-- 内存相关告警配置
INSERT INTO sys_monitor_alert_config (
    metric_type, metric_name, metric_display_name,
    warning_threshold, critical_threshold, emergency_threshold,
    comparison_operator, unit, description, enabled, notification_enabled
) VALUES 
-- 系统内存使用率
('MEMORY', 'memory_usage', '系统内存使用率', 80.00, 90.00, 95.00, 'GT', '%', '系统内存使用率监控，防止内存耗尽', 1, 1),

-- 可用内存监控（阈值为绝对值，单位为MB）
('MEMORY', 'memory_free', '可用内存', 1024.00, 512.00, 256.00, 'LT', 'MB', '系统可用内存监控，低于阈值时告警', 1, 1);

-- 磁盘相关告警配置
INSERT INTO sys_monitor_alert_config (
    metric_type, metric_name, metric_display_name,
    warning_threshold, critical_threshold, emergency_threshold,
    comparison_operator, unit, description, enabled, notification_enabled
) VALUES 
-- 磁盘使用率
('DISK', 'disk_usage', '磁盘使用率', 85.00, 90.00, 95.00, 'GT', '%', '磁盘使用率监控，防止磁盘空间不足', 1, 1),

-- 可用磁盘空间（阈值为绝对值，单位为GB）
('DISK', 'disk_available', '可用磁盘空间', 10.00, 5.00, 2.00, 'LT', 'GB', '磁盘可用空间监控，低于阈值时告警', 1, 1);

-- JVM相关告警配置
INSERT INTO sys_monitor_alert_config (
    metric_type, metric_name, metric_display_name,
    warning_threshold, critical_threshold, emergency_threshold,
    comparison_operator, unit, description, enabled, notification_enabled
) VALUES 
-- JVM堆内存使用率
('JVM', 'jvm_heap_usage', 'JVM堆内存使用率', 75.00, 85.00, 95.00, 'GT', '%', 'JVM堆内存使用率监控，防止内存溢出', 1, 1),

-- JVM非堆内存使用率
('JVM', 'jvm_non_heap_usage', 'JVM非堆内存使用率', 80.00, 90.00, 95.00, 'GT', '%', 'JVM非堆内存使用率监控', 1, 1),

-- GC频率监控（每分钟GC次数）
('JVM', 'gc_frequency', 'GC频率', 10.00, 20.00, 50.00, 'GT', '次/分钟', 'GC频率监控，频繁GC可能影响性能', 1, 1),

-- GC耗时监控（单次GC平均耗时）
('JVM', 'gc_avg_time', 'GC平均耗时', 100.00, 500.00, 1000.00, 'GT', 'ms', 'GC平均耗时监控，耗时过长影响响应', 1, 1);

-- 线程相关告警配置
INSERT INTO sys_monitor_alert_config (
    metric_type, metric_name, metric_display_name,
    warning_threshold, critical_threshold, emergency_threshold,
    comparison_operator, unit, description, enabled, notification_enabled
) VALUES 
-- 活跃线程数
('THREAD', 'thread_count', '活跃线程数', 200.00, 500.00, 1000.00, 'GT', '个', '活跃线程数监控，线程过多可能影响性能', 1, 1),

-- 死锁线程监控
('THREAD', 'deadlocked_threads', '死锁线程数', 1.00, 1.00, 1.00, 'GTE', '个', '死锁线程监控，发现死锁立即告警', 1, 1),

-- 线程池使用率（如果有线程池监控的话）
('THREAD', 'thread_pool_usage', '线程池使用率', 80.00, 90.00, 95.00, 'GT', '%', '线程池使用率监控', 0, 0);

-- =====================================================
-- 2. 插入系统基础信息（初始化数据）
-- =====================================================

-- 清空现有系统信息
DELETE FROM sys_monitor_system_info;

-- 插入默认系统信息（启动时会被实际数据覆盖）
INSERT INTO sys_monitor_system_info (
    app_name, app_version, app_start_time,
    os_name, os_version, os_arch,
    java_version, java_vendor, java_home,
    jvm_name, jvm_version,
    processor_count, total_physical_memory, total_disk_space
) VALUES (
    'RickPan', 
    '2.1.0', 
    NOW(),
    'Unknown', 
    'Unknown', 
    'Unknown',
    'Unknown', 
    'Unknown', 
    'Unknown',
    'Unknown', 
    'Unknown',
    0, 0, 0
);

-- =====================================================
-- 3. 创建告警配置视图（便于查询）
-- =====================================================

-- 创建告警配置概览视图
CREATE OR REPLACE VIEW v_sys_monitor_alert_config_overview AS
SELECT 
    c.id,
    c.metric_type,
    c.metric_name,
    c.metric_display_name,
    c.warning_threshold,
    c.critical_threshold,
    c.emergency_threshold,
    c.comparison_operator,
    c.unit,
    c.enabled,
    c.notification_enabled,
    c.description,
    -- 计算告警配置的优先级
    CASE 
        WHEN c.emergency_threshold IS NOT NULL THEN 'HIGH'
        WHEN c.critical_threshold IS NOT NULL THEN 'MEDIUM' 
        WHEN c.warning_threshold IS NOT NULL THEN 'LOW'
        ELSE 'INFO'
    END as priority,
    -- 统计该配置的告警次数（最近7天）
    COALESCE(
        (SELECT COUNT(*) 
         FROM sys_monitor_alert_log l 
         WHERE l.alert_config_id = c.id 
         AND l.alert_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        ), 0
    ) as recent_alert_count,
    c.created_at,
    c.updated_at
FROM sys_monitor_alert_config c
ORDER BY 
    c.metric_type, 
    CASE c.metric_name 
        WHEN 'cpu_usage' THEN 1
        WHEN 'memory_usage' THEN 2  
        WHEN 'disk_usage' THEN 3
        WHEN 'jvm_heap_usage' THEN 4
        ELSE 99
    END;

-- =====================================================
-- 4. 创建默认的系统维护告警记录
-- =====================================================

-- 插入系统初始化完成的信息记录
INSERT INTO sys_monitor_alert_log (
    alert_type, metric_type, metric_name, alert_level, alert_status,
    alert_title, alert_message, alert_time,
    metric_value, threshold_value, unit
) VALUES (
    'SYSTEM_MAINTENANCE', 
    'SYSTEM', 
    'initialization', 
    'INFO',
    'RESOLVED',
    '系统监控模块初始化完成',
    'SysMonitor系统监控模块初始化完成，已创建默认告警配置和基础数据。',
    NOW(),
    1.00, 
    1.00, 
    'status'
);

-- =====================================================
-- 5. 创建示例监控数据（用于测试和演示）
-- =====================================================

-- 注意：这里只是创建表结构，实际的监控数据会由定时任务生成
-- 如果需要测试数据，可以插入一些模拟数据

-- 插入一条当前时间的实时监控数据模拟记录
INSERT INTO sys_monitor_realtime (
    cpu_usage, cpu_cores, 
    memory_total, memory_used, memory_free, memory_usage,
    disk_total, disk_used, disk_available, disk_usage,
    jvm_heap_used, jvm_heap_max, jvm_heap_usage,
    jvm_non_heap_used, jvm_non_heap_max,
    gc_count, gc_time, gc_avg_time,
    thread_count, thread_peak_count, thread_daemon_count,
    system_uptime, jvm_uptime
) VALUES (
    25.50, 8,  -- CPU: 25.5%, 8核
    17179869184, 4294967296, 12884901888, 25.00,  -- 内存: 16GB总，4GB已用，25%使用率
    1073741824000, 322122547200, 751619276800, 30.00,  -- 磁盘: 1TB总，300GB已用，30%使用率
    268435456, 2147483648, 12.50,  -- JVM堆: 256MB已用，2GB最大，12.5%使用率
    134217728, 268435456,  -- JVM非堆: 128MB已用，256MB最大
    15, 150, 10.00,  -- GC: 15次，150ms总耗时，平均10ms
    45, 50, 20,  -- 线程: 45活跃，50峰值，20守护
    3600000, 3600000  -- 运行时间: 1小时
);

-- =====================================================
-- 6. 验证初始化结果
-- =====================================================

-- 检查告警配置数量
SELECT 
    '告警配置统计' as check_item,
    metric_type,
    COUNT(*) as config_count,
    SUM(CASE WHEN enabled = 1 THEN 1 ELSE 0 END) as enabled_count
FROM sys_monitor_alert_config 
GROUP BY metric_type
UNION ALL
SELECT 
    '总计' as check_item,
    'ALL' as metric_type,
    COUNT(*) as config_count,
    SUM(CASE WHEN enabled = 1 THEN 1 ELSE 0 END) as enabled_count
FROM sys_monitor_alert_config;

-- 检查系统信息
SELECT 
    '系统信息检查' as check_item,
    app_name,
    app_version, 
    'OK' as status
FROM sys_monitor_system_info 
LIMIT 1;

-- 检查实时监控数据
SELECT 
    '实时监控数据检查' as check_item,
    CASE WHEN COUNT(*) > 0 THEN 'OK' ELSE 'EMPTY' END as status,
    COUNT(*) as record_count
FROM sys_monitor_realtime;

-- 检查告警日志
SELECT 
    '告警日志检查' as check_item,
    CASE WHEN COUNT(*) > 0 THEN 'OK' ELSE 'EMPTY' END as status,
    COUNT(*) as log_count
FROM sys_monitor_alert_log;

-- =====================================================
-- 7. 设置权限（如果需要）
-- =====================================================

-- 这里可以添加数据库用户权限设置
-- 例如为应用用户授予表的操作权限

-- GRANT SELECT, INSERT, UPDATE, DELETE ON sys_monitor_* TO 'rickpan_user'@'%';

-- =====================================================
-- 初始化完成
-- =====================================================

SELECT 
    'SysMonitor系统监控模块初始化完成!' as message,
    NOW() as initialized_at,
    (SELECT COUNT(*) FROM sys_monitor_alert_config WHERE enabled = 1) as enabled_alert_configs,
    'SUCCESS' as status;