package com.rickpan.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 系统监控数据传输对象
 * 用于前后端数据传输
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysMonitorDataDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 数据时间戳
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timestamp;

    /**
     * CPU相关信息
     */
    private CpuInfo cpu;

    /**
     * 系统内存相关信息
     */
    private MemoryInfo memory;

    /**
     * 磁盘相关信息
     */
    private DiskInfo disk;

    /**
     * JVM相关信息
     */
    private JvmInfo jvm;

    /**
     * 垃圾回收相关信息
     */
    private GcInfo gc;

    /**
     * 线程相关信息
     */
    private ThreadInfo threads;

    /**
     * 系统信息
     */
    private SystemInfo system;

    // ==================== 内部类定义 ====================

    /**
     * CPU信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CpuInfo implements Serializable {
        /**
         * CPU使用率(%)
         */
        private BigDecimal usage;

        /**
         * CPU核心数
         */
        private Integer cores;

        /**
         * 系统负载平均值数组 [1分钟, 5分钟, 15分钟]
         */
        private BigDecimal[] loadAverage;

        /**
         * 进程CPU使用率(%)
         */
        private BigDecimal processUsage;
    }

    /**
     * 内存信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MemoryInfo implements Serializable {
        /**
         * 总内存(字节)
         */
        private Long total;

        /**
         * 已用内存(字节)
         */
        private Long used;

        /**
         * 空闲内存(字节)
         */
        private Long free;

        /**
         * 内存使用率(%)
         */
        private BigDecimal usage;

        /**
         * 可用内存(字节)
         */
        private Long available;
    }

    /**
     * 磁盘信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DiskInfo implements Serializable {
        /**
         * 总空间(字节)
         */
        private Long total;

        /**
         * 已用空间(字节)
         */
        private Long used;

        /**
         * 可用空间(字节)
         */
        private Long available;

        /**
         * 磁盘使用率(%)
         */
        private BigDecimal usage;
    }

    /**
     * JVM信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class JvmInfo implements Serializable {
        /**
         * 堆内存已用(字节)
         */
        private Long heapUsed;

        /**
         * 堆内存最大(字节)
         */
        private Long heapMax;

        /**
         * 堆内存已分配(字节)
         */
        private Long heapCommitted;

        /**
         * 堆内存使用率(%)
         */
        private BigDecimal heapUsage;

        /**
         * 非堆内存已用(字节)
         */
        private Long nonHeapUsed;

        /**
         * 非堆内存最大(字节)
         */
        private Long nonHeapMax;

        /**
         * 非堆内存已分配(字节)
         */
        private Long nonHeapCommitted;

        /**
         * 非堆内存使用率(%)
         */
        private BigDecimal nonHeapUsage;

        /**
         * JVM运行时间(毫秒)
         */
        private Long uptime;
    }

    /**
     * 垃圾回收信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GcInfo implements Serializable {
        /**
         * GC总次数
         */
        private Long count;

        /**
         * GC总耗时(毫秒)
         */
        private Long time;

        /**
         * GC平均耗时(毫秒)
         */
        private BigDecimal avgTime;

        /**
         * GC最大耗时(毫秒)
         */
        private Long maxTime;

        /**
         * GC收集器详情
         */
        private GcCollectorInfo[] collectors;
    }

    /**
     * GC收集器信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GcCollectorInfo implements Serializable {
        /**
         * 收集器名称
         */
        private String name;

        /**
         * 收集次数
         */
        private Long collectionCount;

        /**
         * 收集耗时(毫秒)
         */
        private Long collectionTime;

        /**
         * 平均收集耗时(毫秒)
         */
        private BigDecimal avgCollectionTime;
    }

    /**
     * 线程信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ThreadInfo implements Serializable {
        /**
         * 活跃线程数
         */
        private Integer active;

        /**
         * 峰值线程数
         */
        private Integer peak;

        /**
         * 守护线程数
         */
        private Integer daemon;

        /**
         * 总启动线程数
         */
        private Long totalStarted;

        /**
         * 用户线程数
         */
        private Integer user;

        /**
         * 死锁线程数
         */
        private Integer deadlocked;
    }

    /**
     * 系统信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SystemInfo implements Serializable {
        /**
         * 操作系统名称
         */
        private String osName;

        /**
         * 操作系统版本
         */
        private String osVersion;

        /**
         * 系统架构
         */
        private String osArch;

        /**
         * Java版本
         */
        private String javaVersion;

        /**
         * Java厂商
         */
        private String javaVendor;

        /**
         * JVM名称
         */
        private String jvmName;

        /**
         * JVM版本
         */
        private String jvmVersion;

        /**
         * 应用名称
         */
        private String appName;

        /**
         * 应用版本
         */
        private String appVersion;

        /**
         * 应用启动时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime appStartTime;

        /**
         * 系统运行时间(毫秒)
         */
        private Long systemUptime;

        /**
         * JVM运行时间(毫秒)
         */
        private Long jvmUptime;
    }
}