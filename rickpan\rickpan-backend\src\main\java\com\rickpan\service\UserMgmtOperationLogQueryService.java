package com.rickpan.service;

import com.rickpan.dto.usermgmt.UserMgmtOperationLogQueryDTO;
import com.rickpan.dto.usermgmt.UserMgmtOperationLogDetailDTO;
import com.rickpan.dto.usermgmt.UserMgmtPageDTO;
import com.rickpan.entity.UserMgmtOperationLog;
import com.rickpan.repository.UserMgmtOperationLogRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.criteria.Predicate;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.Arrays;

/**
 * 用户管理操作日志查询服务
 * Operation Log Query Service - 提供操作日志的查询和统计功能
 * 
 * <AUTHOR> Team
 * @since Phase 3
 */
@Service
@Transactional(readOnly = true)
public class UserMgmtOperationLogQueryService {

    private static final Logger logger = LoggerFactory.getLogger(UserMgmtOperationLogQueryService.class);

    @Autowired
    private UserMgmtOperationLogRepository operationLogRepository;

    // ========== 操作日志查询方法 ==========

    /**
     * 获取操作日志列表（分页）
     * 
     * @param queryDTO 查询参数
     * @return 分页操作日志列表
     */
    public UserMgmtPageDTO<UserMgmtOperationLogDetailDTO> getOperationLogList(UserMgmtOperationLogQueryDTO queryDTO) {
        try {
            logger.debug("获取操作日志列表 - 查询参数: {}", queryDTO);
            
            // 构建分页和排序参数
            Pageable pageable = buildPageable(queryDTO);
            
            // 构建查询条件
            Specification<UserMgmtOperationLog> specification = buildOperationLogSpecification(queryDTO);
            
            // 执行分页查询
            Page<UserMgmtOperationLog> page = operationLogRepository.findAll(specification, pageable);
            
            // 转换为DTO
            List<UserMgmtOperationLogDetailDTO> content = page.getContent().stream()
                    .map(UserMgmtOperationLogDetailDTO::new)
                    .collect(Collectors.toList());
            
            // 构建分页响应
            UserMgmtPageDTO<UserMgmtOperationLogDetailDTO> result = new UserMgmtPageDTO<>();
            result.setContent(content);
            result.setNumber(queryDTO.getPageForSpringData()); // 使用从0开始的页码
            result.setSize(queryDTO.getSize());
            result.setTotalElements(page.getTotalElements());
            result.setTotalPages(page.getTotalPages());
            result.setNumberOfElements(content.size());
            result.setFirst(page.isFirst());
            result.setLast(page.isLast());
            
            logger.debug("获取操作日志列表成功 - 总记录数: {}, 当前页记录数: {}", 
                    page.getTotalElements(), content.size());
            
            return result;
            
        } catch (Exception e) {
            logger.error("获取操作日志列表失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取操作日志列表失败: " + e.getMessage());
        }
    }

    /**
     * 搜索操作日志
     * 
     * @param queryDTO 搜索查询参数
     * @return 搜索结果
     */
    public UserMgmtPageDTO<UserMgmtOperationLogDetailDTO> searchOperationLogs(UserMgmtOperationLogQueryDTO queryDTO) {
        // 搜索和列表使用相同的逻辑
        return getOperationLogList(queryDTO);
    }

    /**
     * 根据ID获取操作日志详情
     * 
     * @param logId 操作日志ID
     * @return 操作日志详情
     */
    public Optional<UserMgmtOperationLogDetailDTO> getOperationLogById(Long logId) {
        try {
            logger.debug("获取操作日志详情 - logId: {}", logId);
            
            Optional<UserMgmtOperationLog> logOpt = operationLogRepository.findById(logId);
            
            if (logOpt.isPresent()) {
                UserMgmtOperationLogDetailDTO dto = new UserMgmtOperationLogDetailDTO(logOpt.get());
                logger.debug("获取操作日志详情成功 - logId: {}, operationType: {}", 
                        logId, dto.getOperationType());
                return Optional.of(dto);
            } else {
                logger.debug("操作日志不存在 - logId: {}", logId);
                return Optional.empty();
            }
            
        } catch (Exception e) {
            logger.error("获取操作日志详情失败 - logId: {}, error: {}", logId, e.getMessage(), e);
            throw new RuntimeException("获取操作日志详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取操作日志统计信息
     * 
     * @param days 统计天数
     * @return 统计信息
     */
    public Map<String, Object> getOperationLogStats(Integer days) {
        try {
            logger.debug("获取操作日志统计信息 - days: {}", days);
            
            LocalDateTime startTime = LocalDateTime.now().minusDays(days);
            LocalDateTime endTime = LocalDateTime.now();
            
            logger.info("统计时间范围 - startTime: {}, endTime: {}", startTime, endTime);
            
            Map<String, Object> stats = new HashMap<>();
            
            // 总操作数
            Long totalOperations = operationLogRepository.countOperationsByTimeRange(startTime, endTime);
            logger.info("总操作数查询结果: {}", totalOperations);
            stats.put("totalOperations", totalOperations);
            
            // 按操作类型统计
            List<Object[]> operationTypeStats = operationLogRepository.countOperationsByType(startTime, endTime);
            logger.info("操作类型统计查询结果数量: {}", operationTypeStats.size());
            Map<String, Long> operationTypeMap = new HashMap<>();
            for (Object[] row : operationTypeStats) {
                UserMgmtOperationLog.OperationType type = (UserMgmtOperationLog.OperationType) row[0];
                Long count = (Long) row[1];
                operationTypeMap.put(type.name(), count);
            }
            stats.put("operationTypeStats", operationTypeMap);
            
            // 按操作者统计
            List<Object[]> operatorStats = operationLogRepository.countOperationsByOperator(startTime, endTime);
            List<Map<String, Object>> operatorList = new ArrayList<>();
            for (Object[] row : operatorStats) {
                if (operatorStats.size() <= 10) { // 只取前10个
                    Map<String, Object> operatorInfo = new HashMap<>();
                    operatorInfo.put("operatorId", row[0]);
                    operatorInfo.put("operatorName", row[1]);
                    operatorInfo.put("operationCount", row[2]);
                    operatorList.add(operatorInfo);
                }
            }
            stats.put("topOperators", operatorList);
            
            // 按日期统计
            List<Object[]> dateStats = operationLogRepository.countOperationsByDate(startTime, endTime);
            List<Map<String, Object>> dateList = new ArrayList<>();
            for (Object[] row : dateStats) {
                Map<String, Object> dateInfo = new HashMap<>();
                dateInfo.put("date", row[0]);
                dateInfo.put("count", row[1]);
                dateList.add(dateInfo);
            }
            stats.put("dailyStats", dateList);
            
            // 成功失败统计（考虑时间范围）
            Long successCount = operationLogRepository.countOperationsByResultAndTimeRange(
                    UserMgmtOperationLog.OperationResult.SUCCESS, startTime, endTime);
            Long failedCount = operationLogRepository.countOperationsByResultAndTimeRange(
                    UserMgmtOperationLog.OperationResult.FAILED, startTime, endTime);
            Long partialSuccessCount = operationLogRepository.countOperationsByResultAndTimeRange(
                    UserMgmtOperationLog.OperationResult.PARTIAL_SUCCESS, startTime, endTime);
            
            logger.info("操作结果统计 - SUCCESS: {}, FAILED: {}, PARTIAL_SUCCESS: {}", 
                    successCount, failedCount, partialSuccessCount);
            
            Map<String, Long> resultStats = new HashMap<>();
            resultStats.put("SUCCESS", successCount);
            resultStats.put("FAILED", failedCount);
            resultStats.put("PARTIAL_SUCCESS", partialSuccessCount);
            stats.put("operationResultStats", resultStats);
            
            // 统计时间范围
            stats.put("statisticsRange", Map.of(
                    "startTime", startTime,
                    "endTime", endTime,
                    "days", days
            ));
            
            logger.debug("获取操作日志统计信息成功 - totalOperations: {}", totalOperations);
            
            return stats;
            
        } catch (Exception e) {
            logger.error("获取操作日志统计信息失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取操作日志统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取操作类型选项
     * 
     * @return 操作类型选项映射
     */
    public Map<String, String> getOperationTypes() {
        Map<String, String> operationTypes = new HashMap<>();
        for (UserMgmtOperationLog.OperationType type : UserMgmtOperationLog.OperationType.values()) {
            operationTypes.put(type.name(), type.getDescription());
        }
        return operationTypes;
    }

    /**
     * 获取操作结果选项
     * 
     * @return 操作结果选项映射
     */
    public Map<String, String> getOperationResults() {
        Map<String, String> operationResults = new HashMap<>();
        for (UserMgmtOperationLog.OperationResult result : UserMgmtOperationLog.OperationResult.values()) {
            operationResults.put(result.name(), result.getDescription());
        }
        return operationResults;
    }

    /**
     * 清理历史日志
     * 
     * @param beforeDays 保留天数
     * @return 清理结果
     */
    @Transactional
    public Map<String, Object> cleanupOperationLogs(Integer beforeDays) {
        try {
            logger.debug("清理历史操作日志 - beforeDays: {}", beforeDays);
            
            LocalDateTime beforeTime = LocalDateTime.now().minusDays(beforeDays);
            
            // 先查询要删除的记录数
            UserMgmtOperationLogQueryDTO queryDTO = new UserMgmtOperationLogQueryDTO();
            queryDTO.setEndTime(beforeTime);
            queryDTO.setSize(1);
            
            Specification<UserMgmtOperationLog> specification = buildOperationLogSpecification(queryDTO);
            long countToDelete = operationLogRepository.count(specification);
            
            // 执行删除
            operationLogRepository.deleteLogsBefore(beforeTime);
            
            Map<String, Object> result = new HashMap<>();
            result.put("cleanedCount", countToDelete);
            result.put("beforeTime", beforeTime);
            result.put("beforeDays", beforeDays);
            
            logger.info("清理历史操作日志成功 - cleanedCount: {}, beforeTime: {}", 
                    countToDelete, beforeTime);
            
            return result;
            
        } catch (Exception e) {
            logger.error("清理历史操作日志失败: {}", e.getMessage(), e);
            throw new RuntimeException("清理历史操作日志失败: " + e.getMessage());
        }
    }

    // ========== 私有辅助方法 ==========

    /**
     * 构建分页和排序参数
     */
    private Pageable buildPageable(UserMgmtOperationLogQueryDTO queryDTO) {
        // 构建排序
        Sort sort = Sort.by(
                "DESC".equalsIgnoreCase(queryDTO.getSortDirection()) 
                        ? Sort.Direction.DESC 
                        : Sort.Direction.ASC,
                queryDTO.getSortBy()
        );
        
        return PageRequest.of(queryDTO.getPageForSpringData(), queryDTO.getSize(), sort);
    }

    /**
     * 构建操作日志查询条件Specification
     */
    private Specification<UserMgmtOperationLog> buildOperationLogSpecification(UserMgmtOperationLogQueryDTO queryDTO) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            // 操作者ID筛选
            if (queryDTO.getOperatorId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("operatorId"), queryDTO.getOperatorId()));
            }
            
            // 操作者用户名筛选（模糊搜索）
            if (queryDTO.hasOperatorFilter() && queryDTO.getCleanOperatorName() != null) {
                String operatorName = "%" + queryDTO.getCleanOperatorName().toLowerCase() + "%";
                predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("operatorName")), operatorName));
            }
            
            // 目标用户ID筛选
            if (queryDTO.getTargetUserId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("targetUserId"), queryDTO.getTargetUserId()));
            }
            
            // 目标用户名筛选（模糊搜索）
            if (queryDTO.hasTargetUserFilter() && queryDTO.getCleanTargetUsername() != null) {
                String targetUsername = "%" + queryDTO.getCleanTargetUsername().toLowerCase() + "%";
                predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("targetUsername")), targetUsername));
            }
            
            // 操作类型筛选
            if (queryDTO.hasOperationTypeFilter()) {
                predicates.add(criteriaBuilder.equal(root.get("operationType"), queryDTO.getOperationType()));
            }
            
            // 操作结果筛选
            if (queryDTO.hasOperationResultFilter()) {
                predicates.add(criteriaBuilder.equal(root.get("operationResult"), queryDTO.getOperationResult()));
            }
            
            // 关键词搜索（操作描述）
            if (queryDTO.hasKeywordFilter()) {
                String keyword = "%" + queryDTO.getCleanKeyword().toLowerCase() + "%";
                predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("operationDesc")), keyword));
            }
            
            // IP地址筛选（支持多种比较方式）
            if (queryDTO.hasIpAddressFilter()) {
                String ipAddress = queryDTO.getCleanIpAddress();
                if (ipAddress != null) {
                    // 支持逗号分隔的多个IP（用于IN查询）
                    if (ipAddress.contains(",")) {
                        String[] ips = ipAddress.split(",");
                        List<String> ipList = Arrays.stream(ips)
                                .map(String::trim)
                                .collect(Collectors.toList());
                        predicates.add(root.get("ipAddress").in(ipList));
                    } else {
                        // 默认使用模糊搜索
                        String ipPattern = "%" + ipAddress.toLowerCase() + "%";
                        predicates.add(criteriaBuilder.like(
                                criteriaBuilder.lower(root.get("ipAddress")), ipPattern));
                    }
                }
            }
            
            // 请求URI筛选（支持多种比较方式）
            if (queryDTO.hasRequestUriFilter()) {
                String requestUri = queryDTO.getCleanRequestUri();
                if (requestUri != null) {
                    // 支持逗号分隔的多个URI（用于IN查询）
                    if (requestUri.contains(",")) {
                        String[] uris = requestUri.split(",");
                        List<String> uriList = Arrays.stream(uris)
                                .map(String::trim)
                                .collect(Collectors.toList());
                        predicates.add(root.get("requestUri").in(uriList));
                    } else {
                        // 默认使用模糊搜索
                        String uriPattern = "%" + requestUri.toLowerCase() + "%";
                        predicates.add(criteriaBuilder.like(
                                criteriaBuilder.lower(root.get("requestUri")), uriPattern));
                    }
                }
            }
            
            // 请求方法筛选（支持多种比较方式）
            if (queryDTO.hasRequestMethodFilter()) {
                String requestMethod = queryDTO.getCleanRequestMethod();
                if (requestMethod != null) {
                    // 支持逗号分隔的多个方法（用于IN查询）
                    if (requestMethod.contains(",")) {
                        String[] methods = requestMethod.split(",");
                        List<String> methodList = Arrays.stream(methods)
                                .map(String::trim)
                                .map(String::toUpperCase)
                                .collect(Collectors.toList());
                        predicates.add(root.get("requestMethod").in(methodList));
                    } else {
                        predicates.add(criteriaBuilder.equal(root.get("requestMethod"), requestMethod));
                    }
                }
            }
            
            // 时间范围筛选
            if (queryDTO.hasTimeRangeFilter()) {
                if (queryDTO.getStartTime() != null) {
                    predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createdAt"), queryDTO.getStartTime()));
                }
                if (queryDTO.getEndTime() != null) {
                    predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("createdAt"), queryDTO.getEndTime()));
                }
            }
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    // ========== 数据导出方法 ==========

    /**
     * 导出操作日志到文件
     * 
     * @param response HTTP响应对象
     * @param queryDTO 查询条件
     * @param format 导出格式（excel/csv）
     */
    public void exportOperationLogs(HttpServletResponse response, UserMgmtOperationLogQueryDTO queryDTO, String format) {
        try {
            logger.info("开始导出操作日志 - format: {}, 查询条件: {}", format, queryDTO);
            
            // 查询数据
            List<UserMgmtOperationLog> logs = queryLogsForExport(queryDTO);
            
            // 根据格式导出
            if ("csv".equalsIgnoreCase(format)) {
                exportToCSV(response, logs);
            } else {
                // 默认导出Excel格式
                exportToExcel(response, logs);
            }
            
            logger.info("操作日志导出完成 - 导出记录数: {}", logs.size());
            
        } catch (Exception e) {
            logger.error("导出操作日志失败: {}", e.getMessage(), e);
            throw new RuntimeException("导出操作日志失败: " + e.getMessage(), e);
        }
    }

    /**
     * 查询要导出的日志数据
     * 
     * @param queryDTO 查询条件
     * @return 日志列表
     */
    private List<UserMgmtOperationLog> queryLogsForExport(UserMgmtOperationLogQueryDTO queryDTO) {
        try {
            // 构建查询规格
            Specification<UserMgmtOperationLog> spec = buildOperationLogSpecification(queryDTO);
            
            // 构建排序
            Sort sort = Sort.by(Sort.Direction.DESC, "createdAt");
            
            // 执行查询（不分页，获取所有符合条件的记录）
            List<UserMgmtOperationLog> logs = operationLogRepository.findAll(spec, sort);
            
            logger.debug("查询到导出日志记录数: {}", logs.size());
            return logs;
            
        } catch (Exception e) {
            logger.error("查询导出日志数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("查询导出数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导出为CSV格式
     * 
     * @param response HTTP响应对象
     * @param logs 日志数据
     */
    private void exportToCSV(HttpServletResponse response, List<UserMgmtOperationLog> logs) throws IOException {
        // 设置响应头
        String fileName = "operation_logs_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".csv";
        response.setContentType("text/csv;charset=UTF-8");
        response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + 
                          URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        
        // 写入CSV数据
        try (PrintWriter writer = response.getWriter()) {
            // 写入BOM，确保Excel正确识别UTF-8编码
            writer.write('\ufeff');
            
            // 写入CSV头部
            writeCsvHeader(writer);
            
            // 写入数据行
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            for (UserMgmtOperationLog log : logs) {
                writeCsvRow(writer, log, formatter);
            }
            
            writer.flush();
        }
    }

    /**
     * 导出为Excel格式 (简化版，实际项目可使用Apache POI)
     * 
     * @param response HTTP响应对象
     * @param logs 日志数据
     */
    private void exportToExcel(HttpServletResponse response, List<UserMgmtOperationLog> logs) throws IOException {
        // 目前简化实现，导出为制表符分隔的文本文件，Excel可以正确识别
        String fileName = "operation_logs_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xls";
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + 
                          URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        
        try (PrintWriter writer = response.getWriter()) {
            // 写入BOM
            writer.write('\ufeff');
            
            // 写入Excel头部（制表符分隔）
            writeExcelHeader(writer);
            
            // 写入数据行
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            for (UserMgmtOperationLog log : logs) {
                writeExcelRow(writer, log, formatter);
            }
            
            writer.flush();
        }
    }

    /**
     * 写入CSV头部
     */
    private void writeCsvHeader(PrintWriter writer) {
        String[] headers = {
            "日志ID", "操作者ID", "操作者姓名", "目标用户ID", "目标用户名", 
            "操作类型", "操作描述", "操作结果", "IP地址", "请求方法", 
            "请求URI", "执行时间(ms)", "错误信息", "操作时间"
        };
        
        writer.println(String.join(",", Arrays.stream(headers)
                .map(this::escapeCsvValue)
                .toArray(String[]::new)));
    }

    /**
     * 写入CSV数据行
     */
    private void writeCsvRow(PrintWriter writer, UserMgmtOperationLog log, DateTimeFormatter formatter) {
        String[] values = {
            String.valueOf(log.getId()),
            String.valueOf(log.getOperatorId()),
            log.getOperatorName(),
            String.valueOf(log.getTargetUserId()),
            log.getTargetUsername(),
            getOperationTypeDescription(log.getOperationType()),
            log.getOperationDesc(),
            getOperationResultDescription(log.getOperationResult()),
            log.getIpAddress(),
            log.getRequestMethod(),
            log.getRequestUri(),
            log.getExecutionTime() != null ? String.valueOf(log.getExecutionTime()) : "",
            log.getErrorMessage(),
            log.getCreatedAt() != null ? log.getCreatedAt().format(formatter) : ""
        };
        
        writer.println(String.join(",", Arrays.stream(values)
                .map(v -> v != null ? escapeCsvValue(v) : "")
                .toArray(String[]::new)));
    }

    /**
     * 写入Excel头部
     */
    private void writeExcelHeader(PrintWriter writer) {
        String[] headers = {
            "日志ID", "操作者ID", "操作者姓名", "目标用户ID", "目标用户名", 
            "操作类型", "操作描述", "操作结果", "IP地址", "请求方法", 
            "请求URI", "执行时间(ms)", "错误信息", "操作时间"
        };
        
        writer.println(String.join("\t", headers));
    }

    /**
     * 写入Excel数据行
     */
    private void writeExcelRow(PrintWriter writer, UserMgmtOperationLog log, DateTimeFormatter formatter) {
        String[] values = {
            String.valueOf(log.getId()),
            String.valueOf(log.getOperatorId()),
            log.getOperatorName(),
            String.valueOf(log.getTargetUserId()),
            log.getTargetUsername(),
            getOperationTypeDescription(log.getOperationType()),
            log.getOperationDesc(),
            getOperationResultDescription(log.getOperationResult()),
            log.getIpAddress(),
            log.getRequestMethod(),
            log.getRequestUri(),
            log.getExecutionTime() != null ? String.valueOf(log.getExecutionTime()) : "",
            log.getErrorMessage(),
            log.getCreatedAt() != null ? log.getCreatedAt().format(formatter) : ""
        };
        
        writer.println(String.join("\t", Arrays.stream(values)
                .map(v -> v != null ? v.replace("\t", " ").replace("\r\n", " ").replace("\n", " ") : "")
                .toArray(String[]::new)));
    }

    /**
     * CSV值转义处理
     */
    private String escapeCsvValue(String value) {
        if (value == null) return "";
        
        // 如果包含逗号、双引号或换行符，需要用双引号包围，并转义内部双引号
        if (value.contains(",") || value.contains("\"") || value.contains("\n") || value.contains("\r")) {
            return "\"" + value.replace("\"", "\"\"") + "\"";
        }
        return value;
    }

    /**
     * 获取操作类型描述
     */
    private String getOperationTypeDescription(UserMgmtOperationLog.OperationType operationType) {
        return operationType != null ? operationType.getDescription() : "";
    }

    /**
     * 获取操作结果描述
     */
    private String getOperationResultDescription(UserMgmtOperationLog.OperationResult operationResult) {
        return operationResult != null ? operationResult.getDescription() : "";
    }

    /**
     * 获取操作日志统计数据（带缓存）
     * 
     * @param days 统计天数
     * @return 统计数据
     */
    @Cacheable(value = "operation-stats", key = "'stats:' + #days", cacheManager = "cacheManager")
    public Map<String, Object> getOperationStats(Integer days) {
        try {
            logger.debug("获取操作日志统计数据 - days: {}", days);
            
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusDays(days);
            
            Map<String, Object> stats = new HashMap<>();
            
            // 基础统计
            Long totalOperations = operationLogRepository.countOperationsByTimeRange(startTime, endTime);
            stats.put("totalOperations", totalOperations);
            
            // 操作结果统计
            Map<String, Long> resultStats = new HashMap<>();
            resultStats.put("SUCCESS", operationLogRepository.countByOperationResult(
                    UserMgmtOperationLog.OperationResult.SUCCESS));
            resultStats.put("FAILED", operationLogRepository.countByOperationResult(
                    UserMgmtOperationLog.OperationResult.FAILED));
            resultStats.put("PARTIAL_SUCCESS", operationLogRepository.countByOperationResult(
                    UserMgmtOperationLog.OperationResult.PARTIAL_SUCCESS));
            stats.put("operationResultStats", resultStats);
            
            // 操作类型统计
            List<Object[]> typeStats = operationLogRepository.countOperationsByType(startTime, endTime);
            stats.put("operationTypeStats", typeStats);
            
            logger.debug("操作日志统计数据获取完成 - days: {}, totalOperations: {}", days, totalOperations);
            return stats;
            
        } catch (Exception e) {
            logger.error("获取操作日志统计数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取统计数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 用户行为分析统计（带缓存）
     * 
     * @param days 统计天数
     * @param operatorId 指定操作者ID（可选）
     * @return 用户行为分析结果
     */
    @Cacheable(value = "behavior-analysis", 
               key = "'behavior:' + #days + ':' + (#operatorId != null ? #operatorId : 'all')", 
               cacheManager = "cacheManager")
    public Map<String, Object> getUserBehaviorAnalysis(Integer days, Long operatorId) {
        try {
            logger.info("开始用户行为分析 - days: {}, operatorId: {}", days, operatorId);
            
            LocalDateTime endTime = LocalDateTime.now().plusHours(1); // 加1小时确保包含当前时间
            LocalDateTime startTime = endTime.minusDays(days).withHour(0).withMinute(0).withSecond(0).withNano(0); // 确保从那天的00:00:00开始
            
            logger.info("查询时间范围: {} 到 {}", startTime, endTime);
            
            Map<String, Object> result = new HashMap<>();
            
            // 基础统计数据
            Map<String, Object> basicStats = getBasicOperationStats(startTime, endTime, operatorId);
            result.put("basicStats", basicStats);
            logger.info("基础统计数据: {}", basicStats);
            
            // 操作类型分布
            List<Object[]> operationTypeStats = getOperationTypeDistribution(startTime, endTime, operatorId);
            result.put("operationTypeDistribution", operationTypeStats);
            logger.info("操作类型分布数据量: {}", operationTypeStats.size());
            
            // 操作结果分布
            Map<String, Long> operationResultStats = getOperationResultDistribution(startTime, endTime, operatorId);
            result.put("operationResultDistribution", operationResultStats);
            logger.info("操作结果分布: {}", operationResultStats);
            
            // 每日操作趋势
            List<Object[]> dailyTrend = getDailyOperationTrend(startTime, endTime, operatorId);
            result.put("dailyOperationTrend", dailyTrend);
            logger.info("每日趋势数据量: {}", dailyTrend.size());
            
            // 小时分布热力图
            List<Object[]> hourlyDistribution = getHourlyOperationDistribution(startTime, endTime, operatorId);
            result.put("hourlyDistribution", hourlyDistribution);
            
            // 高频操作用户TOP10（如果未指定operatorId）
            if (operatorId == null) {
                List<Object[]> topOperators = getTopOperators(startTime, endTime, 10);
                result.put("topOperators", topOperators);
                logger.info("TOP操作者数据量: {}", topOperators.size());
            }
            
            // 异常操作检测
            List<Object[]> suspiciousActivities = getSuspiciousActivities(startTime, endTime, operatorId);
            result.put("suspiciousActivities", suspiciousActivities);
            
            logger.debug("用户行为分析完成 - days: {}, operatorId: {}", days, operatorId);
            return result;
            
        } catch (Exception e) {
            logger.error("用户行为分析失败: {}", e.getMessage(), e);
            throw new RuntimeException("用户行为分析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 用户行为分析统计（不使用缓存）
     * 
     * @param days 统计天数
     * @param operatorId 指定操作者ID（可选）
     * @return 用户行为分析结果
     */
    public Map<String, Object> getUserBehaviorAnalysisNoCache(Integer days, Long operatorId) {
        try {
            logger.info("开始用户行为分析（无缓存） - days: {}, operatorId: {}", days, operatorId);
            
            LocalDateTime endTime = LocalDateTime.now().plusHours(1); // 加1小时确保包含当前时间
            LocalDateTime startTime = endTime.minusDays(days).withHour(0).withMinute(0).withSecond(0).withNano(0); // 确保从那天的00:00:00开始
            
            logger.info("查询时间范围: {} 到 {}", startTime, endTime);
            
            Map<String, Object> result = new HashMap<>();
            
            // 基础统计数据
            Map<String, Object> basicStats = getBasicOperationStats(startTime, endTime, operatorId);
            result.put("basicStats", basicStats);
            logger.info("基础统计数据: {}", basicStats);
            
            // 操作类型分布
            List<Object[]> operationTypeStats = getOperationTypeDistribution(startTime, endTime, operatorId);
            result.put("operationTypeDistribution", operationTypeStats);
            logger.info("操作类型分布数据量: {}", operationTypeStats.size());
            
            // 操作结果分布
            Map<String, Long> operationResultStats = getOperationResultDistribution(startTime, endTime, operatorId);
            result.put("operationResultDistribution", operationResultStats);
            logger.info("操作结果分布: {}", operationResultStats);
            
            // 每日操作趋势
            List<Object[]> dailyTrend = getDailyOperationTrend(startTime, endTime, operatorId);
            result.put("dailyOperationTrend", dailyTrend);
            logger.info("每日趋势数据量: {}", dailyTrend.size());
            
            // 小时分布热力图
            List<Object[]> hourlyDistribution = getHourlyOperationDistribution(startTime, endTime, operatorId);
            result.put("hourlyDistribution", hourlyDistribution);
            
            // 高频操作用户TOP10（如果未指定operatorId）
            if (operatorId == null) {
                List<Object[]> topOperators = getTopOperators(startTime, endTime, 10);
                result.put("topOperators", topOperators);
                logger.info("TOP操作者数据量: {}", topOperators.size());
            }
            
            // 异常操作检测
            List<Object[]> suspiciousActivities = getSuspiciousActivities(startTime, endTime, operatorId);
            result.put("suspiciousActivities", suspiciousActivities);
            
            logger.debug("用户行为分析完成（无缓存） - days: {}, operatorId: {}", days, operatorId);
            return result;
            
        } catch (Exception e) {
            logger.error("用户行为分析失败（无缓存）: {}", e.getMessage(), e);
            throw new RuntimeException("用户行为分析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 活跃用户分析（带缓存）
     * 
     * @param days 统计天数
     * @param limit 返回用户数量限制
     * @return 活跃用户分析结果
     */
    @Cacheable(value = "active-users", 
               key = "'active:' + #days + ':' + #limit", 
               cacheManager = "cacheManager")
    public Map<String, Object> getActiveUsersAnalysis(Integer days, Integer limit) {
        try {
            logger.info("开始活跃用户分析 - days: {}, limit: {}", days, limit);
            
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusDays(days);
            
            Map<String, Object> result = new HashMap<>();
            
            // 最活跃用户排行
            Pageable pageable = PageRequest.of(0, limit);
            List<Object[]> activeOperators = operationLogRepository.findActiveOperators(startTime, pageable);
            result.put("activeOperators", activeOperators);
            
            // 最常被操作的用户
            List<Object[]> mostOperatedUsers = operationLogRepository.findMostOperatedUsers(startTime, pageable);
            result.put("mostOperatedUsers", mostOperatedUsers);
            
            // 新增活跃用户（第一次操作在时间范围内的用户）
            List<Object[]> newActiveUsers = getNewActiveUsers(startTime, endTime, limit);
            result.put("newActiveUsers", newActiveUsers);
            
            // 用户活跃度等级分布
            Map<String, Long> activityLevelDistribution = getUserActivityLevelDistribution(startTime, endTime);
            result.put("activityLevelDistribution", activityLevelDistribution);
            
            logger.info("活跃用户分析完成 - days: {}, limit: {}", days, limit);
            return result;
            
        } catch (Exception e) {
            logger.error("活跃用户分析失败: {}", e.getMessage(), e);
            throw new RuntimeException("活跃用户分析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 用户行为分析统计（按指定日期范围，不使用缓存）
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param operatorId 指定操作者ID（可选）
     * @return 用户行为分析结果
     */
    public Map<String, Object> getUserBehaviorAnalysisByDateRange(LocalDateTime startTime, LocalDateTime endTime, Long operatorId) {
        try {
            logger.info("开始用户行为分析（按日期范围） - startTime: {}, endTime: {}, operatorId: {}", startTime, endTime, operatorId);
            
            Map<String, Object> result = new HashMap<>();
            
            // 基础统计数据
            Map<String, Object> basicStats = getBasicOperationStats(startTime, endTime, operatorId);
            result.put("basicStats", basicStats);
            logger.info("基础统计数据: {}", basicStats);
            
            // 操作类型分布
            List<Object[]> operationTypeStats = getOperationTypeDistribution(startTime, endTime, operatorId);
            result.put("operationTypeDistribution", operationTypeStats);
            logger.info("操作类型分布数据量: {}", operationTypeStats.size());
            
            // 操作结果分布
            Map<String, Long> operationResultStats = getOperationResultDistribution(startTime, endTime, operatorId);
            result.put("operationResultDistribution", operationResultStats);
            logger.info("操作结果分布: {}", operationResultStats);
            
            // 每日操作趋势
            List<Object[]> dailyTrend = getDailyOperationTrend(startTime, endTime, operatorId);
            result.put("dailyOperationTrend", dailyTrend);
            logger.info("每日趋势数据量: {}", dailyTrend.size());
            
            // 小时分布热力图
            List<Object[]> hourlyDistribution = getHourlyOperationDistribution(startTime, endTime, operatorId);
            result.put("hourlyDistribution", hourlyDistribution);
            
            // 高频操作用户TOP10（如果未指定operatorId）
            if (operatorId == null) {
                List<Object[]> topOperators = getTopOperators(startTime, endTime, 10);
                result.put("topOperators", topOperators);
                logger.info("TOP操作者数据量: {}", topOperators.size());
            }
            
            // 异常操作检测
            List<Object[]> suspiciousActivities = getSuspiciousActivities(startTime, endTime, operatorId);
            result.put("suspiciousActivities", suspiciousActivities);
            
            logger.debug("用户行为分析完成（按日期范围） - startTime: {}, endTime: {}, operatorId: {}", startTime, endTime, operatorId);
            return result;
            
        } catch (Exception e) {
            logger.error("用户行为分析失败（按日期范围）: {}", e.getMessage(), e);
            throw new RuntimeException("用户行为分析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 操作热力图数据
     * 
     * @param days 统计天数
     * @return 操作热力图数据
     */
    public Map<String, Object> getOperationHeatmap(Integer days) {
        try {
            logger.info("开始生成操作热力图数据 - days: {}", days);
            
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusDays(days);
            
            Map<String, Object> result = new HashMap<>();
            
            // 按小时和星期几的操作分布（7*24的热力图）
            List<Object[]> weeklyHourlyDistribution = getWeeklyHourlyDistribution(startTime, endTime);
            result.put("weeklyHourlyDistribution", weeklyHourlyDistribution);
            
            // 按日期的操作分布
            List<Object[]> dailyDistribution = operationLogRepository.countOperationsByDate(startTime, endTime);
            result.put("dailyDistribution", dailyDistribution);
            
            // 操作类型热力图
            Map<String, List<Object[]>> operationTypeHeatmap = getOperationTypeHeatmap(startTime, endTime);
            result.put("operationTypeHeatmap", operationTypeHeatmap);
            
            logger.info("操作热力图数据生成完成 - days: {}", days);
            return result;
            
        } catch (Exception e) {
            logger.error("操作热力图数据生成失败: {}", e.getMessage(), e);
            throw new RuntimeException("操作热力图数据生成失败: " + e.getMessage(), e);
        }
    }

    // ========== 私有辅助方法 ==========

    /**
     * 获取基础操作统计
     */
    private Map<String, Object> getBasicOperationStats(LocalDateTime startTime, LocalDateTime endTime, Long operatorId) {
        Map<String, Object> stats = new HashMap<>();
        
        // 总操作数
        Long totalOperations = operationLogRepository.countOperationsByTimeRange(startTime, endTime);
        stats.put("totalOperations", totalOperations);
        
        // 成功操作数
        Long successOperations = operationLogRepository.countOperationsByResultAndTimeRange(
                UserMgmtOperationLog.OperationResult.SUCCESS, startTime, endTime);
        stats.put("successOperations", successOperations);
        
        // 失败操作数
        Long failedOperations = operationLogRepository.countOperationsByResultAndTimeRange(
                UserMgmtOperationLog.OperationResult.FAILED, startTime, endTime);
        stats.put("failedOperations", failedOperations);
        
        // 成功率
        Double successRate = totalOperations > 0 ? (double) successOperations / totalOperations * 100 : 0.0;
        stats.put("successRate", Math.round(successRate * 100.0) / 100.0);
        
        // 平均每日操作数
        long daysDiff = java.time.temporal.ChronoUnit.DAYS.between(startTime.toLocalDate(), endTime.toLocalDate());
        Double avgDailyOperations = daysDiff > 0 ? (double) totalOperations / daysDiff : totalOperations.doubleValue();
        stats.put("avgDailyOperations", Math.round(avgDailyOperations * 100.0) / 100.0);
        
        return stats;
    }

    /**
     * 获取操作类型分布
     */
    private List<Object[]> getOperationTypeDistribution(LocalDateTime startTime, LocalDateTime endTime, Long operatorId) {
        if (operatorId != null) {
            // 如果指定了操作者，需要自定义查询
            return operationLogRepository.countOperationsByType(startTime, endTime);
        } else {
            return operationLogRepository.countOperationsByType(startTime, endTime);
        }
    }

    /**
     * 获取操作结果分布
     */
    private Map<String, Long> getOperationResultDistribution(LocalDateTime startTime, LocalDateTime endTime, Long operatorId) {
        Map<String, Long> distribution = new HashMap<>();
        distribution.put("SUCCESS", operationLogRepository.countOperationsByResultAndTimeRange(
            UserMgmtOperationLog.OperationResult.SUCCESS, startTime, endTime));
        distribution.put("FAILED", operationLogRepository.countOperationsByResultAndTimeRange(
            UserMgmtOperationLog.OperationResult.FAILED, startTime, endTime));
        distribution.put("PARTIAL_SUCCESS", operationLogRepository.countOperationsByResultAndTimeRange(
            UserMgmtOperationLog.OperationResult.PARTIAL_SUCCESS, startTime, endTime));
        return distribution;
    }

    /**
     * 获取每日操作趋势
     */
    private List<Object[]> getDailyOperationTrend(LocalDateTime startTime, LocalDateTime endTime, Long operatorId) {
        return operationLogRepository.countOperationsByDate(startTime, endTime);
    }

    /**
     * 获取小时分布
     */
    private List<Object[]> getHourlyOperationDistribution(LocalDateTime startTime, LocalDateTime endTime, Long operatorId) {
        try {
            logger.info("获取小时分布数据 - startTime: {}, endTime: {}", startTime, endTime);
            
            // 调用Repository获取按小时统计的数据
            List<Object[]> hourlyStats = operationLogRepository.countOperationsByHour(startTime, endTime);
            logger.info("小时统计查询结果数量: {}", hourlyStats.size());
            
            // 确保所有24小时都有数据（没有操作的小时显示为0）
            List<Object[]> result = new ArrayList<>();
            Map<Integer, Long> hourlyMap = new HashMap<>();
            
            // 将查询结果放入Map中
            for (Object[] row : hourlyStats) {
                Integer hour = (Integer) row[0];
                Long count = (Long) row[1];
                hourlyMap.put(hour, count);
                logger.debug("小时统计 - 小时: {}, 操作数: {}", hour, count);
            }
            
            // 填充所有24小时的数据
            for (int hour = 0; hour < 24; hour++) {
                Long count = hourlyMap.getOrDefault(hour, 0L);
                result.add(new Object[]{hour, count});
            }
            
            logger.info("处理后的小时分布数据量: {}", result.size());
            return result;
            
        } catch (Exception e) {
            logger.error("获取小时分布数据失败: {}", e.getMessage(), e);
            // 返回默认的24小时数据（全为0）
            List<Object[]> defaultResult = new ArrayList<>();
            for (int hour = 0; hour < 24; hour++) {
                defaultResult.add(new Object[]{hour, 0L});
            }
            return defaultResult;
        }
    }

    /**
     * 获取TOP操作者
     */
    private List<Object[]> getTopOperators(LocalDateTime startTime, LocalDateTime endTime, Integer limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return operationLogRepository.findActiveOperators(startTime, pageable);
    }

    /**
     * 获取可疑活动
     */
    private List<Object[]> getSuspiciousActivities(LocalDateTime startTime, LocalDateTime endTime, Long operatorId) {
        // 查找短时间内大量操作的情况
        List<UserMgmtOperationLog.OperationType> sensitiveOperations = Arrays.asList(
                UserMgmtOperationLog.OperationType.BATCH_ENABLE_USERS,
                UserMgmtOperationLog.OperationType.BATCH_DISABLE_USERS,
                UserMgmtOperationLog.OperationType.BATCH_UPGRADE_VIP,
                UserMgmtOperationLog.OperationType.BATCH_DOWNGRADE_BASIC
        );
        
        return operationLogRepository.findSuspiciousActivity(startTime, sensitiveOperations, 10L);
    }

    /**
     * 获取新活跃用户
     */
    private List<Object[]> getNewActiveUsers(LocalDateTime startTime, LocalDateTime endTime, Integer limit) {
        // 简化实现，返回最近活跃的用户
        Pageable pageable = PageRequest.of(0, limit);
        return operationLogRepository.findActiveOperators(startTime, pageable);
    }

    /**
     * 获取用户活跃度等级分布
     */
    private Map<String, Long> getUserActivityLevelDistribution(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            logger.info("计算用户活跃度等级分布 - startTime: {}, endTime: {}", startTime, endTime);
            
            // 获取所有操作者的操作统计
            List<Object[]> operatorStats = operationLogRepository.countOperationsByOperator(startTime, endTime);
            logger.info("操作者统计数据量: {}", operatorStats.size());
            
            Map<String, Long> distribution = new HashMap<>();
            distribution.put("高活跃度(>100次)", 0L);
            distribution.put("中活跃度(20-100次)", 0L);
            distribution.put("低活跃度(1-20次)", 0L);
            
            // 统计每个活跃度等级的用户数量
            for (Object[] row : operatorStats) {
                Long operationCount = (Long) row[2]; // 操作次数
                
                if (operationCount > 100) {
                    distribution.put("高活跃度(>100次)", distribution.get("高活跃度(>100次)") + 1);
                } else if (operationCount >= 20) {
                    distribution.put("中活跃度(20-100次)", distribution.get("中活跃度(20-100次)") + 1);
                } else if (operationCount >= 1) {
                    distribution.put("低活跃度(1-20次)", distribution.get("低活跃度(1-20次)") + 1);
                }
            }
            
            logger.info("用户活跃度分布: {}", distribution);
            return distribution;
            
        } catch (Exception e) {
            logger.error("计算用户活跃度等级分布失败: {}", e.getMessage(), e);
            // 返回默认分布
            Map<String, Long> defaultDistribution = new HashMap<>();
            defaultDistribution.put("高活跃度(>100次)", 0L);
            defaultDistribution.put("中活跃度(20-100次)", 0L);
            defaultDistribution.put("低活跃度(1-20次)", 0L);
            return defaultDistribution;
        }
    }

    /**
     * 获取按星期和小时的分布
     */
    private List<Object[]> getWeeklyHourlyDistribution(LocalDateTime startTime, LocalDateTime endTime) {
        // 简化实现
        return operationLogRepository.countOperationsByDate(startTime, endTime);
    }

    /**
     * 获取操作类型热力图
     */
    private Map<String, List<Object[]>> getOperationTypeHeatmap(LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, List<Object[]>> heatmap = new HashMap<>();
        List<Object[]> typeStats = operationLogRepository.countOperationsByType(startTime, endTime);
        heatmap.put("operationTypes", typeStats);
        return heatmap;
    }
}