# SysMonitor系统监控模块 - 详细开发规划文档

## 📋 目录
- [项目概述](#项目概述)
- [开发阶段规划](#开发阶段规划)
- [文件结构设计](#文件结构设计)
- [开发任务清单](#开发任务清单)
- [API接口设计](#API接口设计)
- [数据库设计](#数据库设计)
- [前端组件设计](#前端组件设计)
- [测试计划](#测试计划)
- [风险控制](#风险控制)
- [交付标准](#交付标准)

---

## 🎯 项目概述

### 开发目标
基于现有RickPan架构，开发独立的系统监控页面，为管理员提供实时的系统资源监控和历史数据分析功能。

### 遵循原则
1. **最小改动原则** - 不修改公共文件，主动适配现有架构
2. **规划驱动开发** - 严格按照本规划文档执行
3. **用户测试** - 开发完成后由用户进行编译和测试
4. **图标标准化** - 使用element-plus图标前通过context7 mcp服务确认

### 统一命名规范
- **前缀标识**：所有文件和组件使用 `SysMonitor` 前缀
- **文件命名**：采用PascalCase命名法 (SysMonitorXxx.vue)
- **API路径**：`/api/sys-monitor/*`
- **数据库表**：`sys_monitor_*`

---

## 📅 开发阶段规划

### 阶段一：基础架构搭建 (第1周)
**时间安排：5个工作日**  
**主要目标：**搭建后端监控数据采集和API基础架构

#### Day 1: 数据库设计与JMX研究
- ✅ **数据库表结构设计**
- ✅ **JMX API调研和测试**
- ✅ **数据模型类设计**

#### Day 2: 后端基础服务开发
- ✅ **SysMonitorService核心业务逻辑**
- ✅ **数据采集器SysMonitorCollector**
- ✅ **单元测试编写**

#### Day 3: API控制器和定时任务
- ✅ **SysMonitorController API接口**
- ✅ **定时任务配置和实现**
- ✅ **异常处理和日志记录**

#### Day 4: 前端API模块和路由
- ✅ **前端API模块sysMonitor.ts**
- ✅ **路由配置和权限控制**
- ✅ **TypeScript类型定义**

#### Day 5: 基础页面框架搭建
- ✅ **SysMonitorDashboard.vue主页面**
- ✅ **基础布局和导航集成**
- ✅ **权限验证和错误处理**

**交付物：**
- 完整的后端API接口
- 基础的前端页面框架
- 数据库表结构和初始数据
- 单元测试用例

### 阶段二：核心功能实现 (第2周)  
**时间安排：5个工作日**
**主要目标：**实现实时监控和数据可视化功能

#### Day 1: 实时监控组件开发
- ✅ **SysMonitorRealtime.vue实时监控组件**
- ✅ **监控卡片组件设计**
- ✅ **数据获取和状态管理**

#### Day 2: 图表组件集成
- ✅ **SysMonitorCharts.vue图表组件**
- ✅ **ECharts集成和配置**
- ✅ **实时数据更新逻辑**

#### Day 3: 历史数据分析
- ✅ **SysMonitorHistory.vue历史数据组件**
- ✅ **时间范围选择器**
- ✅ **历史数据查询和展示**

#### Day 4: 系统信息展示
- ✅ **系统信息卡片组件**
- ✅ **JVM详细信息展示**
- ✅ **性能指标统计**

#### Day 5: 响应式设计和优化
- ✅ **移动端适配**
- ✅ **界面优化和交互完善**
- ✅ **性能优化**

**交付物：**
- 完整的实时监控功能
- 历史数据分析功能
- 响应式界面设计
- 图表可视化组件

### 阶段三：高级功能和测试 (第3周)
**时间安排：5个工作日**  
**主要目标：**完善告警功能、数据导出和全面测试

#### Day 1: 告警功能开发
- ✅ **SysMonitorAlerts.vue告警组件**
- ✅ **告警配置管理**
- ✅ **实时告警检测**

#### Day 2: 数据导出功能
- ✅ **Excel/CSV数据导出**
- ✅ **监控报告生成**
- ✅ **文件下载处理**

#### Day 3: 功能测试和调优
- ✅ **功能完整性测试**
- ✅ **性能测试和优化**
- ✅ **兼容性测试**

#### Day 4: 集成测试
- ✅ **与现有系统集成测试**
- ✅ **权限控制测试**
- ✅ **数据一致性验证**

#### Day 5: 文档整理和部署准备
- ✅ **技术文档完善**
- ✅ **用户手册编写**
- ✅ **部署指南制作**

**交付物：**
- 完整的系统监控模块
- 全面的测试报告
- 完整的技术文档
- 部署和使用指南

---

## 📁 文件结构设计

### 后端文件结构
```
rickpan-backend/src/main/java/com/rickpan/
├── controller/
│   └── SysMonitorController.java          # 系统监控API控制器
├── service/
│   ├── SysMonitorService.java             # 监控数据业务逻辑
│   └── SysMonitorCollectorService.java    # 数据采集服务
├── entity/
│   ├── SysMonitorRealtime.java            # 实时监控数据实体
│   ├── SysMonitorHistory.java             # 历史监控数据实体
│   ├── SysMonitorAlertConfig.java         # 告警配置实体
│   └── SysMonitorAlertLog.java            # 告警日志实体
├── repository/
│   ├── SysMonitorRealtimeRepository.java  # 实时数据访问层
│   ├── SysMonitorHistoryRepository.java   # 历史数据访问层
│   ├── SysMonitorAlertConfigRepository.java # 告警配置访问层
│   └── SysMonitorAlertLogRepository.java  # 告警日志访问层
├── dto/
│   ├── SysMonitorDataDTO.java             # 监控数据传输对象
│   ├── SysMonitorQueryDTO.java            # 查询参数对象
│   └── SysMonitorAlertDTO.java            # 告警数据对象
├── config/
│   └── SysMonitorConfig.java              # 监控模块配置类
└── task/
    └── SysMonitorTask.java                # 定时任务配置
```

### 前端文件结构
```
rickpan-frontend/src/
├── views/dashboard/admin/
│   └── SysMonitorDashboard.vue            # 系统监控主页面
├── components/sysMonitor/
│   ├── SysMonitorRealtime.vue             # 实时监控组件
│   ├── SysMonitorCharts.vue              # 图表组件
│   ├── SysMonitorHistory.vue             # 历史数据组件
│   ├── SysMonitorAlerts.vue              # 告警管理组件
│   ├── SysMonitorSystemInfo.vue          # 系统信息组件
│   └── SysMonitorExport.vue              # 数据导出组件
├── api/modules/
│   └── sysMonitor.ts                     # 监控API接口模块
├── types/
│   └── sysMonitor.ts                     # 监控相关类型定义
├── stores/
│   └── sysMonitor.ts                     # 监控数据状态管理(可选)
└── composables/
    ├── useSysMonitorData.ts              # 数据获取组合函数
    ├── useSysMonitorCharts.ts            # 图表处理组合函数
    └── useSysMonitorAlerts.ts            # 告警处理组合函数
```

### 数据库表文件
```
docs/database/
└── sys_monitor_tables.sql                # 监控模块数据库表结构
```

---

## 📋 开发任务清单

### 🔧 后端开发任务

#### 数据库设计任务
- [ ] **T1.1** 设计sys_monitor_realtime表结构
- [ ] **T1.2** 设计sys_monitor_history表结构  
- [ ] **T1.3** 设计sys_monitor_alert_config表结构
- [ ] **T1.4** 设计sys_monitor_alert_log表结构
- [ ] **T1.5** 创建数据库索引和分区策略
- [ ] **T1.6** 编写初始化SQL脚本

#### 实体类开发任务
- [ ] **T2.1** SysMonitorRealtime.java - 实时数据实体
- [ ] **T2.2** SysMonitorHistory.java - 历史数据实体
- [ ] **T2.3** SysMonitorAlertConfig.java - 告警配置实体
- [ ] **T2.4** SysMonitorAlertLog.java - 告警日志实体
- [ ] **T2.5** 相关DTO类定义

#### 数据访问层任务
- [ ] **T3.1** SysMonitorRealtimeRepository.java
- [ ] **T3.2** SysMonitorHistoryRepository.java
- [ ] **T3.3** SysMonitorAlertConfigRepository.java
- [ ] **T3.4** SysMonitorAlertLogRepository.java
- [ ] **T3.5** 自定义查询方法实现

#### 业务逻辑层任务
- [ ] **T4.1** SysMonitorService.java - 核心业务逻辑
  - JMX数据采集方法
  - 数据格式化和计算
  - 历史数据查询
  - 告警阈值检查
- [ ] **T4.2** SysMonitorCollectorService.java - 数据采集服务
  - 定时数据采集
  - 数据存储逻辑
  - 异常处理机制

#### 控制器层任务
- [ ] **T5.1** SysMonitorController.java - API接口
  - GET /api/sys-monitor/realtime - 获取实时数据
  - GET /api/sys-monitor/history - 获取历史数据
  - GET /api/sys-monitor/system-info - 获取系统信息
  - GET/POST /api/sys-monitor/alert/config - 告警配置管理
  - GET /api/sys-monitor/alert/logs - 获取告警日志
  - POST /api/sys-monitor/export - 数据导出

#### 配置和任务
- [ ] **T6.1** SysMonitorConfig.java - 配置类
- [ ] **T6.2** SysMonitorTask.java - 定时任务
- [ ] **T6.3** 异常处理和日志配置

### 🎨 前端开发任务

#### API接口集成
- [ ] **F1.1** sysMonitor.ts - API接口模块
  - 实时数据获取接口
  - 历史数据查询接口
  - 告警配置接口
  - 数据导出接口
- [ ] **F1.2** sysMonitor.ts - TypeScript类型定义
  - 监控数据类型
  - 查询参数类型
  - 告警配置类型

#### 主页面开发
- [ ] **F2.1** SysMonitorDashboard.vue - 主页面框架
  - 页面布局设计
  - 导航集成
  - 权限验证
  - 响应式设计

#### 核心组件开发
- [ ] **F3.1** SysMonitorRealtime.vue - 实时监控组件
  - CPU/内存/磁盘/JVM监控卡片
  - 仪表盘样式设计
  - 实时数据更新
  - 颜色状态指示
- [ ] **F3.2** SysMonitorCharts.vue - 图表组件  
  - ECharts集成配置
  - 实时趋势图表
  - 图表交互功能
  - 数据更新动画
- [ ] **F3.3** SysMonitorHistory.vue - 历史数据组件
  - 时间范围选择器
  - 历史数据图表
  - 数据对比功能
  - 导出功能集成
- [ ] **F3.4** SysMonitorSystemInfo.vue - 系统信息组件
  - 系统基础信息展示
  - JVM详细信息
  - 运行时统计
  - 版本信息显示
- [ ] **F3.5** SysMonitorAlerts.vue - 告警管理组件
  - 告警配置界面
  - 实时告警显示
  - 告警历史查看
  - 告警统计

#### 辅助功能组件
- [ ] **F4.1** SysMonitorExport.vue - 数据导出组件
- [ ] **F4.2** 组合式函数开发
  - useSysMonitorData.ts
  - useSysMonitorCharts.ts  
  - useSysMonitorAlerts.ts

#### 路由和权限集成
- [ ] **F5.1** 路由配置更新
- [ ] **F5.2** 菜单项添加
- [ ] **F5.3** 权限控制集成

### 🧪 测试任务

#### 后端测试
- [ ] **Test1** 单元测试编写
  - Service层业务逻辑测试
  - Repository层数据访问测试
  - Controller层API接口测试
- [ ] **Test2** 集成测试
  - 数据采集功能测试
  - API接口完整性测试
  - 数据库操作测试
- [ ] **Test3** 性能测试
  - JMX API性能测试
  - 数据库查询性能测试
  - 并发访问测试

#### 前端测试
- [ ] **Test4** 组件测试
  - 核心组件单元测试
  - 图表渲染测试
  - 数据绑定测试
- [ ] **Test5** 功能测试
  - 用户交互测试
  - 数据更新测试
  - 响应式布局测试
- [ ] **Test6** 兼容性测试
  - 多浏览器测试
  - 移动端适配测试
  - 性能基准测试

---

## 🔌 API接口设计

### 基础接口规范
**基础路径：** `/api/sys-monitor`  
**认证要求：** 需要ADMIN角色权限  
**响应格式：** 统一使用ResponseDetails包装

### 核心API接口

#### 1. 获取实时监控数据
```http
GET /api/sys-monitor/realtime

Response:
{
  "status": 200,
  "message": "success",
  "data": {
    "timestamp": "2025-01-15T10:30:00",
    "cpu": {
      "usage": 45.5,
      "cores": 8,
      "loadAverage": [1.2, 1.5, 1.8]
    },
    "memory": {
      "total": 17179869184,
      "used": 8589934592,
      "free": 8589934592,
      "usage": 50.0
    },
    "disk": {
      "total": 536870912000,
      "used": 429496729600,
      "available": 107374182400,
      "usage": 80.0
    },
    "jvm": {
      "heapUsed": 536870912,
      "heapMax": 2147483648,
      "heapUsage": 25.0,
      "nonHeapUsed": 104857600,
      "nonHeapMax": 268435456
    },
    "gc": {
      "count": 25,
      "time": 150,
      "avgTime": 6.0
    },
    "threads": {
      "active": 45,
      "peak": 50,
      "daemon": 20
    }
  }
}
```

#### 2. 获取历史监控数据
```http
GET /api/sys-monitor/history?timeRange=1h&metrics=cpu,memory

Parameters:
- timeRange: 时间范围 (1h/6h/24h/7d/30d)
- metrics: 指标类型 (cpu,memory,disk,jvm)
- startTime: 开始时间 (可选)
- endTime: 结束时间 (可选)

Response:
{
  "status": 200,
  "message": "success", 
  "data": {
    "timeRange": "1h",
    "interval": "5m",
    "dataPoints": [
      {
        "timestamp": "2025-01-15T09:30:00",
        "cpu": 42.5,
        "memory": 48.2,
        "disk": 79.8,
        "jvm": 24.5
      }
      // ... more data points
    ]
  }
}
```

#### 3. 获取系统信息
```http
GET /api/sys-monitor/system-info

Response:
{
  "status": 200,
  "message": "success",
  "data": {
    "system": {
      "osName": "Windows 11",
      "osVersion": "10.0",
      "osArch": "amd64"
    },
    "java": {
      "version": "17.0.2",
      "vendor": "Oracle Corporation",
      "runtime": "Java(TM) SE Runtime Environment"
    },
    "application": {
      "name": "RickPan",
      "version": "v2.1.0",
      "startTime": "2025-01-15T08:00:00",
      "uptime": 9000000
    }
  }
}
```

#### 4. 告警配置管理
```http
GET /api/sys-monitor/alert/config
POST /api/sys-monitor/alert/config

GET Response:
{
  "status": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "metricType": "CPU",
      "warningThreshold": 70.0,
      "criticalThreshold": 90.0,
      "enabled": true
    }
  ]
}

POST Body:
{
  "metricType": "CPU",
  "warningThreshold": 75.0,
  "criticalThreshold": 85.0,
  "enabled": true
}
```

#### 5. 获取告警日志
```http
GET /api/sys-monitor/alert/logs?page=1&size=10

Response:
{
  "status": 200,
  "message": "success",
  "data": {
    "content": [
      {
        "id": 1,
        "alertType": "THRESHOLD_EXCEEDED",
        "metricType": "CPU",
        "alertLevel": "WARNING",
        "alertMessage": "CPU使用率超过阈值",
        "metricValue": 75.5,
        "thresholdValue": 70.0,
        "createdAt": "2025-01-15T10:25:00",
        "resolvedAt": null
      }
    ],
    "totalElements": 50,
    "totalPages": 5,
    "currentPage": 1
  }
}
```

#### 6. 数据导出
```http
POST /api/sys-monitor/export

Body:
{
  "format": "excel", // excel | csv
  "timeRange": "24h",
  "metrics": ["cpu", "memory", "disk", "jvm"],
  "includeCharts": true
}

Response:
{
  "status": 200,
  "message": "success",
  "data": {
    "downloadUrl": "/api/sys-monitor/download/report_20250115.xlsx",
    "fileName": "系统监控报告_20250115.xlsx",
    "fileSize": 2048000
  }
}
```

---

## 🗄️ 数据库设计

### 表结构设计

#### 1. 实时监控数据表
```sql
CREATE TABLE sys_monitor_realtime (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    cpu_usage DECIMAL(5,2) COMMENT 'CPU使用率(%)',
    cpu_cores INT COMMENT 'CPU核心数',
    memory_total BIGINT COMMENT '总内存(字节)',
    memory_used BIGINT COMMENT '已用内存(字节)',
    memory_free BIGINT COMMENT '空闲内存(字节)', 
    memory_usage DECIMAL(5,2) COMMENT '内存使用率(%)',
    disk_total BIGINT COMMENT '磁盘总空间(字节)',
    disk_used BIGINT COMMENT '磁盘已用空间(字节)',
    disk_available BIGINT COMMENT '磁盘可用空间(字节)',
    disk_usage DECIMAL(5,2) COMMENT '磁盘使用率(%)',
    jvm_heap_used BIGINT COMMENT 'JVM堆内存已用(字节)',
    jvm_heap_max BIGINT COMMENT 'JVM堆内存最大(字节)',
    jvm_heap_usage DECIMAL(5,2) COMMENT 'JVM堆内存使用率(%)',
    jvm_non_heap_used BIGINT COMMENT 'JVM非堆内存已用(字节)',
    jvm_non_heap_max BIGINT COMMENT 'JVM非堆内存最大(字节)',
    gc_count BIGINT COMMENT 'GC总次数',
    gc_time BIGINT COMMENT 'GC总耗时(毫秒)',
    thread_count INT COMMENT '活跃线程数',
    thread_peak_count INT COMMENT '峰值线程数',
    thread_daemon_count INT COMMENT '守护线程数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统实时监控数据表';
```

#### 2. 历史监控数据表
```sql
CREATE TABLE sys_monitor_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    record_time TIMESTAMP NOT NULL COMMENT '记录时间',
    cpu_usage DECIMAL(5,2) COMMENT 'CPU使用率(%)',
    memory_usage DECIMAL(5,2) COMMENT '内存使用率(%)',
    disk_usage DECIMAL(5,2) COMMENT '磁盘使用率(%)',
    jvm_heap_usage DECIMAL(5,2) COMMENT 'JVM堆使用率(%)',
    gc_count BIGINT COMMENT 'GC次数',
    gc_time BIGINT COMMENT 'GC耗时(毫秒)',
    thread_count INT COMMENT '线程数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_record_time (record_time),
    INDEX idx_cpu_usage (cpu_usage),
    INDEX idx_memory_usage (memory_usage),
    INDEX idx_disk_usage (disk_usage)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统历史监控数据表'
PARTITION BY RANGE (UNIX_TIMESTAMP(record_time)) (
    PARTITION p_202501 VALUES LESS THAN (UNIX_TIMESTAMP('2025-02-01')),
    PARTITION p_202502 VALUES LESS THAN (UNIX_TIMESTAMP('2025-03-01')),
    PARTITION p_202503 VALUES LESS THAN (UNIX_TIMESTAMP('2025-04-01')),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

#### 3. 告警配置表
```sql
CREATE TABLE sys_monitor_alert_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    metric_type VARCHAR(50) NOT NULL COMMENT '指标类型(CPU/MEMORY/DISK/JVM)',
    metric_name VARCHAR(100) COMMENT '指标名称',
    warning_threshold DECIMAL(5,2) COMMENT '警告阈值',
    critical_threshold DECIMAL(5,2) COMMENT '严重阈值',
    enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用(0:禁用,1:启用)',
    notification_enabled TINYINT(1) DEFAULT 1 COMMENT '是否通知',
    description VARCHAR(255) COMMENT '描述信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_metric_type_name (metric_type, metric_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统监控告警配置表';
```

#### 4. 告警日志表
```sql
CREATE TABLE sys_monitor_alert_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    alert_type VARCHAR(50) NOT NULL COMMENT '告警类型',
    metric_type VARCHAR(50) NOT NULL COMMENT '指标类型',
    metric_name VARCHAR(100) COMMENT '指标名称',
    alert_level VARCHAR(20) NOT NULL COMMENT '告警级别(WARNING/CRITICAL)',
    alert_message TEXT COMMENT '告警消息',
    metric_value DECIMAL(10,2) COMMENT '指标值',
    threshold_value DECIMAL(10,2) COMMENT '阈值',
    alert_time TIMESTAMP NOT NULL COMMENT '告警时间',
    resolved_time TIMESTAMP NULL COMMENT '解决时间',
    resolved TINYINT(1) DEFAULT 0 COMMENT '是否已解决(0:未解决,1:已解决)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_alert_time (alert_time),
    INDEX idx_metric_type (metric_type),
    INDEX idx_alert_level (alert_level),
    INDEX idx_resolved (resolved)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统监控告警日志表';
```

### 初始化数据脚本
```sql
-- 插入默认告警配置
INSERT INTO sys_monitor_alert_config (metric_type, metric_name, warning_threshold, critical_threshold, description) VALUES
('CPU', 'cpu_usage', 70.00, 90.00, 'CPU使用率监控'),
('MEMORY', 'memory_usage', 80.00, 95.00, '内存使用率监控'),
('DISK', 'disk_usage', 85.00, 95.00, '磁盘使用率监控'),
('JVM', 'jvm_heap_usage', 80.00, 90.00, 'JVM堆内存使用率监控');

-- 创建数据清理事件
CREATE EVENT sys_monitor_cleanup
ON SCHEDULE EVERY 1 DAY
STARTS TIMESTAMP(CURRENT_DATE, '02:00:00')
DO
  DELETE FROM sys_monitor_history 
  WHERE record_time < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

---

## 🎨 前端组件设计

### 组件层次结构
```
SysMonitorDashboard.vue (主页面)
├── SysMonitorRealtime.vue (实时监控)
│   ├── MonitorCard.vue (监控卡片)
│   └── StatusIndicator.vue (状态指示器)
├── SysMonitorCharts.vue (图表展示)
│   ├── RealtimeChart.vue (实时图表)
│   └── HistoryChart.vue (历史图表)
├── SysMonitorHistory.vue (历史数据)
│   ├── TimeRangeSelector.vue (时间选择器)
│   └── DataTable.vue (数据表格)
├── SysMonitorSystemInfo.vue (系统信息)
├── SysMonitorAlerts.vue (告警管理)
│   ├── AlertConfig.vue (告警配置)
│   └── AlertLog.vue (告警日志)
└── SysMonitorExport.vue (数据导出)
```

### 主要组件详细设计

#### SysMonitorDashboard.vue - 主页面
**功能：**
- 整体布局和导航
- 权限验证和路由守卫
- 全局状态管理
- 响应式设计

**组件结构：**
```vue
<template>
  <div class="sys-monitor-dashboard">
    <!-- 页面头部 -->
    <div class="dashboard-header">
      <h1 class="page-title">
        <el-icon><Monitor /></el-icon>
        系统监控
      </h1>
      <div class="header-actions">
        <span class="last-update">最后更新: {{ lastUpdateTime }}</span>
        <el-button @click="refreshData" :loading="isLoading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>
    
    <!-- 实时监控卡片区域 -->
    <SysMonitorRealtime />
    
    <!-- 图表展示区域 -->
    <SysMonitorCharts />
    
    <!-- 系统信息和告警 -->
    <div class="info-alerts-section">
      <SysMonitorSystemInfo />
      <SysMonitorAlerts />
    </div>
    
    <!-- 历史数据分析 -->
    <SysMonitorHistory />
  </div>
</template>
```

#### SysMonitorRealtime.vue - 实时监控组件
**功能：**
- 实时数据展示
- 监控卡片布局
- 状态颜色指示
- 自动数据刷新

**核心逻辑：**
```typescript
export default defineComponent({
  name: 'SysMonitorRealtime',
  setup() {
    const monitorData = ref<SysMonitorData | null>(null);
    const isLoading = ref(false);
    
    // 获取实时数据
    const fetchRealtimeData = async () => {
      isLoading.value = true;
      try {
        const response = await sysMonitorApi.getRealtimeData();
        monitorData.value = response.data;
      } catch (error) {
        ElMessage.error('获取监控数据失败');
      } finally {
        isLoading.value = false;
      }
    };
    
    // 自动刷新
    const { pause, resume } = useIntervalFn(fetchRealtimeData, 5000);
    
    onMounted(() => {
      fetchRealtimeData();
    });
    
    onUnmounted(() => {
      pause();
    });
    
    return {
      monitorData,
      isLoading,
      fetchRealtimeData
    };
  }
});
```

#### SysMonitorCharts.vue - 图表组件
**功能：**
- ECharts图表集成
- 实时数据更新
- 多指标趋势展示
- 图表交互功能

**图表配置：**
```typescript
const chartOption = computed(() => ({
  title: {
    text: '系统资源使用趋势',
    left: 'center'
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross'
    }
  },
  legend: {
    data: ['CPU使用率', '内存使用率', '磁盘使用率', 'JVM使用率']
  },
  xAxis: {
    type: 'category',
    data: chartData.value.labels
  },
  yAxis: {
    type: 'value',
    min: 0,
    max: 100,
    axisLabel: {
      formatter: '{value}%'
    }
  },
  series: [
    {
      name: 'CPU使用率',
      type: 'line',
      data: chartData.value.cpu,
      smooth: true,
      itemStyle: { color: '#ff6b6b' }
    },
    {
      name: '内存使用率',
      type: 'line', 
      data: chartData.value.memory,
      smooth: true,
      itemStyle: { color: '#4ecdc4' }
    }
    // ... 其他数据系列
  ]
}));
```

---

## 🧪 测试计划

### 测试策略
- **单元测试**：核心业务逻辑100%覆盖
- **集成测试**：API接口和数据库完整性测试
- **功能测试**：用户场景完整性验证
- **性能测试**：负载和压力测试
- **兼容性测试**：多浏览器和设备适配验证

### 后端测试计划

#### 单元测试 (JUnit 5)
```java
@SpringBootTest
class SysMonitorServiceTest {
    
    @Test
    void testGetRealtimeData() {
        // 测试实时数据获取
        SysMonitorDataDTO data = sysMonitorService.getRealtimeData();
        assertNotNull(data);
        assertTrue(data.getCpu().getUsage() >= 0);
        assertTrue(data.getCpu().getUsage() <= 100);
    }
    
    @Test
    void testDataCollection() {
        // 测试数据采集功能
        sysMonitorCollectorService.collectAndSaveData();
        // 验证数据是否正确保存到数据库
    }
    
    @Test
    void testAlertChecking() {
        // 测试告警检查逻辑
        // 模拟超过阈值的数据
        // 验证告警是否正确触发
    }
}

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class SysMonitorControllerTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void testRealtimeDataApi() {
        ResponseEntity<ResponseDetails> response = restTemplate
            .withBasicAuth("admin", "password")
            .getForEntity("/api/sys-monitor/realtime", ResponseDetails.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(200, response.getBody().getStatus());
    }
}
```

#### 性能测试
```java
@Test
void testDataCollectionPerformance() {
    long startTime = System.currentTimeMillis();
    
    // 执行数据采集
    sysMonitorService.getRealtimeData();
    
    long endTime = System.currentTimeMillis();
    long executionTime = endTime - startTime;
    
    // 验证采集时间小于100ms
    assertTrue(executionTime < 100, "数据采集时间超过100ms: " + executionTime);
}

@Test
void testConcurrentAccess() {
    // 并发访问测试
    ExecutorService executor = Executors.newFixedThreadPool(10);
    CountDownLatch latch = new CountDownLatch(10);
    
    for (int i = 0; i < 10; i++) {
        executor.submit(() -> {
            try {
                sysMonitorService.getRealtimeData();
            } finally {
                latch.countDown();
            }
        });
    }
    
    assertDoesNotThrow(() -> latch.await(5, TimeUnit.SECONDS));
}
```

### 前端测试计划

#### 组件单元测试 (Vue Test Utils + Vitest)
```typescript
import { mount } from '@vue/test-utils';
import { describe, it, expect, vi } from 'vitest';
import SysMonitorRealtime from '@/components/sysMonitor/SysMonitorRealtime.vue';

describe('SysMonitorRealtime', () => {
  it('renders monitor cards correctly', () => {
    const wrapper = mount(SysMonitorRealtime, {
      props: {
        monitorData: mockMonitorData
      }
    });
    
    expect(wrapper.find('.cpu-card').exists()).toBe(true);
    expect(wrapper.find('.memory-card').exists()).toBe(true);
    expect(wrapper.find('.disk-card').exists()).toBe(true);
    expect(wrapper.find('.jvm-card').exists()).toBe(true);
  });
  
  it('updates data automatically', async () => {
    const fetchDataSpy = vi.fn();
    const wrapper = mount(SysMonitorRealtime, {
      global: {
        provide: {
          fetchRealtimeData: fetchDataSpy
        }
      }
    });
    
    // 模拟自动刷新
    await new Promise(resolve => setTimeout(resolve, 5100));
    expect(fetchDataSpy).toHaveBeenCalled();
  });
});
```

#### E2E测试 (Cypress)
```typescript
describe('System Monitor Dashboard', () => {
  beforeEach(() => {
    cy.login('admin', 'password');
    cy.visit('/dashboard/admin/sys-monitor');
  });
  
  it('displays real-time monitoring data', () => {
    cy.get('[data-testid="cpu-card"]').should('be.visible');
    cy.get('[data-testid="memory-card"]').should('be.visible');
    cy.get('[data-testid="disk-card"]').should('be.visible');
    cy.get('[data-testid="jvm-card"]').should('be.visible');
    
    // 验证数据格式
    cy.get('[data-testid="cpu-usage"]').should('contain', '%');
  });
  
  it('refreshes data when refresh button is clicked', () => {
    cy.intercept('GET', '/api/sys-monitor/realtime').as('getRealtimeData');
    
    cy.get('[data-testid="refresh-button"]').click();
    cy.wait('@getRealtimeData');
    
    cy.get('[data-testid="last-update"]').should('contain', new Date().toLocaleDateString());
  });
  
  it('displays historical data charts', () => {
    cy.get('[data-testid="history-tab"]').click();
    cy.get('[data-testid="time-range-selector"]').select('1h');
    
    cy.get('.echarts-chart').should('be.visible');
    cy.get('.chart-legend').should('contain', 'CPU使用率');
  });
});
```

### 测试数据准备
```typescript
// 测试数据模拟
export const mockMonitorData: SysMonitorData = {
  timestamp: new Date().toISOString(),
  cpu: {
    usage: 45.5,
    cores: 8,
    loadAverage: [1.2, 1.5, 1.8]
  },
  memory: {
    total: 17179869184,
    used: 8589934592,
    free: 8589934592,
    usage: 50.0
  },
  disk: {
    total: 536870912000,
    used: 429496729600,
    available: 107374182400,
    usage: 80.0
  },
  jvm: {
    heapUsed: 536870912,
    heapMax: 2147483648,
    heapUsage: 25.0,
    nonHeapUsed: 104857600,
    nonHeapMax: 268435456
  }
};
```

---

## ⚠️ 风险控制

### 开发风险控制

#### 1. 技术风险控制
**风险识别：**
- JMX API兼容性问题
- 数据库性能问题
- 前端图表渲染性能

**控制措施：**
```java
// 异常处理和降级策略
@Service
public class SysMonitorService {
    
    public SysMonitorDataDTO getRealtimeData() {
        try {
            return collectRealTimeData();
        } catch (Exception e) {
            log.warn("数据采集失败，返回默认数据", e);
            return getDefaultMonitorData();
        }
    }
    
    private SysMonitorDataDTO getDefaultMonitorData() {
        // 返回安全的默认数据
        return SysMonitorDataDTO.builder()
            .cpu(CpuInfo.builder().usage(0.0).cores(1).build())
            .memory(MemoryInfo.builder().usage(0.0).build())
            .build();
    }
}
```

#### 2. 性能风险控制
**监控数据采集优化：**
```java
@Configuration
@EnableAsync
public class SysMonitorAsyncConfig {
    
    @Bean("monitorTaskExecutor")
    public TaskExecutor monitorTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(4);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("SysMonitor-");
        executor.initialize();
        return executor;
    }
}

@Service
public class SysMonitorCollectorService {
    
    @Async("monitorTaskExecutor")
    @Scheduled(fixedRate = 5000)
    public CompletableFuture<Void> collectData() {
        try {
            // 设置超时时间
            CompletableFuture<SysMonitorData> dataFuture = 
                CompletableFuture.supplyAsync(this::collectSystemData)
                    .orTimeout(3, TimeUnit.SECONDS);
            
            SysMonitorData data = dataFuture.get();
            saveData(data);
        } catch (TimeoutException e) {
            log.warn("数据采集超时，跳过本次采集");
        }
        return CompletableFuture.completedFuture(null);
    }
}
```

#### 3. 数据安全风险控制
**权限验证加强：**
```java
@RestController
@RequestMapping("/api/sys-monitor")
@PreAuthorize("hasRole('ADMIN')")
public class SysMonitorController {
    
    @GetMapping("/realtime")
    @PreAuthorize("hasAuthority('SYSTEM_MONITOR_READ')")
    public ResponseDetails getRealtimeData(Authentication auth) {
        // 记录访问日志
        log.info("用户 {} 访问系统监控数据", auth.getName());
        
        try {
            SysMonitorDataDTO data = sysMonitorService.getRealtimeData();
            return ResponseDetails.ok().put("data", data);
        } catch (Exception e) {
            log.error("获取监控数据失败", e);
            return ResponseDetails.error("获取监控数据失败");
        }
    }
}
```

### 运维风险控制

#### 1. 数据存储风险控制
```sql
-- 数据自动清理策略
CREATE EVENT sys_monitor_auto_cleanup
ON SCHEDULE EVERY 1 DAY
STARTS TIMESTAMP(CURRENT_DATE, '02:00:00')
DO
BEGIN
    -- 清理30天前的历史数据
    DELETE FROM sys_monitor_history 
    WHERE record_time < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- 清理已解决的告警日志(保留90天)
    DELETE FROM sys_monitor_alert_log 
    WHERE resolved = 1 AND resolved_time < DATE_SUB(NOW(), INTERVAL 90 DAY);
    
    -- 优化表
    OPTIMIZE TABLE sys_monitor_history;
    OPTIMIZE TABLE sys_monitor_alert_log;
END;

-- 监控表大小
CREATE VIEW v_sys_monitor_table_size AS
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as table_size_mb,
    table_rows
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_name LIKE 'sys_monitor_%';
```

#### 2. 系统资源控制
```java
@ConfigurationProperties(prefix = "sys.monitor")
@Component
public class SysMonitorProperties {
    
    /**
     * 是否启用监控功能
     */
    private boolean enabled = true;
    
    /**
     * 数据采集间隔(毫秒)
     */
    private long collectInterval = 5000;
    
    /**
     * 最大并发采集数
     */
    private int maxConcurrentCollections = 3;
    
    /**
     * 单次查询最大数据量
     */
    private int maxQueryDataPoints = 1000;
    
    /**
     * 历史数据保留天数
     */
    private int historyRetentionDays = 30;
    
    // getters and setters
}
```

---

## ✅ 交付标准

### 功能完整性标准
**必须实现功能 (P0)：**
- ✅ 实时监控数据展示 (CPU、内存、磁盘、JVM)
- ✅ 基础图表可视化 (折线图、仪表盘)
- ✅ 系统信息展示
- ✅ 权限控制 (仅管理员可访问)
- ✅ 响应式界面设计

**重要功能 (P1)：**
- ✅ 历史数据查询和展示
- ✅ 告警配置管理
- ✅ 数据导出功能
- ✅ 移动端适配

**增强功能 (P2)：**
- ✅ 告警日志管理
- ✅ 性能分析报告
- ✅ 数据趋势预测

### 性能标准
**响应时间要求：**
- API响应时间 < 1秒
- 页面首次加载 < 3秒
- 图表渲染时间 < 500ms
- 数据自动刷新间隔 5秒

**资源使用要求：**
- 前端内存占用 < 100MB
- 后端CPU占用 < 5% (空闲时)
- 数据库查询响应 < 200ms
- 数据采集CPU占用 < 2%

### 代码质量标准
**后端代码质量：**
```java
// 代码规范要求
1. 所有类和方法必须有完整的JavaDoc注释
2. 单元测试覆盖率 > 80%
3. 遵循阿里巴巴Java开发规范
4. 使用SonarQube检查无严重问题
5. 所有异常必须有适当的处理和日志记录

// 示例：
/**
 * 系统监控数据采集服务
 * 负责定时采集系统资源使用情况并存储到数据库
 * 
 * <AUTHOR>
 * @since 2.1.0
 */
@Service
@Slf4j
public class SysMonitorCollectorService {
    
    /**
     * 采集并保存系统监控数据
     * 该方法每5秒执行一次，采集CPU、内存、磁盘等系统资源信息
     * 
     * @throws MonitorDataCollectionException 数据采集失败时抛出
     */
    @Scheduled(fixedRate = 5000)
    @Async("monitorTaskExecutor")
    public void collectAndSaveData() throws MonitorDataCollectionException {
        // 实现逻辑
    }
}
```

**前端代码质量：**
```typescript
// 代码规范要求
1. 所有组件必须有完整的TypeScript类型定义
2. 遵循Vue 3组合式API最佳实践
3. 使用ESLint检查无错误和警告
4. 组件必须有props验证和默认值
5. 关键功能必须有单元测试

// 示例：
/**
 * 系统实时监控组件
 * 显示CPU、内存、磁盘、JVM等系统资源的实时使用情况
 */
export default defineComponent({
  name: 'SysMonitorRealtime',
  props: {
    refreshInterval: {
      type: Number,
      default: 5000,
      validator: (value: number) => value >= 1000
    },
    autoRefresh: {
      type: Boolean,
      default: true
    }
  },
  emits: {
    dataUpdated: (data: SysMonitorData) => data !== null,
    error: (error: Error) => error instanceof Error
  },
  setup(props, { emit }) {
    // 组件逻辑
  }
});
```

### 文档标准
**技术文档要求：**
1. **API文档**：完整的接口文档，包含请求/响应示例
2. **数据库文档**：表结构说明和关系图
3. **组件文档**：组件使用说明和属性文档
4. **部署文档**：详细的部署步骤和配置说明
5. **故障排查文档**：常见问题和解决方案

### 测试标准
**测试覆盖要求：**
- 后端单元测试覆盖率 > 80%
- 关键API接口集成测试覆盖率 100%
- 前端组件测试覆盖主要功能
- E2E测试覆盖核心用户流程
- 性能测试验证关键指标

**测试用例要求：**
```java
// 测试用例必须包含
1. 正常流程测试
2. 异常情况测试  
3. 边界值测试
4. 性能基准测试
5. 并发访问测试

@Test
@DisplayName("CPU使用率数据采集-正常情况")
void testCpuUsageCollection_Normal() {
    // Given
    SysMonitorCollectorService collector = new SysMonitorCollectorService();
    
    // When  
    CpuInfo cpuInfo = collector.collectCpuInfo();
    
    // Then
    assertThat(cpuInfo.getUsage()).isBetween(0.0, 100.0);
    assertThat(cpuInfo.getCores()).isGreaterThan(0);
}

@Test
@DisplayName("CPU使用率数据采集-异常情况")
void testCpuUsageCollection_Exception() {
    // 测试JMX API异常时的处理
}
```

### 安全标准
**安全要求：**
1. **权限验证**：所有API接口必须验证ADMIN权限
2. **输入验证**：所有用户输入必须验证和过滤
3. **日志安全**：不在日志中记录敏感信息
4. **错误处理**：不向客户端暴露系统内部信息
5. **数据传输**：使用HTTPS协议传输数据

### 部署标准
**部署检查清单：**
- [ ] 数据库表结构正确创建
- [ ] 初始化数据正确插入  
- [ ] API接口响应正常
- [ ] 前端页面正常访问
- [ ] 权限控制生效
- [ ] 监控数据正常采集
- [ ] 定时任务正常运行
- [ ] 日志输出正常
- [ ] 性能指标达标
- [ ] 兼容性测试通过

---

## 📝 最终确认清单

### 开发前确认
- [ ] **需求理解**：开发团队完全理解需求文档
- [ ] **技术方案**：技术选型和架构设计获得确认  
- [ ] **环境准备**：开发环境配置完成
- [ ] **权限配置**：数据库和系统权限配置完成
- [ ] **图标确认**：通过Context7 MCP确认所需图标

### 开发中检查
- [ ] **代码规范**：遵循项目编码规范
- [ ] **最小改动**：未修改公共文件，主动适配现有架构
- [ ] **进度跟踪**：按照规划文档执行，及时更新进度
- [ ] **测试覆盖**：编写充分的单元测试和集成测试
- [ ] **文档同步**：及时更新技术文档

### 交付前验证
- [ ] **功能完整**：所有P0和P1功能正常工作
- [ ] **性能达标**：满足性能要求指标
- [ ] **安全合规**：通过安全检查
- [ ] **兼容性**：多浏览器和设备测试通过
- [ ] **文档完整**：技术文档和用户文档完整

---

**📋 总结**

本开发规划文档为SysMonitor系统监控模块提供了完整的开发指导，包括：

✅ **详细的阶段规划** - 3个阶段，15个工作日完成  
✅ **完整的文件设计** - 清晰的前后端文件结构  
✅ **具体的任务清单** - 可执行的开发任务分解  
✅ **全面的测试计划** - 保证质量的测试策略  
✅ **严格的交付标准** - 明确的完成标准  

**下一步行动：**
1. 开发团队仔细阅读所有文档
2. 确认理解需求和技术方案  
3. 准备开发环境和工具
4. 开始第一阶段开发工作

严格按照本规划文档执行，确保高质量交付系统监控功能。

---

*文档版本：v1.0*  
*创建时间：2025-01-15*  
*规划负责人：技术团队*