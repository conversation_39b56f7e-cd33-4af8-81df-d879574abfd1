package com.rickpan.service;

import com.rickpan.dto.SysMonitorDataDTO;
import com.rickpan.dto.SysMonitorQueryDTO;
import com.rickpan.entity.SysMonitorHistory;
import com.rickpan.entity.SysMonitorRealtime;
import com.rickpan.entity.SysMonitorSystemInfo;
import com.rickpan.repository.SysMonitorHistoryRepository;
import com.rickpan.repository.SysMonitorRealtimeRepository;
import com.rickpan.repository.SysMonitorSystemInfoRepository;
import com.sun.management.OperatingSystemMXBean;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.lang.management.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 系统监控核心业务服务
 * 负责系统监控数据的采集、处理和查询
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SysMonitorService {

    private final SysMonitorRealtimeRepository realtimeRepository;
    private final SysMonitorHistoryRepository historyRepository;
    private final SysMonitorSystemInfoRepository systemInfoRepository;

    // JMX管理Bean缓存（减少重复获取开销）
    private volatile OperatingSystemMXBean osBean;
    private volatile MemoryMXBean memoryBean;
    private volatile ThreadMXBean threadBean;
    private volatile RuntimeMXBean runtimeBean;
    private volatile List<GarbageCollectorMXBean> gcBeans;

    // 应用启动时间（用于计算运行时间）
    private final long applicationStartTime = System.currentTimeMillis();

    /**
     * 获取实时监控数据
     * 
     * @return 实时监控数据DTO
     */
    public SysMonitorDataDTO getRealtimeData() {
        log.debug("开始获取实时监控数据");
        
        try {
            // 初始化JMX Bean（如果需要）
            initializeJmxBeans();
            
            // 采集系统监控数据
            SysMonitorDataDTO.CpuInfo cpuInfo = collectCpuInfo();
            SysMonitorDataDTO.MemoryInfo memoryInfo = collectMemoryInfo();
            SysMonitorDataDTO.DiskInfo diskInfo = collectDiskInfo();
            SysMonitorDataDTO.JvmInfo jvmInfo = collectJvmInfo();
            SysMonitorDataDTO.GcInfo gcInfo = collectGcInfo();
            SysMonitorDataDTO.ThreadInfo threadInfo = collectThreadInfo();
            SysMonitorDataDTO.SystemInfo systemInfo = collectSystemInfo();
            
            // 构建返回数据
            SysMonitorDataDTO result = SysMonitorDataDTO.builder()
                .timestamp(LocalDateTime.now())
                .cpu(cpuInfo)
                .memory(memoryInfo)
                .disk(diskInfo)
                .jvm(jvmInfo)
                .gc(gcInfo)
                .threads(threadInfo)
                .system(systemInfo)
                .build();
                
            log.debug("实时监控数据获取完成");
            return result;
            
        } catch (Exception e) {
            log.error("获取实时监控数据失败", e);
            // 返回默认数据，避免前端报错
            return createDefaultMonitorData();
        }
    }

    /**
     * 查询历史监控数据
     * 
     * @param queryDTO 查询参数
     * @return 历史监控数据
     */
    public Page<SysMonitorHistory> getHistoryData(SysMonitorQueryDTO queryDTO) {
        log.debug("查询历史监控数据, 参数: {}", queryDTO);
        
        try {
            // 解析时间范围
            LocalDateTime[] timeRange = parseTimeRange(queryDTO.getTimeRange(), 
                queryDTO.getStartTime(), queryDTO.getEndTime());
            
            // 创建分页参数
            Pageable pageable = PageRequest.of(
                queryDTO.getPage() - 1, 
                queryDTO.getSize(),
                "desc".equalsIgnoreCase(queryDTO.getSort()) ? 
                    Sort.by("recordTime").descending() : 
                    Sort.by("recordTime").ascending()
            );
            
            // 查询数据
            Page<SysMonitorHistory> result = historyRepository.findByRecordTimeBetweenOrderByRecordTimeAsc(
                timeRange[0], timeRange[1], pageable);
                
            log.debug("历史监控数据查询完成, 共{}条记录", result.getTotalElements());
            return result;
            
        } catch (Exception e) {
            log.error("查询历史监控数据失败", e);
            throw new RuntimeException("查询历史监控数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取监控数据统计信息
     * 
     * @param timeRange 时间范围
     * @return 统计信息
     */
    public Object getMonitorStatistics(String timeRange) {
        log.debug("获取监控数据统计信息, 时间范围: {}", timeRange);
        
        try {
            LocalDateTime[] range = parseTimeRange(timeRange, null, null);
            Object[] stats = historyRepository.getStatisticsByRecordTimeBetween(range[0], range[1]);
            
            if (stats != null && stats.length > 0) {
                // 构建统计结果
                return SysMonitorDataDTO.builder()
                    .cpu(SysMonitorDataDTO.CpuInfo.builder()
                        .usage((BigDecimal) stats[0])  // avgCpu
                        .build())
                    .memory(SysMonitorDataDTO.MemoryInfo.builder()
                        .usage((BigDecimal) stats[3])  // avgMemory
                        .build())
                    .disk(SysMonitorDataDTO.DiskInfo.builder()
                        .usage((BigDecimal) stats[6])  // avgDisk
                        .build())
                    .jvm(SysMonitorDataDTO.JvmInfo.builder()
                        .heapUsage((BigDecimal) stats[9])  // avgJvm
                        .build())
                    .build();
            }
            
            return null;
            
        } catch (Exception e) {
            log.error("获取监控统计信息失败", e);
            throw new RuntimeException("获取监控统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 保存实时监控数据到数据库
     * 
     * @param monitorData 监控数据
     */
    @Transactional
    public void saveRealtimeData(SysMonitorDataDTO monitorData) {
        log.debug("保存实时监控数据到数据库");
        
        try {
            // 清空现有实时数据（保持只有一条最新记录）
            realtimeRepository.deleteAllRealtimeData();
            
            // 构建实时数据实体
            SysMonitorRealtime realtime = buildRealtimeEntity(monitorData);
            
            // 保存到数据库
            realtimeRepository.save(realtime);
            
            log.debug("实时监控数据保存成功");
            
        } catch (Exception e) {
            log.error("保存实时监控数据失败", e);
            throw new RuntimeException("保存实时监控数据失败: " + e.getMessage());
        }
    }

    /**
     * 保存历史监控数据
     * 
     * @param monitorData 监控数据
     */
    @Transactional
    public void saveHistoryData(SysMonitorDataDTO monitorData) {
        log.debug("保存历史监控数据");
        
        try {
            // 构建历史数据实体
            SysMonitorHistory history = buildHistoryEntity(monitorData);
            
            // 保存到数据库
            historyRepository.save(history);
            
            log.debug("历史监控数据保存成功");
            
        } catch (Exception e) {
            log.error("保存历史监控数据失败", e);
            throw new RuntimeException("保存历史监控数据失败: " + e.getMessage());
        }
    }

    /**
     * 更新系统基础信息
     * 
     * @param monitorData 监控数据
     */
    @Transactional
    public void updateSystemInfo(SysMonitorDataDTO monitorData) {
        log.debug("更新系统基础信息");
        
        try {
            Optional<SysMonitorSystemInfo> existingInfo = systemInfoRepository.findLatest();
            
            SysMonitorSystemInfo systemInfo;
            if (existingInfo.isPresent()) {
                systemInfo = existingInfo.get();
                // 更新可变信息
                systemInfo.setAppStartTime(monitorData.getSystem().getAppStartTime());
            } else {
                // 创建新的系统信息记录
                systemInfo = buildSystemInfoEntity(monitorData);
            }
            
            systemInfoRepository.save(systemInfo);
            
            log.debug("系统基础信息更新成功");
            
        } catch (Exception e) {
            log.error("更新系统基础信息失败", e);
            throw new RuntimeException("更新系统基础信息失败: " + e.getMessage());
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 初始化JMX管理Bean
     */
    private void initializeJmxBeans() {
        if (osBean == null) {
            synchronized (this) {
                if (osBean == null) {
                    osBean = (OperatingSystemMXBean) ManagementFactory.getOperatingSystemMXBean();
                    memoryBean = ManagementFactory.getMemoryMXBean();
                    threadBean = ManagementFactory.getThreadMXBean();
                    runtimeBean = ManagementFactory.getRuntimeMXBean();
                    gcBeans = ManagementFactory.getGarbageCollectorMXBeans();
                    
                    log.info("JMX管理Bean初始化完成");
                }
            }
        }
    }

    /**
     * 采集CPU信息
     */
    private SysMonitorDataDTO.CpuInfo collectCpuInfo() {
        try {
            int processors = osBean.getAvailableProcessors();
            
            // CPU使用率（可能需要调用两次获取准确值）
            double cpuLoad = osBean.getCpuLoad();
            if (cpuLoad < 0) {
                // 第一次调用可能返回负值，等待一下再调用
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                cpuLoad = osBean.getCpuLoad();
            }
            
            // 进程CPU使用率
            double processCpuLoad = osBean.getProcessCpuLoad();
            
            // 系统负载（Linux/Unix系统支持）
            double systemLoadAverage = osBean.getSystemLoadAverage();
            BigDecimal[] loadAverage = new BigDecimal[3];
            if (systemLoadAverage >= 0) {
                loadAverage[0] = BigDecimal.valueOf(systemLoadAverage);
                loadAverage[1] = BigDecimal.valueOf(systemLoadAverage);
                loadAverage[2] = BigDecimal.valueOf(systemLoadAverage);
            } else {
                // Windows系统不支持，使用CPU使用率近似
                loadAverage[0] = BigDecimal.valueOf(cpuLoad * processors);
                loadAverage[1] = BigDecimal.valueOf(cpuLoad * processors);
                loadAverage[2] = BigDecimal.valueOf(cpuLoad * processors);
            }
            
            return SysMonitorDataDTO.CpuInfo.builder()
                .usage(BigDecimal.valueOf(cpuLoad * 100).setScale(2, RoundingMode.HALF_UP))
                .cores(processors)
                .loadAverage(loadAverage)
                .processUsage(BigDecimal.valueOf(processCpuLoad * 100).setScale(2, RoundingMode.HALF_UP))
                .build();
                
        } catch (Exception e) {
            log.warn("采集CPU信息失败", e);
            return SysMonitorDataDTO.CpuInfo.builder()
                .usage(BigDecimal.ZERO)
                .cores(1)
                .loadAverage(new BigDecimal[]{BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO})
                .processUsage(BigDecimal.ZERO)
                .build();
        }
    }

    /**
     * 采集系统内存信息
     */
    private SysMonitorDataDTO.MemoryInfo collectMemoryInfo() {
        try {
            long totalMemory = osBean.getTotalPhysicalMemorySize();
            long freeMemory = osBean.getFreePhysicalMemorySize();
            long usedMemory = totalMemory - freeMemory;
            
            BigDecimal usage = totalMemory > 0 ? 
                BigDecimal.valueOf((double) usedMemory / totalMemory * 100).setScale(2, RoundingMode.HALF_UP) :
                BigDecimal.ZERO;
            
            return SysMonitorDataDTO.MemoryInfo.builder()
                .total(totalMemory)
                .used(usedMemory)
                .free(freeMemory)
                .available(freeMemory)
                .usage(usage)
                .build();
                
        } catch (Exception e) {
            log.warn("采集系统内存信息失败", e);
            return SysMonitorDataDTO.MemoryInfo.builder()
                .total(0L).used(0L).free(0L).available(0L).usage(BigDecimal.ZERO)
                .build();
        }
    }

    /**
     * 采集磁盘信息
     */
    private SysMonitorDataDTO.DiskInfo collectDiskInfo() {
        try {
            long totalSpace = 0;
            long freeSpace = 0;
            
            // 遍历所有根目录
            File[] roots = File.listRoots();
            for (File root : roots) {
                totalSpace += root.getTotalSpace();
                freeSpace += root.getFreeSpace();
            }
            
            long usedSpace = totalSpace - freeSpace;
            BigDecimal usage = totalSpace > 0 ? 
                BigDecimal.valueOf((double) usedSpace / totalSpace * 100).setScale(2, RoundingMode.HALF_UP) :
                BigDecimal.ZERO;
            
            return SysMonitorDataDTO.DiskInfo.builder()
                .total(totalSpace)
                .used(usedSpace)
                .available(freeSpace)
                .usage(usage)
                .build();
                
        } catch (Exception e) {
            log.warn("采集磁盘信息失败", e);
            return SysMonitorDataDTO.DiskInfo.builder()
                .total(0L).used(0L).available(0L).usage(BigDecimal.ZERO)
                .build();
        }
    }

    /**
     * 采集JVM信息
     */
    private SysMonitorDataDTO.JvmInfo collectJvmInfo() {
        try {
            // 堆内存使用情况
            MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
            BigDecimal heapUsagePercent = heapUsage.getMax() > 0 ?
                BigDecimal.valueOf((double) heapUsage.getUsed() / heapUsage.getMax() * 100).setScale(2, RoundingMode.HALF_UP) :
                BigDecimal.ZERO;
            
            // 非堆内存使用情况
            MemoryUsage nonHeapUsage = memoryBean.getNonHeapMemoryUsage();
            BigDecimal nonHeapUsagePercent = nonHeapUsage.getMax() > 0 ?
                BigDecimal.valueOf((double) nonHeapUsage.getUsed() / nonHeapUsage.getMax() * 100).setScale(2, RoundingMode.HALF_UP) :
                BigDecimal.ZERO;
            
            // JVM运行时间
            long jvmUptime = runtimeBean.getUptime();
            
            return SysMonitorDataDTO.JvmInfo.builder()
                .heapUsed(heapUsage.getUsed())
                .heapMax(heapUsage.getMax())
                .heapCommitted(heapUsage.getCommitted())
                .heapUsage(heapUsagePercent)
                .nonHeapUsed(nonHeapUsage.getUsed())
                .nonHeapMax(nonHeapUsage.getMax())
                .nonHeapCommitted(nonHeapUsage.getCommitted())
                .nonHeapUsage(nonHeapUsagePercent)
                .uptime(jvmUptime)
                .build();
                
        } catch (Exception e) {
            log.warn("采集JVM信息失败", e);
            return SysMonitorDataDTO.JvmInfo.builder()
                .heapUsed(0L).heapMax(0L).heapCommitted(0L).heapUsage(BigDecimal.ZERO)
                .nonHeapUsed(0L).nonHeapMax(-1L).nonHeapCommitted(0L).nonHeapUsage(BigDecimal.ZERO)
                .uptime(0L)
                .build();
        }
    }

    /**
     * 采集垃圾回收信息
     */
    private SysMonitorDataDTO.GcInfo collectGcInfo() {
        try {
            long totalGcCount = 0;
            long totalGcTime = 0;
            double maxAvgGcTime = 0;
            
            List<SysMonitorDataDTO.GcCollectorInfo> collectors = new ArrayList<>();
            
            for (GarbageCollectorMXBean gcBean : gcBeans) {
                long gcCount = gcBean.getCollectionCount();
                long gcTime = gcBean.getCollectionTime();
                
                if (gcCount > 0) {
                    totalGcCount += gcCount;
                    totalGcTime += gcTime;
                    
                    double avgTime = (double) gcTime / gcCount;
                    if (avgTime > maxAvgGcTime) {
                        maxAvgGcTime = avgTime;
                    }
                    
                    collectors.add(SysMonitorDataDTO.GcCollectorInfo.builder()
                        .name(gcBean.getName())
                        .collectionCount(gcCount)
                        .collectionTime(gcTime)
                        .avgCollectionTime(BigDecimal.valueOf(avgTime).setScale(2, RoundingMode.HALF_UP))
                        .build());
                }
            }
            
            BigDecimal avgGcTime = totalGcCount > 0 ?
                BigDecimal.valueOf((double) totalGcTime / totalGcCount).setScale(2, RoundingMode.HALF_UP) :
                BigDecimal.ZERO;
            
            return SysMonitorDataDTO.GcInfo.builder()
                .count(totalGcCount)
                .time(totalGcTime)
                .avgTime(avgGcTime)
                .maxTime((long) maxAvgGcTime)
                .collectors(collectors.toArray(new SysMonitorDataDTO.GcCollectorInfo[0]))
                .build();
                
        } catch (Exception e) {
            log.warn("采集GC信息失败", e);
            return SysMonitorDataDTO.GcInfo.builder()
                .count(0L).time(0L).avgTime(BigDecimal.ZERO).maxTime(0L)
                .collectors(new SysMonitorDataDTO.GcCollectorInfo[0])
                .build();
        }
    }

    /**
     * 采集线程信息
     */
    private SysMonitorDataDTO.ThreadInfo collectThreadInfo() {
        try {
            int threadCount = threadBean.getThreadCount();
            int peakThreadCount = threadBean.getPeakThreadCount();
            int daemonThreadCount = threadBean.getDaemonThreadCount();
            long totalStartedThreadCount = threadBean.getTotalStartedThreadCount();
            
            // 死锁检测
            long[] deadlockedThreads = threadBean.findDeadlockedThreads();
            int deadlockedCount = deadlockedThreads != null ? deadlockedThreads.length : 0;
            
            return SysMonitorDataDTO.ThreadInfo.builder()
                .active(threadCount)
                .peak(peakThreadCount)
                .daemon(daemonThreadCount)
                .totalStarted(totalStartedThreadCount)
                .user(threadCount - daemonThreadCount)
                .deadlocked(deadlockedCount)
                .build();
                
        } catch (Exception e) {
            log.warn("采集线程信息失败", e);
            return SysMonitorDataDTO.ThreadInfo.builder()
                .active(0).peak(0).daemon(0).totalStarted(0L).user(0).deadlocked(0)
                .build();
        }
    }

    /**
     * 采集系统信息
     */
    private SysMonitorDataDTO.SystemInfo collectSystemInfo() {
        try {
            return SysMonitorDataDTO.SystemInfo.builder()
                .osName(System.getProperty("os.name"))
                .osVersion(System.getProperty("os.version"))
                .osArch(System.getProperty("os.arch"))
                .javaVersion(System.getProperty("java.version"))
                .javaVendor(System.getProperty("java.vendor"))
                .jvmName(runtimeBean.getVmName())
                .jvmVersion(runtimeBean.getVmVersion())
                .appName("RickPan")
                .appVersion("2.1.0")
                .appStartTime(LocalDateTime.now().minusSeconds(runtimeBean.getUptime() / 1000))
                .systemUptime(System.currentTimeMillis() - applicationStartTime)
                .jvmUptime(runtimeBean.getUptime())
                .build();
                
        } catch (Exception e) {
            log.warn("采集系统信息失败", e);
            return SysMonitorDataDTO.SystemInfo.builder()
                .osName("Unknown").osVersion("Unknown").osArch("Unknown")
                .javaVersion("Unknown").javaVendor("Unknown")
                .jvmName("Unknown").jvmVersion("Unknown")
                .appName("RickPan").appVersion("2.1.0")
                .appStartTime(LocalDateTime.now())
                .systemUptime(0L).jvmUptime(0L)
                .build();
        }
    }

    /**
     * 解析时间范围
     */
    private LocalDateTime[] parseTimeRange(String timeRange, LocalDateTime startTime, LocalDateTime endTime) {
        if ("custom".equals(timeRange) && startTime != null && endTime != null) {
            return new LocalDateTime[]{startTime, endTime};
        }
        
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime start;
        
        switch (timeRange) {
            case "1h":
                start = now.minusHours(1);
                break;
            case "6h":
                start = now.minusHours(6);
                break;
            case "24h":
                start = now.minusHours(24);
                break;
            case "7d":
                start = now.minusDays(7);
                break;
            case "30d":
                start = now.minusDays(30);
                break;
            default:
                start = now.minusHours(1);
        }
        
        return new LocalDateTime[]{start, now};
    }

    /**
     * 构建实时数据实体
     */
    private SysMonitorRealtime buildRealtimeEntity(SysMonitorDataDTO monitorData) {
        return SysMonitorRealtime.builder()
            .cpuUsage(monitorData.getCpu().getUsage())
            .cpuCores(monitorData.getCpu().getCores())
            .memoryTotal(monitorData.getMemory().getTotal())
            .memoryUsed(monitorData.getMemory().getUsed())
            .memoryFree(monitorData.getMemory().getFree())
            .memoryUsage(monitorData.getMemory().getUsage())
            .diskTotal(monitorData.getDisk().getTotal())
            .diskUsed(monitorData.getDisk().getUsed())
            .diskAvailable(monitorData.getDisk().getAvailable())
            .diskUsage(monitorData.getDisk().getUsage())
            .jvmHeapUsed(monitorData.getJvm().getHeapUsed())
            .jvmHeapMax(monitorData.getJvm().getHeapMax())
            .jvmHeapCommitted(monitorData.getJvm().getHeapCommitted())
            .jvmHeapUsage(monitorData.getJvm().getHeapUsage())
            .jvmNonHeapUsed(monitorData.getJvm().getNonHeapUsed())
            .jvmNonHeapMax(monitorData.getJvm().getNonHeapMax())
            .jvmNonHeapCommitted(monitorData.getJvm().getNonHeapCommitted())
            .gcCount(monitorData.getGc().getCount())
            .gcTime(monitorData.getGc().getTime())
            .gcAvgTime(monitorData.getGc().getAvgTime())
            .gcMaxTime(monitorData.getGc().getMaxTime())
            .threadCount(monitorData.getThreads().getActive())
            .threadPeakCount(monitorData.getThreads().getPeak())
            .threadDaemonCount(monitorData.getThreads().getDaemon())
            .threadTotalStarted(monitorData.getThreads().getTotalStarted())
            .systemUptime(monitorData.getSystem().getSystemUptime())
            .jvmUptime(monitorData.getSystem().getJvmUptime())
            .build();
    }

    /**
     * 构建历史数据实体
     */
    private SysMonitorHistory buildHistoryEntity(SysMonitorDataDTO monitorData) {
        return SysMonitorHistory.builder()
            .recordTime(monitorData.getTimestamp())
            .cpuUsage(monitorData.getCpu().getUsage())
            .memoryUsage(monitorData.getMemory().getUsage())
            .diskUsage(monitorData.getDisk().getUsage())
            .jvmHeapUsage(monitorData.getJvm().getHeapUsage())
            .memoryTotal(monitorData.getMemory().getTotal())
            .memoryUsed(monitorData.getMemory().getUsed())
            .diskTotal(monitorData.getDisk().getTotal())
            .diskUsed(monitorData.getDisk().getUsed())
            .jvmHeapUsed(monitorData.getJvm().getHeapUsed())
            .jvmHeapMax(monitorData.getJvm().getHeapMax())
            .gcCount(monitorData.getGc().getCount())
            .gcTime(monitorData.getGc().getTime())
            .threadCount(monitorData.getThreads().getActive())
            .cpuCores(monitorData.getCpu().getCores())
            .build();
    }

    /**
     * 构建系统信息实体
     */
    private SysMonitorSystemInfo buildSystemInfoEntity(SysMonitorDataDTO monitorData) {
        SysMonitorDataDTO.SystemInfo sysInfo = monitorData.getSystem();
        return SysMonitorSystemInfo.builder()
            .osName(sysInfo.getOsName())
            .osVersion(sysInfo.getOsVersion())
            .osArch(sysInfo.getOsArch())
            .javaVersion(sysInfo.getJavaVersion())
            .javaVendor(sysInfo.getJavaVendor())
            .javaHome(System.getProperty("java.home"))
            .jvmName(sysInfo.getJvmName())
            .jvmVersion(sysInfo.getJvmVersion())
            .appName(sysInfo.getAppName())
            .appVersion(sysInfo.getAppVersion())
            .appStartTime(sysInfo.getAppStartTime())
            .processorCount(monitorData.getCpu().getCores())
            .totalPhysicalMemory(monitorData.getMemory().getTotal())
            .totalDiskSpace(monitorData.getDisk().getTotal())
            .build();
    }

    /**
     * 创建默认监控数据（异常时使用）
     */
    private SysMonitorDataDTO createDefaultMonitorData() {
        return SysMonitorDataDTO.builder()
            .timestamp(LocalDateTime.now())
            .cpu(SysMonitorDataDTO.CpuInfo.builder()
                .usage(BigDecimal.ZERO).cores(1)
                .loadAverage(new BigDecimal[]{BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO})
                .processUsage(BigDecimal.ZERO).build())
            .memory(SysMonitorDataDTO.MemoryInfo.builder()
                .total(0L).used(0L).free(0L).available(0L).usage(BigDecimal.ZERO).build())
            .disk(SysMonitorDataDTO.DiskInfo.builder()
                .total(0L).used(0L).available(0L).usage(BigDecimal.ZERO).build())
            .jvm(SysMonitorDataDTO.JvmInfo.builder()
                .heapUsed(0L).heapMax(0L).heapCommitted(0L).heapUsage(BigDecimal.ZERO)
                .nonHeapUsed(0L).nonHeapMax(-1L).nonHeapCommitted(0L).nonHeapUsage(BigDecimal.ZERO)
                .uptime(0L).build())
            .gc(SysMonitorDataDTO.GcInfo.builder()
                .count(0L).time(0L).avgTime(BigDecimal.ZERO).maxTime(0L)
                .collectors(new SysMonitorDataDTO.GcCollectorInfo[0]).build())
            .threads(SysMonitorDataDTO.ThreadInfo.builder()
                .active(0).peak(0).daemon(0).totalStarted(0L).user(0).deadlocked(0).build())
            .system(SysMonitorDataDTO.SystemInfo.builder()
                .osName("Unknown").osVersion("Unknown").osArch("Unknown")
                .javaVersion("Unknown").javaVendor("Unknown")
                .jvmName("Unknown").jvmVersion("Unknown")
                .appName("RickPan").appVersion("2.1.0")
                .appStartTime(LocalDateTime.now()).systemUptime(0L).jvmUptime(0L).build())
            .build();
    }
}