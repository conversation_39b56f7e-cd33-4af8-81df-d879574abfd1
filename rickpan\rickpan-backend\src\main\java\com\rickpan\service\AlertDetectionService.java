package com.rickpan.service;

import com.rickpan.dto.SysMonitorDataDTO;
import com.rickpan.entity.SysMonitorAlertConfig;
import com.rickpan.entity.SysMonitorAlertLog;
import com.rickpan.repository.SysMonitorAlertConfigRepository;
import com.rickpan.repository.SysMonitorAlertLogRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 告警检测服务
 * 负责检测监控数据是否超过配置的阈值，并触发告警
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AlertDetectionService {

    private final SysMonitorAlertConfigRepository alertConfigRepository;
    private final SysMonitorAlertLogRepository alertLogRepository;
    private final AlertRuleEngine alertRuleEngine;
    private final AlertNotificationService alertNotificationService;

    /**
     * 异步执行告警检测
     * 
     * @param monitorData 监控数据
     */
    @Async
    public void checkAlerts(SysMonitorDataDTO monitorData) {
        if (monitorData == null) {
            log.warn("监控数据为空，跳过告警检测");
            return;
        }

        try {
            log.debug("开始执行告警检测，时间: {}", LocalDateTime.now());
            
            // 获取所有启用的告警配置
            List<SysMonitorAlertConfig> enabledConfigs = getEnabledAlertConfigs();
            
            if (enabledConfigs.isEmpty()) {
                log.debug("没有启用的告警配置，跳过检测");
                return;
            }

            log.debug("找到 {} 个启用的告警配置", enabledConfigs.size());

            // 逐个检查告警配置
            int triggeredCount = 0;
            for (SysMonitorAlertConfig config : enabledConfigs) {
                try {
                    if (checkSingleAlert(monitorData, config)) {
                        triggeredCount++;
                    }
                } catch (Exception e) {
                    log.error("检查告警配置失败: configId={}, metricType={}, metricName={}", 
                        config.getId(), config.getMetricType(), config.getMetricName(), e);
                }
            }

            log.debug("告警检测完成，触发告警数量: {}", triggeredCount);

        } catch (Exception e) {
            log.error("告警检测执行失败", e);
        }
    }

    /**
     * 检查单个告警配置
     * 
     * @param monitorData 监控数据
     * @param config 告警配置
     * @return 是否触发告警
     */
    private boolean checkSingleAlert(SysMonitorDataDTO monitorData, SysMonitorAlertConfig config) {
        // 提取指标值
        Double currentValue = alertRuleEngine.extractMetricValue(
            monitorData, config.getMetricType(), config.getMetricName());

        if (currentValue == null) {
            log.debug("无法提取指标值: metricType={}, metricName={}", 
                config.getMetricType(), config.getMetricName());
            return false;
        }

        // 检查是否超过阈值
        if (!alertRuleEngine.evaluateThreshold(currentValue, config)) {
            // 未超过阈值，检查是否需要自动恢复已存在的告警
            checkAndResolveExistingAlerts(config, currentValue);
            return false;
        }

        // 确定告警级别
        String alertLevel = alertRuleEngine.determineAlertLevel(currentValue, config);
        if (alertLevel == null) {
            return false;
        }

        // 检查是否需要触发新告警（防重复）
        if (!shouldTriggerNewAlert(config, alertLevel)) {
            log.debug("跳过重复告警: metricType={}, metricName={}, level={}", 
                config.getMetricType(), config.getMetricName(), alertLevel);
            return false;
        }

        // 触发告警
        return triggerAlert(config, currentValue, alertLevel);
    }

    /**
     * 触发告警
     * 
     * @param config 告警配置
     * @param currentValue 当前指标值
     * @param alertLevel 告警级别
     * @return 是否成功触发
     */
    @Transactional
    private boolean triggerAlert(SysMonitorAlertConfig config, Double currentValue, String alertLevel) {
        try {
            // 获取阈值
            Double thresholdValue = getThresholdValue(config, alertLevel);

            // 创建告警记录
            SysMonitorAlertLog alertLog = SysMonitorAlertLog.builder()
                .alertConfigId(config.getId())
                .alertType("THRESHOLD_EXCEEDED")
                .metricType(config.getMetricType())
                .metricName(config.getMetricName())
                .alertLevel(alertLevel)
                .alertStatus("ACTIVE")
                .alertTitle(buildAlertTitle(config, alertLevel))
                .alertMessage(buildAlertMessage(config, currentValue, thresholdValue, alertLevel))
                .metricValue(java.math.BigDecimal.valueOf(currentValue))
                .thresholdValue(java.math.BigDecimal.valueOf(thresholdValue))
                .unit(config.getUnit())
                .alertTime(LocalDateTime.now())
                .firstAlertTime(LocalDateTime.now())
                .occurrenceCount(1)
                .build();

            // 保存告警记录
            alertLogRepository.save(alertLog);

            log.warn("触发告警: {} - {}, 当前值: {}{}, 阈值: {}{}", 
                alertLevel, config.getMetricDisplayName(), 
                currentValue, config.getUnit() != null ? config.getUnit() : "",
                thresholdValue, config.getUnit() != null ? config.getUnit() : "");

            // 异步发送告警通知邮件
            alertNotificationService.sendAlertNotification(alertLog);

            return true;

        } catch (Exception e) {
            log.error("触发告警失败: configId={}", config.getId(), e);
            return false;
        }
    }

    /**
     * 检查是否应该触发新告警（防重复告警）
     * 
     * @param config 告警配置
     * @param alertLevel 告警级别
     * @return 是否应该触发
     */
    private boolean shouldTriggerNewAlert(SysMonitorAlertConfig config, String alertLevel) {
        // 查询最近30分钟内相同指标的活跃告警
        LocalDateTime thirtyMinutesAgo = LocalDateTime.now().minusMinutes(30);
        
        List<SysMonitorAlertLog> recentActiveAlerts = alertLogRepository
            .findByMetricTypeAndMetricNameAndAlertTimeBetween(
                config.getMetricType(), 
                config.getMetricName(), 
                thirtyMinutesAgo, 
                LocalDateTime.now()
            )
            .stream()
            .filter(alert -> "ACTIVE".equals(alert.getAlertStatus()))
            .toList();

        if (recentActiveAlerts.isEmpty()) {
            return true; // 没有活跃告警，可以触发
        }

        // 检查是否是告警级别升级
        for (SysMonitorAlertLog existingAlert : recentActiveAlerts) {
            if (isHigherAlertLevel(alertLevel, existingAlert.getAlertLevel())) {
                // 告警级别升级，可以触发新告警
                log.info("告警级别升级: {} -> {}, 指标: {}", 
                    existingAlert.getAlertLevel(), alertLevel, config.getMetricName());
                return true;
            }
        }

        return false; // 相同或更低级别的告警已存在
    }

    /**
     * 检查并自动恢复已存在的告警
     */
    private void checkAndResolveExistingAlerts(SysMonitorAlertConfig config, Double currentValue) {
        // 查询该指标的活跃告警
        List<SysMonitorAlertLog> activeAlerts = alertLogRepository
            .findByMetricTypeAndMetricNameAndAlertTimeBetween(
                config.getMetricType(), 
                config.getMetricName(),
                LocalDateTime.now().minusHours(24), // 查询24小时内的告警
                LocalDateTime.now()
            )
            .stream()
            .filter(alert -> "ACTIVE".equals(alert.getAlertStatus()))
            .toList();

        for (SysMonitorAlertLog activeAlert : activeAlerts) {
            // 检查当前值是否已经低于告警阈值
            if (hasRecovered(currentValue, activeAlert, config)) {
                resolveAlert(activeAlert, "系统自动恢复：指标值已恢复正常");
            }
        }
    }

    /**
     * 检查指标是否已恢复正常
     */
    private boolean hasRecovered(Double currentValue, SysMonitorAlertLog alert, SysMonitorAlertConfig config) {
        Double thresholdValue = alert.getThresholdValue() != null ? alert.getThresholdValue().doubleValue() : null;
        if (thresholdValue == null) {
            return false;
        }

        // 根据比较操作符判断是否已恢复
        String operator = config.getComparisonOperator();
        return switch (operator.toUpperCase()) {
            case "GT", "GTE" -> currentValue < thresholdValue * 0.9; // 低于阈值的90%认为已恢复
            case "LT", "LTE" -> currentValue > thresholdValue * 1.1; // 高于阈值的110%认为已恢复
            default -> false;
        };
    }

    /**
     * 解决告警
     */
    @Transactional
    private void resolveAlert(SysMonitorAlertLog alert, String resolveNote) {
        alert.setAlertStatus("RESOLVED");
        alert.setResolvedTime(LocalDateTime.now());
        alert.setResolveNote(resolveNote);
        alert.setResolvedBy("SYSTEM");
        
        alertLogRepository.save(alert);
        
        log.info("自动解决告警: id={}, 指标: {}, 原因: {}", 
            alert.getId(), alert.getMetricName(), resolveNote);
    }

    /**
     * 获取启用的告警配置
     */
    private List<SysMonitorAlertConfig> getEnabledAlertConfigs() {
        return alertConfigRepository.findAll()
            .stream()
            .filter(config -> config.getEnabled() != null && config.getEnabled())
            .toList();
    }

    /**
     * 获取指定级别对应的阈值
     */
    private Double getThresholdValue(SysMonitorAlertConfig config, String alertLevel) {
        return switch (alertLevel) {
            case "EMERGENCY" -> config.getEmergencyThreshold() != null ? 
                config.getEmergencyThreshold().doubleValue() : null;
            case "CRITICAL" -> config.getCriticalThreshold() != null ? 
                config.getCriticalThreshold().doubleValue() : null;
            case "WARNING" -> config.getWarningThreshold() != null ? 
                config.getWarningThreshold().doubleValue() : null;
            default -> null;
        };
    }

    /**
     * 构建告警标题
     */
    private String buildAlertTitle(SysMonitorAlertConfig config, String alertLevel) {
        String levelText = switch (alertLevel) {
            case "EMERGENCY" -> "紧急告警";
            case "CRITICAL" -> "严重告警";
            case "WARNING" -> "警告告警";
            default -> "系统告警";
        };
        
        return String.format("%s - %s", levelText, config.getMetricDisplayName());
    }

    /**
     * 构建告警消息
     */
    private String buildAlertMessage(SysMonitorAlertConfig config, Double currentValue, 
                                   Double thresholdValue, String alertLevel) {
        return String.format(
            "监控指标 %s 超过%s阈值。\n当前值: %s%s\n阈值: %s%s\n检测时间: %s",
            config.getMetricDisplayName(),
            alertLevel.equals("EMERGENCY") ? "紧急" : 
            alertLevel.equals("CRITICAL") ? "严重" : "警告",
            currentValue,
            config.getUnit() != null ? config.getUnit() : "",
            thresholdValue,
            config.getUnit() != null ? config.getUnit() : "",
            LocalDateTime.now()
        );
    }

    /**
     * 判断是否是更高级别的告警
     */
    private boolean isHigherAlertLevel(String newLevel, String existingLevel) {
        int newLevelValue = getAlertLevelValue(newLevel);
        int existingLevelValue = getAlertLevelValue(existingLevel);
        return newLevelValue > existingLevelValue;
    }

    /**
     * 获取告警级别的数值（用于比较）
     */
    private int getAlertLevelValue(String level) {
        return switch (level) {
            case "WARNING" -> 1;
            case "CRITICAL" -> 2;
            case "EMERGENCY" -> 3;
            default -> 0;
        };
    }
}