import request from '@/api/request'
import type { ApiResponse } from '@/types/api'
import type { FileInfo } from '@/types'

// 首页统计数据类型定义
export interface DashboardStats {
  totalFiles: number
  totalFolders: number
  shareCount: number
  storageUsed: number
  storageQuota: number
}

// 文件类型统计
export interface FileTypeStats {
  name: string
  count: number
  size: number
  icon: string
  color: string
}

// 分享统计
export interface ShareStats {
  total: number
  active: number
  expired: number
  disabled: number
  totalViews: number
}

// 用户统计（管理员可见）
export interface UserStats {
  totalUsers: number
  onlineUsers: number
  vipUsers: number
  basicUsers: number
  adminUsers: number
}

// 首页数据响应
export interface DashboardData {
  stats: DashboardStats
  recentFiles: FileInfo[]
  favoriteFiles: FileInfo[]
  fileTypeStats: FileTypeStats[]
  shareStats: ShareStats
}

/**
 * 获取首页统计数据
 */
export const getDashboardStats = async (): Promise<ApiResponse<DashboardStats>> => {
  try {
    console.log('🚀 获取首页统计数据')
    const response = await request.get<ApiResponse<DashboardStats>>('/dashboard/stats')
    console.log('✅ 首页统计数据响应:', response)
    return response
  } catch (error) {
    console.error('❌ 获取首页统计数据失败:', error)
    throw error
  }
}

/**
 * 获取最近文件
 */
export const getRecentFiles = async (limit: number = 10): Promise<ApiResponse<FileInfo[]>> => {
  try {
    console.log('🚀 获取最近文件')
    const response = await request.get<ApiResponse<FileInfo[]>>(`/dashboard/recent-files?limit=${limit}`)
    console.log('✅ 最近文件响应:', response)
    return response
  } catch (error) {
    console.error('❌ 获取最近文件失败:', error)
    throw error
  }
}

/**
 * 获取收藏文件
 */
export const getFavoriteFiles = async (limit: number = 10): Promise<ApiResponse<FileInfo[]>> => {
  try {
    console.log('🚀 获取收藏文件')
    const response = await request.get<ApiResponse<FileInfo[]>>(`/dashboard/favorite-files?limit=${limit}`)
    console.log('✅ 收藏文件响应:', response)
    return response
  } catch (error) {
    console.error('❌ 获取收藏文件失败:', error)
    throw error
  }
}

/**
 * 获取文件类型统计
 */
export const getFileTypeStats = async (): Promise<ApiResponse<FileTypeStats[]>> => {
  try {
    console.log('🚀 获取文件类型统计')
    const response = await request.get<ApiResponse<FileTypeStats[]>>('/dashboard/file-type-stats')
    console.log('✅ 文件类型统计响应:', response)
    return response
  } catch (error) {
    console.error('❌ 获取文件类型统计失败:', error)
    throw error
  }
}

/**
 * 获取分享统计
 */
export const getShareStats = async (): Promise<ApiResponse<ShareStats>> => {
  try {
    console.log('🚀 获取分享统计')
    const response = await request.get<ApiResponse<ShareStats>>('/dashboard/share-stats')
    console.log('✅ 分享统计响应:', response)
    return response
  } catch (error) {
    console.error('❌ 获取分享统计失败:', error)
    throw error
  }
}

/**
 * 获取用户统计（管理员专用）
 */
export const getUserStats = async (): Promise<ApiResponse<UserStats>> => {
  try {
    console.log('🚀 获取用户统计数据（管理员）')
    const response = await request.get<ApiResponse<UserStats>>('/admin/dashboard/user-stats')
    console.log('✅ 用户统计响应:', response)
    return response
  } catch (error) {
    console.error('❌ 获取用户统计失败:', error)
    throw error
  }
}

/**
 * 获取首页完整数据
 */
export const getDashboardData = async (): Promise<ApiResponse<DashboardData>> => {
  try {
    console.log('🚀 获取首页完整数据')
    const response = await request.get<ApiResponse<DashboardData>>('/dashboard/data')
    console.log('✅ 首页完整数据响应:', response)
    return response
  } catch (error) {
    console.error('❌ 获取首页完整数据失败:', error)
    throw error
  }
}
