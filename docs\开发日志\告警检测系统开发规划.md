# 告警检测系统开发规划文档

## 1. 开发阶段规划

### 第一阶段：核心告警检测功能（优先级：高）
**目标**：实现基础的阈值检测和告警触发
**预估工时**：2个工作日

#### 1.1 开发任务清单
- [ ] 创建AlertDetectionService服务类
- [ ] 实现AlertRuleEngine规则引擎
- [ ] 集成到SysMonitorCollectorService
- [ ] 实现基础的阈值比较逻辑
- [ ] 添加告警记录保存功能

#### 1.2 具体实现文件
```
📁 src/main/java/com/rickpan/service/
  ├── AlertDetectionService.java         # 核心检测服务
  ├── AlertRuleEngine.java              # 规则引擎
  └── AlertAggregationService.java      # 聚合服务

📁 src/main/java/com/rickpan/dto/
  └── AlertTriggerDTO.java              # 告警触发数据传输对象
```

### 第二阶段：邮件通知功能（优先级：高）
**目标**：实现管理员邮件通知功能
**预估工时**：1个工作日

#### 2.1 开发任务清单
- [ ] 扩展EmailService添加告警邮件方法
- [ ] 创建告警邮件HTML模板
- [ ] 实现管理员用户查询功能
- [ ] 实现异步邮件发送
- [ ] 添加邮件发送失败重试机制

#### 2.2 具体实现文件
```
📁 src/main/java/com/rickpan/service/
  ├── EmailService.java                 # 扩展现有邮件服务
  └── AlertNotificationService.java     # 告警通知服务

📁 src/main/java/com/rickpan/repository/
  └── UserRepository.java               # 添加管理员查询方法
```

### 第三阶段：告警聚合和去重（优先级：中）
**目标**：避免重复告警，实现智能聚合
**预估工时**：1个工作日

#### 3.1 开发任务清单
- [ ] 实现告警时间窗口聚合逻辑
- [ ] 添加告警状态自动恢复机制
- [ ] 实现告警级别升级检测
- [ ] 优化告警查询性能

### 第四阶段：性能优化和完善（优先级：低）
**目标**：优化性能，完善功能
**预估工时**：1个工作日

#### 4.1 开发任务清单
- [ ] 添加告警配置缓存机制
- [ ] 优化数据库查询性能
- [ ] 添加详细的错误日志
- [ ] 实现告警统计功能

## 2. 详细开发步骤

### 阶段1：核心检测功能实现

#### Step 1.1：创建AlertDetectionService
```java
@Service
@RequiredArgsConstructor
@Slf4j
public class AlertDetectionService {
    
    private final SysMonitorAlertConfigRepository alertConfigRepository;
    private final SysMonitorAlertLogRepository alertLogRepository;
    private final AlertRuleEngine alertRuleEngine;
    private final AlertNotificationService notificationService;
    
    @Async
    public void checkAlerts(SysMonitorDataDTO monitorData) {
        // 检测逻辑实现
    }
}
```

#### Step 1.2：创建AlertRuleEngine
```java
@Component
@Slf4j
public class AlertRuleEngine {
    
    public boolean evaluateThreshold(Double currentValue, SysMonitorAlertConfig config) {
        // 阈值比较逻辑
    }
    
    public String determineAlertLevel(Double currentValue, SysMonitorAlertConfig config) {
        // 告警级别判断
    }
}
```

#### Step 1.3：集成到数据采集流程
```java
// 在SysMonitorCollectorService中添加
@Autowired
private AlertDetectionService alertDetectionService;

private void collectData() {
    // 现有采集逻辑...
    sysMonitorService.saveRealtimeData(monitorData);
    
    // 新增：异步告警检测
    alertDetectionService.checkAlerts(monitorData);
}
```

### 阶段2：邮件通知功能实现

#### Step 2.1：扩展EmailService
```java
// 在EmailService中添加新方法
public void sendAlertNotification(List<String> adminEmails, AlertInfo alertInfo) {
    // 告警邮件发送逻辑
}

private String buildAlertEmailContent(AlertInfo alertInfo) {
    // 告警邮件HTML模板
}
```

#### Step 2.2：创建AlertNotificationService
```java
@Service
@RequiredArgsConstructor
@Slf4j
public class AlertNotificationService {
    
    private final EmailService emailService;
    private final UserRepository userRepository;
    
    @Async
    public void sendAlertNotification(SysMonitorAlertLog alertLog) {
        // 通知发送逻辑
    }
}
```

#### Step 2.3：添加用户查询方法
```java
// 在UserRepository中添加
List<User> findByUserType(UserType userType);
```

## 3. 开发约束和注意事项

### 3.1 最小改动原则
- ✅ 不修改现有数据库表结构
- ✅ 不影响现有数据采集流程性能
- ✅ 异步处理避免阻塞主流程
- ✅ 复用现有EmailService代码

### 3.2 错误处理策略
- 告警检测异常不影响数据采集
- 邮件发送失败记录日志但不中断流程
- 所有异步操作都要有完整的异常处理

### 3.3 性能要求
- 告警检测延迟 < 5秒
- 邮件发送不阻塞主流程
- 数据库查询优化，避免N+1问题

### 3.4 日志记录要求
```java
// 关键操作日志示例
log.info("告警检测开始, 检测指标数量: {}", configCount);
log.warn("触发告警: {}, 当前值: {}, 阈值: {}", metricName, currentValue, threshold);
log.error("告警检测失败: {}", e.getMessage(), e);
```

## 4. 测试验证计划

### 4.1 单元测试
- AlertRuleEngine阈值比较逻辑
- AlertDetectionService检测逻辑
- 邮件模板渲染功能

### 4.2 集成测试
- 完整的告警触发流程
- 邮件发送功能验证
- 性能影响测试

### 4.3 功能测试场景
```
场景1：CPU使用率超过警告阈值
- 设置CPU警告阈值为70%
- 模拟CPU使用率达到75%
- 验证告警触发和邮件发送

场景2：内存使用率超过严重阈值
- 设置内存严重阈值为90%
- 模拟内存使用率达到95%
- 验证严重级别告警触发

场景3：告警配置禁用状态
- 禁用某个告警配置
- 超过阈值时验证不触发告警

场景4：通知开关关闭
- 关闭通知开关
- 触发告警时验证不发送邮件
```

## 5. 部署和发布计划

### 5.1 开发环境验证
- 本地开发环境功能测试
- 邮件发送功能验证
- 性能基准测试

### 5.2 生产环境部署
- 渐进式部署，先部署检测功能
- 监控系统性能指标
- 验证邮件发送配置

### 5.3 回滚方案
- 可通过配置开关快速禁用告警功能
- 保留原有数据采集流程不变
- 数据库表结构无变更，回滚风险低

## 6. 开发完成标准

### 6.1 功能完成标准
- ✅ CPU/内存/磁盘/JVM指标超过阈值时正确触发告警
- ✅ 管理员用户能收到格式正确的告警邮件
- ✅ 告警配置的启用/禁用状态正确控制告警行为
- ✅ 通知开关正确控制邮件发送行为
- ✅ 告警记录正确保存到数据库

### 6.2 性能完成标准
- ✅ 告警检测不影响数据采集性能（延迟增加<10%）
- ✅ 邮件发送异步处理，不阻塞主流程
- ✅ 数据库查询性能优化，响应时间<500ms

### 6.3 质量完成标准
- ✅ 代码覆盖率>80%
- ✅ 无阻塞性异常和错误
- ✅ 完整的错误日志记录
- ✅ 符合Spring Boot最佳实践

## 7. 风险控制措施

### 7.1 开发风险
**风险**：集成到数据采集流程可能影响性能
**控制**：异步处理+性能监控+回滚机制

### 7.2 功能风险
**风险**：告警检测逻辑错误导致误报或漏报
**控制**：充分的单元测试+集成测试+灰度发布

### 7.3 运维风险
**风险**：大量告警邮件可能影响邮件服务
**控制**：告警聚合+频率限制+邮件服务监控

---

**总结**：开发规划完整，技术方案可行，风险可控，建议按计划执行开发。