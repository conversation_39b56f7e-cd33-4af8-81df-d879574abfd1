package com.rickpan.service;

import com.rickpan.dto.SysMonitorDataDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 系统监控数据采集服务
 * 负责定时采集系统监控数据并存储
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(name = "sys.monitor.enabled", havingValue = "true", matchIfMissing = true)
public class SysMonitorCollectorService {

    private final SysMonitorService sysMonitorService;
    private final AlertDetectionService alertDetectionService;
    
    // 数据采集统计
    private final AtomicLong collectionCount = new AtomicLong(0);
    private final AtomicLong successCount = new AtomicLong(0);
    private final AtomicLong errorCount = new AtomicLong(0);
    private final AtomicBoolean isCollecting = new AtomicBoolean(false);
    
    // 最后一次采集时间
    private volatile LocalDateTime lastCollectionTime;
    private volatile LocalDateTime lastSuccessTime;
    
    // 控制标志
    private final AtomicBoolean enabled = new AtomicBoolean(true);

    /**
     * 服务初始化
     */
    @PostConstruct
    public void init() {
        log.info("系统监控数据采集服务初始化完成");
        log.info("采集间隔: 5秒, 历史数据保存间隔: 5分钟");
        
        // 初始化系统信息
        try {
            updateSystemInfo();
            log.info("系统基础信息初始化完成");
        } catch (Exception e) {
            log.error("系统基础信息初始化失败", e);
        }
    }

    /**
     * 服务销毁前的清理工作
     */
    @PreDestroy
    public void destroy() {
        enabled.set(false);
        log.info("系统监控数据采集服务已停止");
        logCollectionStatistics();
    }

    /**
     * 实时数据采集任务 - 每5秒执行一次
     * 用于更新实时监控数据
     */
    @Scheduled(fixedRate = 5000, initialDelay = 10000)
    @Async("monitorTaskExecutor")
    public void collectRealtimeData() {
        if (!enabled.get() || isCollecting.get()) {
            return;
        }

        try {
            isCollecting.set(true);
            lastCollectionTime = LocalDateTime.now();
            
            log.debug("开始采集实时监控数据");
            
            // 采集监控数据
            SysMonitorDataDTO monitorData = sysMonitorService.getRealtimeData();
            
            // 保存实时数据
            sysMonitorService.saveRealtimeData(monitorData);
            
            // 异步执行告警检测
            alertDetectionService.checkAlerts(monitorData);
            
            // 更新统计
            collectionCount.incrementAndGet();
            successCount.incrementAndGet();
            lastSuccessTime = LocalDateTime.now();
            
            log.debug("实时监控数据采集完成, CPU: {}%, 内存: {}%, 磁盘: {}%", 
                monitorData.getCpu().getUsage(),
                monitorData.getMemory().getUsage(), 
                monitorData.getDisk().getUsage());
                
        } catch (Exception e) {
            errorCount.incrementAndGet();
            log.error("实时监控数据采集失败", e);
        } finally {
            isCollecting.set(false);
        }
    }

    /**
     * 历史数据保存任务 - 每5分钟执行一次
     * 用于保存历史监控数据
     */
    @Scheduled(fixedRate = 300000, initialDelay = 60000)
    @Async("monitorTaskExecutor")
    public void saveHistoryData() {
        if (!enabled.get()) {
            return;
        }

        try {
            log.debug("开始保存历史监控数据");
            
            // 获取当前监控数据
            SysMonitorDataDTO monitorData = sysMonitorService.getRealtimeData();
            
            // 保存历史数据
            sysMonitorService.saveHistoryData(monitorData);
            
            // 异步执行告警检测（历史数据保存时也进行检测）
            alertDetectionService.checkAlerts(monitorData);
            
            log.debug("历史监控数据保存完成");
            
        } catch (Exception e) {
            log.error("历史监控数据保存失败", e);
        }
    }

    /**
     * 系统信息更新任务 - 每小时执行一次
     * 用于更新系统基础信息
     */
    @Scheduled(fixedRate = 3600000, initialDelay = 300000)
    @Async("monitorTaskExecutor")
    public void updateSystemInfo() {
        if (!enabled.get()) {
            return;
        }

        try {
            log.debug("开始更新系统基础信息");
            
            // 获取当前监控数据
            SysMonitorDataDTO monitorData = sysMonitorService.getRealtimeData();
            
            // 更新系统信息
            sysMonitorService.updateSystemInfo(monitorData);
            
            log.debug("系统基础信息更新完成");
            
        } catch (Exception e) {
            log.error("系统基础信息更新失败", e);
        }
    }

    /**
     * 采集统计日志任务 - 每小时执行一次
     */
    @Scheduled(fixedRate = 3600000, initialDelay = 1800000)
    public void logCollectionStatistics() {
        long total = collectionCount.get();
        long success = successCount.get();
        long error = errorCount.get();
        
        double successRate = total > 0 ? (double) success / total * 100 : 0;
        
        log.info("=== 监控数据采集统计 ===");
        log.info("总采集次数: {}", total);
        log.info("成功次数: {}", success);
        log.info("失败次数: {}", error);
        log.info("成功率: {:.2f}%", successRate);
        log.info("最后采集时间: {}", formatTime(lastCollectionTime));
        log.info("最后成功时间: {}", formatTime(lastSuccessTime));
        log.info("当前状态: {}", enabled.get() ? "运行中" : "已停止");
        log.info("========================");
    }

    /**
     * 数据清理任务 - 每天凌晨2点执行
     * 清理过期的实时数据（保留最近1小时）
     */
    @Scheduled(cron = "0 0 2 * * ?")
    @Async("monitorTaskExecutor") 
    public void cleanupOldData() {
        if (!enabled.get()) {
            return;
        }

        try {
            log.info("开始清理过期监控数据");
            
            LocalDateTime cutoffTime = LocalDateTime.now().minusHours(1);
            
            // 这里可以添加数据清理逻辑
            // 例如：删除1小时前的实时数据记录
            
            log.info("监控数据清理完成");
            
        } catch (Exception e) {
            log.error("监控数据清理失败", e);
        }
    }

    /**
     * 健康检查 - 每分钟执行一次
     * 检查采集服务的健康状态
     */
    @Scheduled(fixedRate = 60000, initialDelay = 120000)
    public void healthCheck() {
        try {
            // 检查最后成功时间是否超过阈值
            if (lastSuccessTime != null) {
                long minutesSinceLastSuccess = java.time.Duration.between(lastSuccessTime, LocalDateTime.now()).toMinutes();
                
                if (minutesSinceLastSuccess > 5) {
                    log.warn("监控数据采集异常: 已超过{}分钟未成功采集数据", minutesSinceLastSuccess);
                }
            }
            
            // 检查错误率
            long total = collectionCount.get();
            long error = errorCount.get();
            if (total > 10) { // 至少有10次采集记录
                double errorRate = (double) error / total * 100;
                if (errorRate > 10) { // 错误率超过10%
                    log.warn("监控数据采集错误率较高: {:.2f}% ({}/{})", errorRate, error, total);
                }
            }
            
        } catch (Exception e) {
            log.error("健康检查失败", e);
        }
    }

    // ==================== 公共方法 ====================

    /**
     * 启用数据采集
     */
    public void enableCollection() {
        enabled.set(true);
        log.info("系统监控数据采集已启用");
    }

    /**
     * 禁用数据采集
     */
    public void disableCollection() {
        enabled.set(false);
        log.info("系统监控数据采集已禁用");
    }

    /**
     * 检查采集服务是否启用
     * 
     * @return 是否启用
     */
    public boolean isEnabled() {
        return enabled.get();
    }

    /**
     * 检查是否正在采集数据
     * 
     * @return 是否正在采集
     */
    public boolean isCollecting() {
        return isCollecting.get();
    }

    /**
     * 获取采集统计信息
     * 
     * @return 统计信息
     */
    public CollectionStatistics getStatistics() {
        return new CollectionStatistics(
            collectionCount.get(),
            successCount.get(), 
            errorCount.get(),
            lastCollectionTime,
            lastSuccessTime,
            enabled.get(),
            isCollecting.get()
        );
    }

    /**
     * 手动触发一次数据采集
     * 
     * @return 采集结果
     */
    public SysMonitorDataDTO manualCollect() {
        log.info("手动触发监控数据采集");
        
        try {
            SysMonitorDataDTO monitorData = sysMonitorService.getRealtimeData();
            sysMonitorService.saveRealtimeData(monitorData);
            
            // 手动采集时也执行告警检测
            alertDetectionService.checkAlerts(monitorData);
            
            collectionCount.incrementAndGet();
            successCount.incrementAndGet();
            lastSuccessTime = LocalDateTime.now();
            
            log.info("手动监控数据采集完成");
            return monitorData;
            
        } catch (Exception e) {
            errorCount.incrementAndGet();
            log.error("手动监控数据采集失败", e);
            throw new RuntimeException("监控数据采集失败: " + e.getMessage());
        }
    }

    /**
     * 重置采集统计
     */
    public void resetStatistics() {
        collectionCount.set(0);
        successCount.set(0);
        errorCount.set(0);
        lastCollectionTime = null;
        lastSuccessTime = null;
        log.info("监控数据采集统计已重置");
    }

    // ==================== 私有方法 ====================

    /**
     * 格式化时间
     */
    private String formatTime(LocalDateTime time) {
        if (time == null) {
            return "未知";
        }
        return time.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    // ==================== 内部类 ====================

    /**
     * 采集统计信息
     */
    public static class CollectionStatistics {
        private final long totalCollections;
        private final long successCollections;
        private final long errorCollections;
        private final LocalDateTime lastCollectionTime;
        private final LocalDateTime lastSuccessTime;
        private final boolean enabled;
        private final boolean collecting;

        public CollectionStatistics(long totalCollections, long successCollections, long errorCollections,
                                   LocalDateTime lastCollectionTime, LocalDateTime lastSuccessTime,
                                   boolean enabled, boolean collecting) {
            this.totalCollections = totalCollections;
            this.successCollections = successCollections;
            this.errorCollections = errorCollections;
            this.lastCollectionTime = lastCollectionTime;
            this.lastSuccessTime = lastSuccessTime;
            this.enabled = enabled;
            this.collecting = collecting;
        }

        // Getters
        public long getTotalCollections() { return totalCollections; }
        public long getSuccessCollections() { return successCollections; }
        public long getErrorCollections() { return errorCollections; }
        public LocalDateTime getLastCollectionTime() { return lastCollectionTime; }
        public LocalDateTime getLastSuccessTime() { return lastSuccessTime; }
        public boolean isEnabled() { return enabled; }
        public boolean isCollecting() { return collecting; }
        
        public double getSuccessRate() {
            return totalCollections > 0 ? (double) successCollections / totalCollections * 100 : 0;
        }
    }
}