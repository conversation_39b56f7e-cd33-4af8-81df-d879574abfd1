# SysMonitor系统监控独立页面方案 - 需求文档

## 📋 目录
- [项目概述](#项目概述)
- [功能需求](#功能需求)
- [技术需求](#技术需求)
- [性能需求](#性能需求)
- [界面需求](#界面需求)
- [数据需求](#数据需求)
- [安全需求](#安全需求)
- [兼容性需求](#兼容性需求)

---

## 🎯 项目概述

### 项目背景
基于现有RickPan系统，为管理员提供专业级的服务器运行状态监控功能，通过独立页面展示实时系统资源使用情况和历史趋势分析。

### 项目目标
- 🎪 **实时监控**：提供CPU、内存、磁盘、JVM等关键指标的实时监控
- 📊 **数据可视化**：通过ECharts图表展示资源使用趋势和历史数据
- 🔔 **智能告警**：支持阈值设置和告警通知功能
- 📱 **响应式设计**：适配桌面端和移动端访问
- 🔒 **权限控制**：仅管理员可访问系统监控功能

### 统一命名规范
所有相关文件和组件采用 **`SysMonitor`** 前缀命名：
- 前端组件：`SysMonitor*.vue`
- 后端类：`SysMonitor*`  
- 数据库表：`sys_monitor_*`
- API路径：`/api/sys-monitor/*`

---

## 🚀 功能需求

### F1. 实时监控功能
**优先级：高**

#### F1.1 系统资源监控
- **CPU监控**
  - CPU使用率（百分比）
  - CPU核心数量
  - 系统负载（1分钟、5分钟、15分钟平均值）
  
- **内存监控**  
  - 系统总内存
  - 已使用内存
  - 空闲内存
  - 内存使用率
  - 缓存和缓冲区使用情况

- **磁盘监控**
  - 磁盘总空间
  - 已使用空间  
  - 可用空间
  - 磁盘使用率
  - 磁盘I/O统计

#### F1.2 JVM监控
- **JVM内存**
  - 堆内存使用情况（已用/最大/已分配）
  - 非堆内存使用情况
  - 新生代/老年代内存分布
  
- **垃圾回收**
  - GC次数统计
  - GC总耗时
  - GC平均耗时
  - 各类型GC收集器统计

- **线程监控**
  - 活跃线程数
  - 峰值线程数
  - 守护线程数
  - 线程状态分布

#### F1.3 应用监控
- **系统信息**
  - Java版本信息
  - 操作系统信息
  - 服务器版本信息
  - 系统运行时间
  
- **网络监控**（扩展功能）
  - 网络连接数
  - 网络流量统计
  - 端口监听状态

### F2. 数据可视化功能
**优先级：高**

#### F2.1 实时图表
- **仪表盘样式**：CPU、内存、磁盘使用率采用仪表盘样式展示
- **进度条样式**：各类资源使用情况采用进度条展示
- **数值卡片**：关键指标采用数值卡片形式突出显示

#### F2.2 趋势图表
- **折线图**：展示资源使用率的时间趋势（最近1小时、6小时、24小时）
- **面积图**：展示内存使用分布和变化趋势
- **柱状图**：展示GC活动和性能指标

#### F2.3 历史数据分析
- **时间范围选择**：支持查看不同时间段的历史数据
- **数据对比**：支持不同时间段的数据对比分析
- **趋势预测**：基于历史数据提供简单的趋势预测

### F3. 告警功能
**优先级：中**

#### F3.1 阈值设置
- **默认阈值**：系统预设告警阈值（CPU>80%、内存>85%、磁盘>90%）
- **自定义阈值**：管理员可自定义各项指标的告警阈值
- **多级告警**：支持警告、严重、紧急三个级别

#### F3.2 告警通知
- **实时提醒**：达到阈值时页面实时显示告警信息
- **告警历史**：记录和展示历史告警信息
- **告警统计**：提供告警频次和类型统计

### F4. 数据管理功能
**优先级：中**

#### F4.1 数据存储
- **实时数据**：内存中保持最近1小时的实时数据
- **历史数据**：数据库中保存历史数据（保留策略：30天）
- **数据压缩**：对历史数据进行适当压缩和聚合

#### F4.2 数据导出
- **Excel导出**：支持将监控数据导出为Excel文件
- **CSV导出**：支持CSV格式数据导出
- **报告生成**：自动生成系统监控报告

---

## 🛠️ 技术需求

### T1. 后端技术要求
**基于现有Spring Boot架构**

#### T1.1 技术栈
- **框架**：Spring Boot 3.2+
- **监控API**：Java Management Extensions (JMX)
- **数据采集**：OperatingSystemMXBean、MemoryMXBean、GarbageCollectorMXBean
- **数据库**：MySQL 8.0+（复用现有数据库）
- **缓存**：Redis（可选，用于实时数据缓存）

#### T1.2 架构设计
- **分层架构**：Controller → Service → Repository
- **定时任务**：使用@Scheduled实现定时数据采集
- **异步处理**：使用@Async处理数据采集和存储
- **事务管理**：使用@Transactional确保数据一致性

### T2. 前端技术要求
**基于现有Vue 3 + Element Plus架构**

#### T2.1 技术栈
- **框架**：Vue 3 + Composition API + TypeScript
- **UI库**：Element Plus（复用现有组件库）
- **图表库**：ECharts 5.6+
- **状态管理**：Pinia（可选，用于数据状态管理）
- **HTTP客户端**：复用现有request.ts工具

#### T2.2 组件设计
- **页面组件**：SysMonitorDashboard.vue（主页面）
- **功能组件**：拆分为多个可复用的子组件
- **组合式函数**：封装数据获取和图表渲染逻辑
- **TypeScript类型**：完整的类型定义和接口

### T3. 数据库设计要求

#### T3.1 表结构设计
```sql
-- 实时监控数据表
sys_monitor_realtime
- id (主键)
- cpu_usage (CPU使用率)  
- memory_usage (内存使用率)
- disk_usage (磁盘使用率)
- jvm_usage (JVM使用率)
- created_at (创建时间)
- updated_at (更新时间)

-- 历史监控数据表
sys_monitor_history  
- id (主键)
- cpu_usage (CPU使用率)
- memory_total/used/free (内存详情)
- disk_total/used/available (磁盘详情)  
- jvm_heap/non_heap (JVM内存详情)
- gc_count/gc_time (GC统计)
- thread_count (线程数)
- record_time (记录时间)
- created_at (创建时间)

-- 告警配置表
sys_monitor_alert_config
- id (主键) 
- metric_type (指标类型)
- warning_threshold (警告阈值)
- critical_threshold (严重阈值)
- enabled (是否启用)
- created_at/updated_at

-- 告警记录表  
sys_monitor_alert_log
- id (主键)
- alert_type (告警类型)
- metric_type (指标类型) 
- alert_level (告警级别)
- alert_message (告警消息)
- metric_value (指标值)
- threshold_value (阈值)
- resolved_at (解决时间)
- created_at (创建时间)
```

---

## ⚡ 性能需求

### P1. 响应时间要求
- **API响应时间**：所有监控数据API响应时间 < 1秒
- **页面加载时间**：监控页面首次加载时间 < 3秒
- **图表渲染时间**：图表渲染和更新时间 < 500ms
- **数据刷新间隔**：实时数据每5秒自动刷新

### P2. 数据处理要求
- **并发处理**：支持多个管理员同时访问监控页面
- **数据准确性**：监控数据采集准确率 > 99%
- **数据完整性**：历史数据保存完整，无数据丢失
- **内存使用**：监控功能本身的内存占用 < 100MB

### P3. 扩展性要求
- **监控指标扩展**：支持新增监控指标，无需大规模重构
- **数据量扩展**：支持大量历史数据存储和查询
- **用户规模扩展**：支持未来更多管理员用户访问

---

## 🎨 界面需求

### U1. 设计风格要求
- **设计语言**：遵循现有RickPan系统的设计风格
- **色彩方案**：采用Element Plus默认主题色彩
- **图标系统**：使用Element Plus Icons，通过Context7 MCP服务选择合适图标
- **响应式设计**：适配1920px桌面端和768px以下移动端

### U2. 布局要求

#### U2.1 页面布局
```
┌─────────────────────────────────────────────┐
│  导航栏 (复用现有DashboardLayout)              │
├─────────────────────────────────────────────┤
│  页面标题 + 最后更新时间 + 刷新按钮               │
├─────────────────────────────────────────────┤
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌──────┐  │
│  │ CPU卡片 │ │ 内存卡片 │ │ 磁盘卡片 │ │JVM卡片│  │
│  └─────────┘ └─────────┘ └─────────┘ └──────┘  │
├─────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────┐  │
│  │        实时趋势图表区域                   │  │
│  └─────────────────────────────────────────┘  │
├─────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────────────────┐  │
│  │  系统信息卡片  │ │      告警信息卡片          │  │
│  └─────────────┘ └─────────────────────────┘  │
└─────────────────────────────────────────────┘
```

#### U2.2 移动端适配
- **卡片堆叠**：卡片改为单列垂直堆叠布局
- **图表适配**：图表自适应小屏幕尺寸
- **交互优化**：增大触摸目标，优化手势操作

### U3. 交互要求
- **实时更新**：数据自动刷新，有加载状态提示
- **手动刷新**：提供手动刷新按钮
- **图表交互**：支持图表缩放、悬停查看详细数据
- **时间范围选择**：支持选择不同的历史数据查看范围

---

## 📊 数据需求

### D1. 数据采集要求
- **采集频率**：每5秒采集一次实时数据
- **数据来源**：JMX API、操作系统API、Runtime API
- **数据格式**：JSON格式，统一数据结构
- **数据验证**：采集前进行数据有效性验证

### D2. 数据存储要求
- **实时数据**：内存中保存最近1小时数据（720个数据点）
- **历史数据**：数据库中按分钟聚合保存，保留30天
- **数据备份**：定期备份历史监控数据
- **数据清理**：自动清理过期历史数据

### D3. 数据安全要求
- **访问控制**：仅管理员角色可访问监控数据
- **数据加密**：敏感数据传输采用HTTPS加密
- **数据脱敏**：系统信息展示时进行适当脱敏
- **审计日志**：记录监控数据的访问日志

---

## 🔐 安全需求

### S1. 权限控制
- **角色验证**：仅ADMIN角色用户可访问系统监控功能
- **会话验证**：验证用户登录状态和会话有效性
- **API鉴权**：所有监控API需要管理员权限验证
- **菜单控制**：非管理员用户不显示系统监控菜单项

### S2. 数据安全
- **传输安全**：所有数据传输使用HTTPS协议
- **存储安全**：数据库连接和存储采用安全配置
- **日志安全**：避免在日志中记录敏感信息
- **错误处理**：安全的错误信息提示，不暴露系统详情

### S3. 系统安全
- **防护措施**：防止监控功能被恶意利用导致系统负载
- **资源限制**：合理控制监控功能的资源消耗
- **故障隔离**：监控功能故障不影响主系统运行
- **降级策略**：异常情况下的功能降级处理

---

## 🌐 兼容性需求

### C1. 浏览器兼容性
- **现代浏览器**：Chrome 90+、Firefox 90+、Safari 14+、Edge 90+
- **移动浏览器**：iOS Safari、Android Chrome
- **功能降级**：低版本浏览器提供基础功能支持

### C2. 设备兼容性
- **桌面端**：1920x1080、1366x768、2560x1440分辨率
- **平板端**：iPad、Android平板横竖屏适配
- **手机端**：iPhone、Android手机适配

### C3. 系统兼容性
- **操作系统**：Windows、Linux、macOS环境下的Java应用
- **Java版本**：Java 17+
- **数据库版本**：MySQL 8.0+

---

## 📈 成功标准

### 功能完整性
- ✅ 所有F1-F4功能需求100%实现
- ✅ 支持实时监控和历史数据分析
- ✅ 告警功能正常工作
- ✅ 数据导出功能可用

### 性能达标
- ✅ API响应时间 < 1秒
- ✅ 页面加载时间 < 3秒  
- ✅ 图表渲染时间 < 500ms
- ✅ 数据准确率 > 99%

### 用户体验
- ✅ 界面美观，符合现有系统风格
- ✅ 交互流畅，响应迅速
- ✅ 移动端适配良好
- ✅ 错误处理友好

### 技术质量
- ✅ 代码规范，符合现有项目标准
- ✅ 组件复用性强，易于维护
- ✅ 类型安全，TypeScript覆盖率100%
- ✅ 单元测试覆盖核心功能

---

## 📝 备注说明

1. **开发优先级**：F1 > F2 > F3 > F4，优先实现核心监控功能
2. **渐进开发**：支持分阶段开发和部署，每个阶段都有可用功能
3. **扩展预留**：为未来功能扩展预留接口和数据结构
4. **文档同步**：开发过程中同步更新技术文档和用户手册

---

*文档版本：v1.0*  
*创建时间：2025-01-15*  
*负责人：开发团队*