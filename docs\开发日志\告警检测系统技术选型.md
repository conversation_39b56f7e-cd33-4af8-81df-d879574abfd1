# 告警检测系统技术选型文档

## 1. 技术架构选型

### 1.1 核心技术栈
- **Spring Boot 3.2+** - 基础框架
- **Spring Data JPA** - 数据持久化
- **Spring Mail** - 邮件发送（复用现有EmailService）
- **Spring Task** - 异步任务处理
- **Java 17** - 编程语言

### 1.2 关键组件设计

#### 1.2.1 告警检测引擎
- **AlertDetectionService** - 核心告警检测服务
- **AlertRuleEngine** - 规则引擎，处理阈值比较逻辑
- **AlertNotificationService** - 告警通知服务
- **AlertAggregationService** - 告警聚合服务

#### 1.2.2 数据流设计
```
数据采集 → 告警检测 → 告警触发 → 通知发送 → 状态更新
    ↓           ↓         ↓        ↓         ↓
SysMonitor → Alert    → Alert   → Email   → Alert
Collector   Detection  Trigger   Service   Update
Service     Service    Service   
```

### 1.3 集成点设计

#### 1.3.1 数据采集集成
- 在`SysMonitorCollectorService.collectData()`中集成
- 采集完成后触发告警检测
- 异步处理，不影响主流程性能

#### 1.3.2 邮件服务集成
- 扩展现有`EmailService`，添加告警邮件模板
- 复用邮件配置和发送逻辑
- 支持HTML格式的告警邮件

#### 1.3.3 用户查询集成
- 查询`User`表中`userType = UserType.ADMIN`的用户
- 获取管理员邮箱列表进行通知
- 注意：使用`userType`字段而非已弃用的`role`字段

## 2. 实现策略

### 2.1 最小改动原则
- 不修改现有数据库表结构
- 不影响现有数据采集流程
- 扩展而非修改现有服务

### 2.2 模块化设计
- 独立的告警检测模块
- 可插拔的通知机制
- 配置驱动的规则引擎

### 2.3 异步处理策略
- 告警检测异步执行
- 邮件发送异步处理
- 使用Spring的@Async注解

## 3. 数据流程设计

### 3.1 检测流程
1. 数据采集完成后触发检测
2. 读取启用的告警配置
3. 逐项检查监控指标
4. 触发符合条件的告警
5. 异步发送通知邮件

### 3.2 聚合策略
- 基于指标类型+指标名称+时间窗口聚合
- 30分钟内相同告警不重复发送
- 告警级别升级时重新发送

### 3.3 恢复机制
- 指标恢复正常时自动标记告警为已解决
- 支持手动确认和处理告警

## 4. 错误处理策略

### 4.1 容错设计
- 告警检测失败不影响数据采集
- 邮件发送失败不影响告警记录
- 完整的异常日志记录

### 4.2 重试机制
- 邮件发送失败自动重试3次
- 指数退避策略避免频繁重试
- 最终失败时记录错误日志

## 5. 性能考量

### 5.1 检测性能
- 批量读取告警配置，避免频繁数据库查询
- 内存缓存活跃配置，提高检测效率
- 异步处理避免阻塞主流程

### 5.2 通知性能
- 批量查询管理员用户
- 异步发送邮件通知
- 合并相似告警减少邮件数量