package com.rickpan.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 系统监控查询参数传输对象
 * 用于历史数据查询等场景
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysMonitorQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 时间范围类型：1h/6h/24h/7d/30d 或 custom
     */
    @NotBlank(message = "时间范围不能为空")
    private String timeRange;

    /**
     * 自定义开始时间（当timeRange为custom时使用）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 自定义结束时间（当timeRange为custom时使用）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 监控指标类型数组：cpu,memory,disk,jvm
     */
    private String[] metrics;

    /**
     * 数据聚合间隔：1m/5m/15m/1h
     */
    @Builder.Default
    private String interval = "5m";

    /**
     * 最大返回数据点数量
     */
    @Builder.Default
    private Integer maxDataPoints = 100;

    /**
     * 是否包含详细信息
     */
    @Builder.Default
    private Boolean includeDetails = false;

    /**
     * 排序方式：asc/desc
     */
    @Builder.Default
    private String sort = "asc";

    /**
     * 页码（用于分页查询）
     */
    @Builder.Default
    private Integer page = 1;

    /**
     * 每页大小（用于分页查询）
     */
    @Builder.Default
    private Integer size = 20;
}