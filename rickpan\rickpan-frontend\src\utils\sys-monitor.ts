/**
 * 系统监控工具函数
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

import type { ByteFormatOptions, TimeFormatOptions } from '@/types/sys-monitor'

// ==================== 数据格式化工具 ====================

/**
 * 格式化字节数据
 * 
 * @param bytes 字节数
 * @param options 格式化选项
 * @returns 格式化后的字符串
 */
export const formatBytes = (bytes: number, options: ByteFormatOptions = {}): string => {
  const { binary = true, precision = 2, unit = '' } = options
  
  if (bytes === 0) return `0 B${unit}`
  
  const k = binary ? 1024 : 1000
  const sizes = binary 
    ? ['B', 'KiB', 'MiB', 'GiB', 'TiB', 'PiB']
    : ['B', 'KB', 'MB', 'GB', 'TB', 'PB']
  
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  const value = bytes / Math.pow(k, i)
  
  return `${value.toFixed(precision)} ${sizes[i]}${unit}`
}

/**
 * 格式化百分比
 * 
 * @param value 数值
 * @param precision 小数位数
 * @returns 格式化后的百分比字符串
 */
export const formatPercentage = (value: number, precision: number = 2): string => {
  return `${value.toFixed(precision)}%`
}

/**
 * 格式化时间间隔
 * 
 * @param milliseconds 毫秒数
 * @returns 格式化后的时间字符串
 */
export const formatDuration = (milliseconds: number): string => {
  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  
  if (days > 0) {
    return `${days}天 ${hours % 24}小时`
  }
  if (hours > 0) {
    return `${hours}小时 ${minutes % 60}分钟`
  }
  if (minutes > 0) {
    return `${minutes}分钟 ${seconds % 60}秒`
  }
  return `${seconds}秒`
}

/**
 * 格式化时间戳
 * 
 * @param timestamp 时间戳字符串或Date对象
 * @param options 格式化选项
 * @returns 格式化后的时间字符串
 */
export const formatTime = (
  timestamp: string | Date, 
  options: TimeFormatOptions = {}
): string => {
  const { format = 'YYYY-MM-DD HH:mm:ss', locale = 'zh-CN', relative = false } = options
  
  const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp
  
  if (relative) {
    return formatRelativeTime(date)
  }
  
  // 简单的日期格式化实现
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 格式化相对时间
 * 
 * @param date 日期对象
 * @returns 相对时间字符串
 */
export const formatRelativeTime = (date: Date): string => {
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)
  
  if (diffInSeconds < 60) {
    return `${diffInSeconds}秒前`
  }
  
  const diffInMinutes = Math.floor(diffInSeconds / 60)
  if (diffInMinutes < 60) {
    return `${diffInMinutes}分钟前`
  }
  
  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) {
    return `${diffInHours}小时前`
  }
  
  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 30) {
    return `${diffInDays}天前`
  }
  
  return formatTime(date)
}

// ==================== 数据计算工具 ====================

/**
 * 计算使用率
 * 
 * @param used 已使用量
 * @param total 总量
 * @returns 使用率百分比
 */
export const calculateUsage = (used: number, total: number): number => {
  if (total === 0) return 0
  return (used / total) * 100
}

/**
 * 计算平均值
 * 
 * @param values 数值数组
 * @returns 平均值
 */
export const calculateAverage = (values: number[]): number => {
  if (values.length === 0) return 0
  return values.reduce((sum, value) => sum + value, 0) / values.length
}

/**
 * 计算最大值
 * 
 * @param values 数值数组  
 * @returns 最大值
 */
export const calculateMax = (values: number[]): number => {
  return values.length > 0 ? Math.max(...values) : 0
}

/**
 * 计算最小值
 * 
 * @param values 数值数组
 * @returns 最小值  
 */
export const calculateMin = (values: number[]): number => {
  return values.length > 0 ? Math.min(...values) : 0
}

/**
 * 计算变化率
 * 
 * @param current 当前值
 * @param previous 之前值
 * @returns 变化率百分比
 */
export const calculateChangeRate = (current: number, previous: number): number => {
  if (previous === 0) return current > 0 ? 100 : 0
  return ((current - previous) / previous) * 100
}

// ==================== 状态判断工具 ====================

/**
 * 获取CPU使用率状态
 * 
 * @param usage CPU使用率
 * @returns 状态类型
 */
export const getCpuStatus = (usage: number): 'normal' | 'warning' | 'danger' => {
  if (usage < 70) return 'normal'
  if (usage < 90) return 'warning'
  return 'danger'
}

/**
 * 获取内存使用率状态
 * 
 * @param usage 内存使用率
 * @returns 状态类型
 */
export const getMemoryStatus = (usage: number): 'normal' | 'warning' | 'danger' => {
  if (usage < 80) return 'normal'
  if (usage < 95) return 'warning'
  return 'danger'
}

/**
 * 获取磁盘使用率状态
 * 
 * @param usage 磁盘使用率
 * @returns 状态类型
 */
export const getDiskStatus = (usage: number): 'normal' | 'warning' | 'danger' => {
  if (usage < 80) return 'normal'
  if (usage < 90) return 'warning'
  return 'danger'
}

/**
 * 获取JVM堆使用率状态
 * 
 * @param usage JVM堆使用率
 * @returns 状态类型
 */
export const getJvmStatus = (usage: number): 'normal' | 'warning' | 'danger' => {
  if (usage < 70) return 'normal'
  if (usage < 85) return 'warning'
  return 'danger'
}

/**
 * 获取状态对应的颜色
 * 
 * @param status 状态
 * @returns 颜色值
 */
export const getStatusColor = (status: 'normal' | 'warning' | 'danger'): string => {
  const colors = {
    normal: '#67C23A',   // 绿色
    warning: '#E6A23C',  // 橙色
    danger: '#F56C6C'    // 红色
  }
  return colors[status]
}

/**
 * 获取状态对应的Element Plus类型
 * 
 * @param status 状态
 * @returns Element Plus类型
 */
export const getStatusType = (status: 'normal' | 'warning' | 'danger'): 'success' | 'warning' | 'danger' => {
  const types = {
    normal: 'success' as const,
    warning: 'warning' as const,
    danger: 'danger' as const
  }
  return types[status]
}

// ==================== 图表数据处理工具 ====================

/**
 * 处理时间序列数据
 * 
 * @param data 原始数据数组
 * @param timeField 时间字段名
 * @param valueField 数值字段名
 * @returns 处理后的图表数据
 */
export const processTimeSeriesData = (
  data: any[],
  timeField: string,
  valueField: string
): Array<[string, number]> => {
  return data.map(item => [
    formatTime(item[timeField], { format: 'MM-DD HH:mm' }),
    item[valueField]
  ])
}

/**
 * 生成图表配置
 * 
 * @param title 图表标题
 * @param data 数据系列
 * @param unit 单位
 * @returns ECharts配置对象
 */
export const generateChartOption = (
  title: string,
  data: Array<[string, number]>,
  unit: string = '%'
) => {
  return {
    title: {
      text: title,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const point = params[0]
        return `${point.name}<br/>${point.seriesName}: ${point.value[1]}${unit}`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data.map(item => item[0])
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: `{value}${unit}`
      }
    },
    series: [
      {
        name: title,
        type: 'line',
        smooth: true,
        data: data.map(item => item[1]),
        lineStyle: {
          width: 2
        },
        areaStyle: {
          opacity: 0.3
        }
      }
    ]
  }
}

// ==================== 数据验证工具 ====================

/**
 * 验证监控数据的有效性
 * 
 * @param data 监控数据
 * @returns 是否有效
 */
export const validateMonitorData = (data: any): boolean => {
  if (!data || typeof data !== 'object') return false
  
  const requiredFields = ['cpu', 'memory', 'disk', 'jvm', 'timestamp']
  return requiredFields.every(field => data[field] !== undefined)
}

/**
 * 验证数值范围
 * 
 * @param value 数值
 * @param min 最小值
 * @param max 最大值
 * @returns 是否在有效范围内
 */
export const validateRange = (value: number, min: number = 0, max: number = 100): boolean => {
  return value >= min && value <= max
}

// ==================== 缓存工具 ====================

/**
 * 简单的内存缓存实现
 */
class SimpleCache {
  private cache = new Map<string, { data: any; expiry: number }>()
  
  set(key: string, data: any, ttl: number = 5000): void {
    this.cache.set(key, {
      data,
      expiry: Date.now() + ttl
    })
  }
  
  get(key: string): any | null {
    const item = this.cache.get(key)
    if (!item) return null
    
    if (Date.now() > item.expiry) {
      this.cache.delete(key)
      return null
    }
    
    return item.data
  }
  
  clear(): void {
    this.cache.clear()
  }
}

export const monitorCache = new SimpleCache()

// ==================== 防抖节流工具 ====================

/**
 * 防抖函数
 * 
 * @param fn 要防抖的函数
 * @param delay 延迟时间
 * @returns 防抖后的函数
 */
export const debounce = <T extends (...args: any[]) => any>(
  fn: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: number | null = null
  
  return (...args: Parameters<T>) => {
    if (timeoutId) clearTimeout(timeoutId)
    timeoutId = window.setTimeout(() => fn(...args), delay)
  }
}

/**
 * 节流函数
 * 
 * @param fn 要节流的函数
 * @param interval 时间间隔
 * @returns 节流后的函数
 */
export const throttle = <T extends (...args: any[]) => any>(
  fn: T,
  interval: number
): ((...args: Parameters<T>) => void) => {
  let lastTime = 0
  
  return (...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastTime >= interval) {
      lastTime = now
      fn(...args)
    }
  }
}