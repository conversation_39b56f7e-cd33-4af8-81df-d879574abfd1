package com.rickpan.controller;

import com.rickpan.dto.SysMonitorDataDTO;
import com.rickpan.dto.SysMonitorQueryDTO;
import com.rickpan.dto.SysMonitorAlertDTO;
import com.rickpan.entity.SysMonitorHistory;
import com.rickpan.entity.SysMonitorAlertConfig;
import com.rickpan.entity.SysMonitorAlertLog;
import com.rickpan.service.SysMonitorCollectorService;
import com.rickpan.service.SysMonitorService;
import com.rickpan.repository.SysMonitorAlertConfigRepository;
import com.rickpan.repository.SysMonitorAlertLogRepository;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Positive;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 系统监控API控制器
 * 提供系统监控数据的查询、统计和控制功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/sys-monitor")
@RequiredArgsConstructor
@Slf4j
@Validated
@Tag(name = "系统监控", description = "系统监控数据API - 提供实时监控、历史查询、统计分析等功能")
public class SysMonitorController {

    private final SysMonitorService sysMonitorService;
    private final SysMonitorCollectorService collectorService;
    private final SysMonitorAlertConfigRepository alertConfigRepository;
    private final SysMonitorAlertLogRepository alertLogRepository;

    // ==================== 实时数据API ====================

    /**
     * 获取实时监控数据
     * 
     * @return 当前系统实时监控数据
     */
    @GetMapping("/realtime")
    @Operation(summary = "获取实时监控数据", 
               description = "获取当前系统的实时监控数据，包括CPU、内存、磁盘、JVM等信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功",
                    content = @Content(schema = @Schema(implementation = SysMonitorDataDTO.class))),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public ResponseEntity<Map<String, Object>> getRealtimeData() {
        try {
            SysMonitorDataDTO data = sysMonitorService.getRealtimeData();
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", data);
            result.put("message", "获取实时监控数据成功");
            result.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("获取实时监控数据失败", e);
            
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "获取实时监控数据失败: " + e.getMessage());
            errorResult.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }

    // ==================== 历史数据API ====================

    /**
     * 查询历史监控数据
     * 
     * @param queryDTO 查询参数
     * @return 历史监控数据分页结果
     */
    @PostMapping("/history")
    @Operation(summary = "查询历史监控数据", 
               description = "根据时间范围和分页参数查询历史监控数据")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public ResponseEntity<Map<String, Object>> getHistoryData(
            @Valid @RequestBody SysMonitorQueryDTO queryDTO) {
        log.info("查询历史监控数据请求, 参数: {}", queryDTO);
        
        try {
            Page<SysMonitorHistory> historyPage = sysMonitorService.getHistoryData(queryDTO);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", historyPage.getContent());
            result.put("total", historyPage.getTotalElements());
            result.put("totalPages", historyPage.getTotalPages());
            result.put("currentPage", historyPage.getNumber() + 1);
            result.put("size", historyPage.getSize());
            result.put("message", "查询历史监控数据成功");
            result.put("timestamp", LocalDateTime.now());
            
            log.info("历史监控数据查询成功, 共{}条记录", historyPage.getTotalElements());
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("查询历史监控数据失败", e);
            
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "查询历史监控数据失败: " + e.getMessage());
            errorResult.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }

    /**
     * 查询历史监控数据 - GET方式（简化查询）
     * 
     * @param timeRange 时间范围 (1h, 6h, 24h, 7d, 30d)
     * @param page 页码 (从1开始)
     * @param size 每页大小
     * @param sort 排序方式 (asc, desc)
     * @return 历史监控数据分页结果
     */
    @GetMapping("/history")
    @Operation(summary = "查询历史监控数据(简化)", 
               description = "通过URL参数查询历史监控数据，适用于简单查询场景")
    public ResponseEntity<Map<String, Object>> getHistoryDataSimple(
            @Parameter(description = "时间范围", example = "1h")
            @RequestParam(defaultValue = "1h") String timeRange,
            
            @Parameter(description = "页码(从1开始)", example = "1")
            @RequestParam(defaultValue = "1") @Positive Integer page,
            
            @Parameter(description = "每页大小", example = "20")
            @RequestParam(defaultValue = "20") @Positive Integer size,
            
            @Parameter(description = "排序方式", example = "desc")
            @RequestParam(defaultValue = "desc") String sort) {
        
        log.info("简化查询历史监控数据请求, timeRange: {}, page: {}, size: {}", timeRange, page, size);
        
        try {
            SysMonitorQueryDTO queryDTO = new SysMonitorQueryDTO();
            queryDTO.setTimeRange(timeRange);
            queryDTO.setPage(page);
            queryDTO.setSize(size);
            queryDTO.setSort(sort);
            
            return getHistoryData(queryDTO);
            
        } catch (Exception e) {
            log.error("简化查询历史监控数据失败", e);
            
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "查询历史监控数据失败: " + e.getMessage());
            errorResult.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }

    // ==================== 统计数据API ====================

    /**
     * 获取监控数据统计信息
     * 
     * @param timeRange 时间范围
     * @return 统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取监控统计信息", 
               description = "获取指定时间范围内的监控数据统计信息，包括平均值、最大值、最小值等")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public ResponseEntity<Map<String, Object>> getStatistics(
            @Parameter(description = "时间范围", example = "24h")
            @RequestParam(defaultValue = "24h") @NotBlank String timeRange) {
        
        log.info("获取监控统计信息请求, timeRange: {}", timeRange);
        
        try {
            Object statistics = sysMonitorService.getMonitorStatistics(timeRange);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", statistics);
            result.put("timeRange", timeRange);
            result.put("message", "获取监控统计信息成功");
            result.put("timestamp", LocalDateTime.now());
            
            log.info("监控统计信息获取成功");
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("获取监控统计信息失败", e);
            
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "获取监控统计信息失败: " + e.getMessage());
            errorResult.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }

    // ==================== 采集控制API ====================

    /**
     * 获取采集服务状态
     * 
     * @return 采集服务状态信息
     */
    @GetMapping("/collector/status")
    @Operation(summary = "获取采集服务状态", 
               description = "获取数据采集服务的当前状态，包括运行状态、统计信息等")
    public ResponseEntity<Map<String, Object>> getCollectorStatus() {
        log.info("获取采集服务状态请求");
        
        try {
            SysMonitorCollectorService.CollectionStatistics statistics = collectorService.getStatistics();
            
            Map<String, Object> statusData = new HashMap<>();
            statusData.put("enabled", statistics.isEnabled());
            statusData.put("collecting", statistics.isCollecting());
            statusData.put("totalCollections", statistics.getTotalCollections());
            statusData.put("successCollections", statistics.getSuccessCollections());
            statusData.put("errorCollections", statistics.getErrorCollections());
            statusData.put("successRate", statistics.getSuccessRate());
            statusData.put("lastCollectionTime", statistics.getLastCollectionTime());
            statusData.put("lastSuccessTime", statistics.getLastSuccessTime());
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", statusData);
            result.put("message", "获取采集服务状态成功");
            result.put("timestamp", LocalDateTime.now());
            
            log.info("采集服务状态获取成功");
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("获取采集服务状态失败", e);
            
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "获取采集服务状态失败: " + e.getMessage());
            errorResult.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }

    /**
     * 启用数据采集
     * 
     * @return 操作结果
     */
    @PostMapping("/collector/enable")
    @Operation(summary = "启用数据采集", 
               description = "启用系统监控数据的自动采集功能")
    public ResponseEntity<Map<String, Object>> enableCollection() {
        log.info("启用数据采集请求");
        
        try {
            collectorService.enableCollection();
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "数据采集已启用");
            result.put("timestamp", LocalDateTime.now());
            
            log.info("数据采集启用成功");
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("启用数据采集失败", e);
            
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "启用数据采集失败: " + e.getMessage());
            errorResult.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }

    /**
     * 禁用数据采集
     * 
     * @return 操作结果
     */
    @PostMapping("/collector/disable")
    @Operation(summary = "禁用数据采集", 
               description = "禁用系统监控数据的自动采集功能")
    public ResponseEntity<Map<String, Object>> disableCollection() {
        log.info("禁用数据采集请求");
        
        try {
            collectorService.disableCollection();
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "数据采集已禁用");
            result.put("timestamp", LocalDateTime.now());
            
            log.info("数据采集禁用成功");
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("禁用数据采集失败", e);
            
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "禁用数据采集失败: " + e.getMessage());
            errorResult.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }

    /**
     * 手动触发数据采集
     * 
     * @return 采集结果
     */
    @PostMapping("/collector/manual")
    @Operation(summary = "手动触发数据采集", 
               description = "立即执行一次数据采集操作，不影响定时采集")
    public ResponseEntity<Map<String, Object>> manualCollection() {
        log.info("手动触发数据采集请求");
        
        try {
            SysMonitorDataDTO data = collectorService.manualCollect();
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", data);
            result.put("message", "手动数据采集完成");
            result.put("timestamp", LocalDateTime.now());
            
            log.info("手动数据采集成功");
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("手动数据采集失败", e);
            
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "手动数据采集失败: " + e.getMessage());
            errorResult.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }

    /**
     * 重置采集统计
     * 
     * @return 操作结果
     */
    @PostMapping("/collector/reset-stats")
    @Operation(summary = "重置采集统计", 
               description = "重置数据采集服务的统计信息，清空历史统计数据")
    public ResponseEntity<Map<String, Object>> resetStatistics() {
        log.info("重置采集统计请求");
        
        try {
            collectorService.resetStatistics();
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "采集统计已重置");
            result.put("timestamp", LocalDateTime.now());
            
            log.info("采集统计重置成功");
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("重置采集统计失败", e);
            
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "重置采集统计失败: " + e.getMessage());
            errorResult.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }

    // ==================== 健康检查API ====================

    /**
     * 系统监控健康检查
     * 
     * @return 健康状态
     */
    @GetMapping("/health")
    @Operation(summary = "系统监控健康检查", 
               description = "检查系统监控模块的健康状态，包括服务可用性、数据采集状态等")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        log.debug("系统监控健康检查请求");
        
        try {
            // 获取采集服务状态
            SysMonitorCollectorService.CollectionStatistics stats = collectorService.getStatistics();
            
            // 尝试获取实时数据（验证监控功能）
            SysMonitorDataDTO realtimeData = sysMonitorService.getRealtimeData();
            
            Map<String, Object> healthData = new HashMap<>();
            healthData.put("status", "UP");
            healthData.put("collectorEnabled", stats.isEnabled());
            healthData.put("dataCollectionWorking", realtimeData != null);
            healthData.put("lastSuccessTime", stats.getLastSuccessTime());
            healthData.put("successRate", stats.getSuccessRate());
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", healthData);
            result.put("message", "系统监控健康");
            result.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("系统监控健康检查失败", e);
            
            Map<String, Object> healthData = new HashMap<>();
            healthData.put("status", "DOWN");
            healthData.put("error", e.getMessage());
            
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("data", healthData);
            errorResult.put("message", "系统监控异常");
            errorResult.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.ok(errorResult); // 健康检查返回200，但状态为DOWN
        }
    }

    // ==================== 告警管理API ====================

    /**
     * 获取告警配置列表
     * 
     * @return 告警配置列表
     */
    @GetMapping("/alert/configs")
    @Operation(summary = "获取告警配置列表", 
               description = "获取所有系统监控告警配置信息")
    public ResponseEntity<Map<String, Object>> getAlertConfigs() {
        log.info("获取告警配置列表请求");
        
        try {
            var configs = alertConfigRepository.findAll();
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", configs);
            result.put("message", "获取告警配置成功");
            result.put("timestamp", LocalDateTime.now());
            
            log.info("告警配置列表获取成功，共{}条", configs.size());
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("获取告警配置列表失败", e);
            
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "获取告警配置失败: " + e.getMessage());
            errorResult.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }

    /**
     * 更新告警配置
     * 
     * @param id 配置ID
     * @param config 配置信息
     * @return 操作结果
     */
    @PutMapping("/alert/configs/{id}")
    @Operation(summary = "更新告警配置", 
               description = "更新指定的告警配置信息")
    public ResponseEntity<Map<String, Object>> updateAlertConfig(
            @PathVariable Long id,
            @RequestBody SysMonitorAlertConfig config) {
        log.info("更新告警配置请求, id: {}", id);
        
        try {
            var existingConfig = alertConfigRepository.findById(id);
            if (existingConfig.isEmpty()) {
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("success", false);
                errorResult.put("message", "告警配置不存在");
                errorResult.put("timestamp", LocalDateTime.now());
                return ResponseEntity.badRequest().body(errorResult);
            }
            
            config.setId(id);
            var savedConfig = alertConfigRepository.save(config);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", savedConfig);
            result.put("message", "告警配置更新成功");
            result.put("timestamp", LocalDateTime.now());
            
            log.info("告警配置更新成功, id: {}", id);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("更新告警配置失败", e);
            
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "更新告警配置失败: " + e.getMessage());
            errorResult.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }

    /**
     * 获取告警日志
     * 
     * @param page 页码
     * @param size 每页大小
     * @param level 告警级别
     * @param status 告警状态
     * @return 告警日志分页结果
     */
    @GetMapping("/alert/logs")
    @Operation(summary = "获取告警日志", 
               description = "分页查询系统监控告警日志")
    public ResponseEntity<Map<String, Object>> getAlertLogs(
            @RequestParam(defaultValue = "1") @Positive Integer page,
            @RequestParam(defaultValue = "20") @Positive Integer size,
            @RequestParam(required = false) String level,
            @RequestParam(required = false) String status) {
        
        log.info("获取告警日志请求, page: {}, size: {}, level: {}, status: {}", page, size, level, status);
        
        try {
            var pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "alertTime"));
            Page<SysMonitorAlertLog> logs;
            
            log.info("筛选参数处理: level='{}', status='{}'", level, status);
            
            // 根据筛选条件查询
            if (level != null && !level.trim().isEmpty() && status != null && !status.trim().isEmpty()) {
                // 同时按级别和状态筛选
                log.info("执行组合筛选查询: level={}, status={}", level, status);
                logs = alertLogRepository.findByAlertLevelAndAlertStatusOrderByAlertTimeDesc(level, status, pageable);
            } else if (level != null && !level.trim().isEmpty()) {
                // 只按级别筛选
                log.info("执行级别筛选查询: level={}", level);
                logs = alertLogRepository.findByAlertLevelOrderByAlertTimeDesc(level, pageable);
            } else if (status != null && !status.trim().isEmpty()) {
                // 只按状态筛选
                log.info("执行状态筛选查询: status={}", status);
                logs = alertLogRepository.findByAlertStatusOrderByAlertTimeDesc(status, pageable);
            } else {
                // 无筛选条件，查询所有
                log.info("执行无筛选查询");
                logs = alertLogRepository.findAll(pageable);
            }
            
            log.info("查询结果: 总数={}, 当前页数据量={}", logs.getTotalElements(), logs.getContent().size());
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", logs.getContent());
            result.put("total", logs.getTotalElements());
            result.put("totalPages", logs.getTotalPages());
            result.put("currentPage", logs.getNumber() + 1);
            result.put("size", logs.getSize());
            result.put("message", "获取告警日志成功");
            result.put("timestamp", LocalDateTime.now());
            
            log.info("告警日志获取成功，共{}条", logs.getTotalElements());
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("获取告警日志失败", e);
            
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "获取告警日志失败: " + e.getMessage());
            errorResult.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }

    /**
     * 解决告警
     * 
     * @param id 告警日志ID
     * @param resolveNote 解决备注
     * @return 操作结果
     */
    @PostMapping("/alert/logs/{id}/resolve")
    @Operation(summary = "解决告警", 
               description = "标记指定告警为已解决状态")
    public ResponseEntity<Map<String, Object>> resolveAlert(
            @PathVariable Long id,
            @RequestBody Map<String, String> request) {
        log.info("解决告警请求, id: {}", id);
        
        try {
            var alertLog = alertLogRepository.findById(id);
            if (alertLog.isEmpty()) {
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("success", false);
                errorResult.put("message", "告警记录不存在");
                errorResult.put("timestamp", LocalDateTime.now());
                return ResponseEntity.badRequest().body(errorResult);
            }
            
            var alertLogEntity = alertLog.get();
            alertLogEntity.setAlertStatus("RESOLVED");
            alertLogEntity.setResolvedTime(java.time.LocalDateTime.now());
            alertLogEntity.setResolveNote(request.get("resolveNote"));
            alertLogEntity.setResolvedBy("system"); // 这里可以从登录用户获取
            
            alertLogRepository.save(alertLogEntity);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "告警已解决");
            result.put("timestamp", LocalDateTime.now());
            
            log.info("告警解决成功, id: {}", id);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("解决告警失败", e);
            
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "解决告警失败: " + e.getMessage());
            errorResult.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }

    /**
     * 获取告警统计信息
     * 
     * @param timeRange 时间范围
     * @return 告警统计信息
     */
    @GetMapping("/alert/statistics")
    @Operation(summary = "获取告警统计信息", 
               description = "获取指定时间范围内的告警统计信息")
    public ResponseEntity<Map<String, Object>> getAlertStatistics(
            @RequestParam(defaultValue = "24h") String timeRange) {
        log.info("获取告警统计信息请求, timeRange: {}", timeRange);
        
        try {
            // 计算时间范围
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = switch (timeRange) {
                case "1h" -> endTime.minusHours(1);
                case "6h" -> endTime.minusHours(6);
                case "24h" -> endTime.minusHours(24);
                case "7d" -> endTime.minusDays(7);
                case "30d" -> endTime.minusDays(30);
                default -> endTime.minusHours(24);
            };
            
            var totalAlerts = alertLogRepository.countByAlertTimeBetween(startTime, endTime);
            var activeAlerts = alertLogRepository.countByAlertStatusAndAlertTimeBetween("ACTIVE", startTime, endTime);
            var resolvedAlerts = alertLogRepository.countByAlertStatusAndAlertTimeBetween("RESOLVED", startTime, endTime);
            
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalAlerts", totalAlerts);
            statistics.put("activeAlerts", activeAlerts);
            statistics.put("resolvedAlerts", resolvedAlerts);
            statistics.put("resolveRate", totalAlerts > 0 ? (double) resolvedAlerts / totalAlerts * 100 : 0);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", statistics);
            result.put("timeRange", timeRange);
            result.put("message", "获取告警统计信息成功");
            result.put("timestamp", LocalDateTime.now());
            
            log.info("告警统计信息获取成功");
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("获取告警统计信息失败", e);
            
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "获取告警统计信息失败: " + e.getMessage());
            errorResult.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }
}