{"version": 3, "sources": ["../../@codemirror/lang-vue/dist/index.js"], "sourcesContent": ["import { LRLanguage, LanguageSupport } from '@codemirror/language';\nimport { html } from '@codemirror/lang-html';\nimport { javascriptLanguage } from '@codemirror/lang-javascript';\nimport { styleTags, tags } from '@lezer/highlight';\nimport { parseMixed } from '@lezer/common';\nimport { LRParser, LocalTokenGroup } from '@lezer/lr';\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst parser = /*@__PURE__*/LRParser.deserialize({\n  version: 14,\n  states: \"%pOVOWOOObQPOOOpOSO'#C_OOOO'#Cp'#CpQVOWOOQxQPOOO!TQQOOQ!YQPOOOOOO,58y,58yO!_OSO,58yOOOO-E6n-E6nO!dQQO'#CqQ{QPOOO!iQPOOQ{QPOOO!qQPOOOOOO1G.e1G.eOOQO,59],59]OOQO-E6o-E6oO!yOpO'#CiO#RO`O'#CiQOQPOOO#ZO#tO'#CmO#fO!bO'#CmOOQO,59T,59TO#qOpO,59TO#vO`O,59TOOOO'#Cr'#CrO#{O#tO,59XOOQO,59X,59XOOOO'#Cs'#CsO$WO!bO,59XOOQO1G.o1G.oOOOO-E6p-E6pOOQO1G.s1G.sOOOO-E6q-E6q\",\n  stateData: \"$g~OjOS~OQROUROkQO~OWTOXUOZUO`VO~OSXOTWO~OXUO[]OlZO~OY^O~O[_O~OT`O~OYaO~OmcOodO~OmfOogO~O^iOnhO~O_jOphO~ObkOqkOrmO~OcnOsnOtmO~OnpO~OppO~ObkOqkOrrO~OcnOsnOtrO~OWX`~\",\n  goto: \"!^hPPPiPPPPPPPPPmPPPpPPsy!Q!WTROSRe]Re_QSORYSS[T^Rb[QlfRqlQogRso\",\n  nodeNames: \"⚠ Content Text Interpolation InterpolationContent }} Entity Attribute VueAttributeName : Identifier @ Is ScriptAttributeValue AttributeScript AttributeScript AttributeName AttributeValue Entity Entity\",\n  maxTerm: 36,\n  nodeProps: [\n    [\"isolate\", -3,3,13,17,\"\"]\n  ],\n  skippedNodes: [0],\n  repeatNodeCount: 4,\n  tokenData: \"'y~RdXY!aYZ!a]^!apq!ars!rwx!w}!O!|!O!P#t!Q![#y![!]$s!_!`%g!b!c%l!c!}#y#R#S#y#T#j#y#j#k%q#k#o#y%W;'S#y;'S;:j$m<%lO#y~!fSj~XY!aYZ!a]^!apq!a~!wOm~~!|Oo~!b#RX`!b}!O!|!Q![!|![!]!|!c!}!|#R#S!|#T#o!|%W;'S!|;'S;:j#n<%lO!|!b#qP;=`<%l!|~#yOl~%W$QXY#t`!b}!O!|!Q![#y![!]!|!c!}#y#R#S#y#T#o#y%W;'S#y;'S;:j$m<%lO#y%W$pP;=`<%l#y~$zXX~`!b}!O!|!Q![!|![!]!|!c!}!|#R#S!|#T#o!|%W;'S!|;'S;:j#n<%lO!|~%lO[~~%qOZ~%W%xXY#t`!b}!O&e!Q![#y![!]!|!c!}#y#R#S#y#T#o#y%W;'S#y;'S;:j$m<%lO#y!b&jX`!b}!O!|!Q![!|![!]!|!c!}'V#R#S!|#T#o'V%W;'S!|;'S;:j#n<%lO!|!b'^XW!b`!b}!O!|!Q![!|![!]!|!c!}'V#R#S!|#T#o'V%W;'S!|;'S;:j#n<%lO!|\",\n  tokenizers: [6, 7, /*@__PURE__*/new LocalTokenGroup(\"b~RP#q#rU~XP#q#r[~aOT~~\", 17, 4), /*@__PURE__*/new LocalTokenGroup(\"!k~RQvwX#o#p!_~^TU~Opmq!]m!^;'Sm;'S;=`!X<%lOm~pUOpmq!]m!]!^!S!^;'Sm;'S;=`!X<%lOm~!XOU~~![P;=`<%lm~!bP#o#p!e~!jOk~~\", 72, 2), /*@__PURE__*/new LocalTokenGroup(\"[~RPwxU~ZOp~~\", 11, 15), /*@__PURE__*/new LocalTokenGroup(\"[~RPrsU~ZOn~~\", 11, 14), /*@__PURE__*/new LocalTokenGroup(\"!e~RQvwXwx!_~^Tc~Opmq!]m!^;'Sm;'S;=`!X<%lOm~pUOpmq!]m!]!^!S!^;'Sm;'S;=`!X<%lOm~!XOc~~![P;=`<%lm~!dOt~~\", 66, 35), /*@__PURE__*/new LocalTokenGroup(\"!e~RQrsXvw^~^Or~~cTb~Oprq!]r!^;'Sr;'S;=`!^<%lOr~uUOprq!]r!]!^!X!^;'Sr;'S;=`!^<%lOr~!^Ob~~!aP;=`<%lr~\", 66, 33)],\n  topRules: {\"Content\":[0,1],\"Attribute\":[1,7]},\n  tokenPrec: 157\n});\n\nconst exprParser = /*@__PURE__*/javascriptLanguage.parser.configure({\n    top: \"SingleExpression\"\n});\nconst baseParser = /*@__PURE__*/parser.configure({\n    props: [\n        /*@__PURE__*/styleTags({\n            Text: tags.content,\n            Is: tags.definitionOperator,\n            AttributeName: tags.attributeName,\n            VueAttributeName: tags.keyword,\n            Identifier: tags.variableName,\n            \"AttributeValue ScriptAttributeValue\": tags.attributeValue,\n            Entity: tags.character,\n            \"{{ }}\": tags.brace,\n            \"@ :\": tags.punctuation\n        })\n    ]\n});\nconst exprMixed = { parser: exprParser };\nconst textParser = /*@__PURE__*/baseParser.configure({\n    wrap: /*@__PURE__*/parseMixed((node, input) => node.name == \"InterpolationContent\" ? exprMixed : null),\n});\nconst attrParser = /*@__PURE__*/baseParser.configure({\n    wrap: /*@__PURE__*/parseMixed((node, input) => node.name == \"AttributeScript\" ? exprMixed : null),\n    top: \"Attribute\"\n});\nconst textMixed = { parser: textParser }, attrMixed = { parser: attrParser };\nconst baseHTML = /*@__PURE__*/html();\nfunction makeVue(base) {\n    return base.configure({\n        dialect: \"selfClosing\",\n        wrap: parseMixed(mixVue)\n    }, \"vue\");\n}\n/**\nA language provider for Vue templates.\n*/\nconst vueLanguage = /*@__PURE__*/makeVue(baseHTML.language);\nfunction mixVue(node, input) {\n    switch (node.name) {\n        case \"Attribute\":\n            return /^(@|:|v-)/.test(input.read(node.from, node.from + 2)) ? attrMixed : null;\n        case \"Text\":\n            return textMixed;\n    }\n    return null;\n}\n/**\nVue template support.\n*/\nfunction vue(config = {}) {\n    let base = baseHTML;\n    if (config.base) {\n        if (config.base.language.name != \"html\" || !(config.base.language instanceof LRLanguage))\n            throw new RangeError(\"The base option must be the result of calling html(...)\");\n        base = config.base;\n    }\n    return new LanguageSupport(base.language == baseHTML.language ? vueLanguage : makeVue(base.language), [\n        base.support,\n        base.language.data.of({ closeBrackets: { brackets: [\"{\", '\"'] } })\n    ]);\n}\n\nexport { vue, vueLanguage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAQA,IAAM,SAAsB,SAAS,YAAY;AAAA,EAC/C,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,MAAM;AAAA,EACN,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW;AAAA,IACT,CAAC,WAAW,IAAG,GAAE,IAAG,IAAG,EAAE;AAAA,EAC3B;AAAA,EACA,cAAc,CAAC,CAAC;AAAA,EAChB,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,YAAY,CAAC,GAAG,GAAgB,IAAI,gBAAgB,2BAA2B,IAAI,CAAC,GAAgB,IAAI,gBAAgB,sHAAsH,IAAI,CAAC,GAAgB,IAAI,gBAAgB,iBAAiB,IAAI,EAAE,GAAgB,IAAI,gBAAgB,iBAAiB,IAAI,EAAE,GAAgB,IAAI,gBAAgB,0GAA0G,IAAI,EAAE,GAAgB,IAAI,gBAAgB,wGAAwG,IAAI,EAAE,CAAC;AAAA,EAChpB,UAAU,EAAC,WAAU,CAAC,GAAE,CAAC,GAAE,aAAY,CAAC,GAAE,CAAC,EAAC;AAAA,EAC5C,WAAW;AACb,CAAC;AAED,IAAM,aAA0B,mBAAmB,OAAO,UAAU;AAAA,EAChE,KAAK;AACT,CAAC;AACD,IAAM,aAA0B,OAAO,UAAU;AAAA,EAC7C,OAAO;AAAA,IACU,UAAU;AAAA,MACnB,MAAM,KAAK;AAAA,MACX,IAAI,KAAK;AAAA,MACT,eAAe,KAAK;AAAA,MACpB,kBAAkB,KAAK;AAAA,MACvB,YAAY,KAAK;AAAA,MACjB,uCAAuC,KAAK;AAAA,MAC5C,QAAQ,KAAK;AAAA,MACb,SAAS,KAAK;AAAA,MACd,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AACJ,CAAC;AACD,IAAM,YAAY,EAAE,QAAQ,WAAW;AACvC,IAAM,aAA0B,WAAW,UAAU;AAAA,EACjD,MAAmB,WAAW,CAAC,MAAM,UAAU,KAAK,QAAQ,yBAAyB,YAAY,IAAI;AACzG,CAAC;AACD,IAAM,aAA0B,WAAW,UAAU;AAAA,EACjD,MAAmB,WAAW,CAAC,MAAM,UAAU,KAAK,QAAQ,oBAAoB,YAAY,IAAI;AAAA,EAChG,KAAK;AACT,CAAC;AACD,IAAM,YAAY,EAAE,QAAQ,WAAW;AAAvC,IAA0C,YAAY,EAAE,QAAQ,WAAW;AAC3E,IAAM,WAAwB,KAAK;AACnC,SAAS,QAAQ,MAAM;AACnB,SAAO,KAAK,UAAU;AAAA,IAClB,SAAS;AAAA,IACT,MAAM,WAAW,MAAM;AAAA,EAC3B,GAAG,KAAK;AACZ;AAIA,IAAM,cAA2B,QAAQ,SAAS,QAAQ;AAC1D,SAAS,OAAO,MAAM,OAAO;AACzB,UAAQ,KAAK,MAAM;AAAA,IACf,KAAK;AACD,aAAO,YAAY,KAAK,MAAM,KAAK,KAAK,MAAM,KAAK,OAAO,CAAC,CAAC,IAAI,YAAY;AAAA,IAChF,KAAK;AACD,aAAO;AAAA,EACf;AACA,SAAO;AACX;AAIA,SAAS,IAAI,SAAS,CAAC,GAAG;AACtB,MAAI,OAAO;AACX,MAAI,OAAO,MAAM;AACb,QAAI,OAAO,KAAK,SAAS,QAAQ,UAAU,EAAE,OAAO,KAAK,oBAAoB;AACzE,YAAM,IAAI,WAAW,yDAAyD;AAClF,WAAO,OAAO;AAAA,EAClB;AACA,SAAO,IAAI,gBAAgB,KAAK,YAAY,SAAS,WAAW,cAAc,QAAQ,KAAK,QAAQ,GAAG;AAAA,IAClG,KAAK;AAAA,IACL,KAAK,SAAS,KAAK,GAAG,EAAE,eAAe,EAAE,UAAU,CAAC,KAAK,GAAG,EAAE,EAAE,CAAC;AAAA,EACrE,CAAC;AACL;", "names": []}