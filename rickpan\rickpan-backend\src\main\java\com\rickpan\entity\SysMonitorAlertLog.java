package com.rickpan.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 系统监控告警日志实体类
 * 对应数据库表：sys_monitor_alert_log
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "sys_monitor_alert_log", indexes = {
    @Index(name = "idx_sys_monitor_alert_log_alert_time", columnList = "alert_time"),
    @Index(name = "idx_sys_monitor_alert_log_metric_type", columnList = "metric_type"),
    @Index(name = "idx_sys_monitor_alert_log_alert_level", columnList = "alert_level"),
    @Index(name = "idx_sys_monitor_alert_log_alert_status", columnList = "alert_status"),
    @Index(name = "idx_sys_monitor_alert_log_config_id", columnList = "alert_config_id")
})
@EntityListeners(AuditingEntityListener.class)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysMonitorAlertLog {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // ==================== 告警基本信息 ====================
    
    /**
     * 告警配置ID
     */
    @Column(name = "alert_config_id")
    private Long alertConfigId;

    /**
     * 告警类型(THRESHOLD_EXCEEDED/SYSTEM_ERROR等)
     */
    @Column(name = "alert_type", length = 50, nullable = false)
    private String alertType;

    /**
     * 指标类型
     */
    @Column(name = "metric_type", length = 50, nullable = false)
    private String metricType;

    /**
     * 指标名称
     */
    @Column(name = "metric_name", length = 100, nullable = false)
    private String metricName;

    // ==================== 告警级别和状态 ====================
    
    /**
     * 告警级别(WARNING/CRITICAL/EMERGENCY)
     */
    @Column(name = "alert_level", length = 20, nullable = false)
    private String alertLevel;

    /**
     * 告警状态(ACTIVE/RESOLVED/IGNORED)
     */
    @Column(name = "alert_status", length = 20)
    @Builder.Default
    private String alertStatus = "ACTIVE";

    // ==================== 告警内容 ====================
    
    /**
     * 告警标题
     */
    @Column(name = "alert_title", length = 200)
    private String alertTitle;

    /**
     * 告警消息详情
     */
    @Column(name = "alert_message", columnDefinition = "TEXT")
    private String alertMessage;

    // ==================== 指标数据 ====================
    
    /**
     * 触发告警时的指标值
     */
    @Column(name = "metric_value", precision = 12, scale = 4)
    private BigDecimal metricValue;

    /**
     * 触发的阈值
     */
    @Column(name = "threshold_value", precision = 12, scale = 4)
    private BigDecimal thresholdValue;

    /**
     * 数值单位
     */
    @Column(name = "unit", length = 20)
    private String unit;

    // ==================== 时间信息 ====================
    
    /**
     * 告警触发时间
     */
    @Column(name = "alert_time", nullable = false)
    private LocalDateTime alertTime;

    /**
     * 首次告警时间（用于告警聚合）
     */
    @Column(name = "first_alert_time")
    private LocalDateTime firstAlertTime;

    /**
     * 告警解决时间
     */
    @Column(name = "resolved_time")
    private LocalDateTime resolvedTime;

    // ==================== 告警处理信息 ====================
    
    /**
     * 解决人
     */
    @Column(name = "resolved_by", length = 100)
    private String resolvedBy;

    /**
     * 解决备注
     */
    @Column(name = "resolve_note", columnDefinition = "TEXT")
    private String resolveNote;

    // ==================== 告警统计 ====================
    
    /**
     * 告警发生次数（用于告警聚合）
     */
    @Column(name = "occurrence_count")
    @Builder.Default
    private Integer occurrenceCount = 1;

    // ==================== 时间戳 ====================
    
    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // ==================== 关联实体 ====================
    
    /**
     * 关联的告警配置
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "alert_config_id", insertable = false, updatable = false)
    private SysMonitorAlertConfig alertConfig;

    // ==================== 枚举定义 ====================
    
    /**
     * 告警类型枚举
     */
    public enum AlertType {
        THRESHOLD_EXCEEDED("THRESHOLD_EXCEEDED", "阈值超限"),
        SYSTEM_ERROR("SYSTEM_ERROR", "系统错误"),
        RESOURCE_SHORTAGE("RESOURCE_SHORTAGE", "资源不足"),
        PERFORMANCE_DEGRADATION("PERFORMANCE_DEGRADATION", "性能下降"),
        SYSTEM_MAINTENANCE("SYSTEM_MAINTENANCE", "系统维护");

        private final String code;
        private final String description;

        AlertType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() { return code; }
        public String getDescription() { return description; }
    }

    /**
     * 告警级别枚举
     */
    public enum AlertLevel {
        INFO("INFO", "信息", 1),
        WARNING("WARNING", "警告", 2),
        CRITICAL("CRITICAL", "严重", 3),
        EMERGENCY("EMERGENCY", "紧急", 4);

        private final String code;
        private final String description;
        private final Integer level;

        AlertLevel(String code, String description, Integer level) {
            this.code = code;
            this.description = description;
            this.level = level;
        }

        public String getCode() { return code; }
        public String getDescription() { return description; }
        public Integer getLevel() { return level; }
    }

    /**
     * 告警状态枚举
     */
    public enum AlertStatus {
        ACTIVE("ACTIVE", "活跃"),
        RESOLVED("RESOLVED", "已解决"),
        IGNORED("IGNORED", "已忽略"),
        SUPPRESSED("SUPPRESSED", "已抑制");

        private final String code;
        private final String description;

        AlertStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() { return code; }
        public String getDescription() { return description; }
    }
}