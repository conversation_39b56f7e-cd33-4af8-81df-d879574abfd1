<template>
  <div class="user-behavior-analysis">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon><TrendCharts /></el-icon>
            用户行为分析
          </h1>
          <p class="page-description">分析用户操作行为模式和统计数据</p>
        </div>
        <div class="header-right">
          <div class="date-shortcuts">
            <el-button 
              size="small"
              @click="setDateRange(7)"
              :type="isDateRangeActive(7) ? 'primary' : 'default'"
            >
              最近7天
            </el-button>
            <el-button 
              size="small"
              @click="setDateRange(30)"
              :type="isDateRangeActive(30) ? 'primary' : 'default'"
            >
              最近30天
            </el-button>
            <el-button 
              size="small"
              @click="setDateRange(90)"
              :type="isDateRangeActive(90) ? 'primary' : 'default'"
            >
              最近90天
            </el-button>
          </div>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateRangeChange"
            clearable
          />
          <el-button 
            type="primary" 
            @click="refreshData"
            :loading="loading"
          >
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计概览卡片 -->
    <div class="overview-section">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card shadow="hover" class="stats-card">
            <div class="stat-item">
              <div class="stat-icon total">
                <el-icon><Operation /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ basicStats.totalOperations || 0 }}</div>
                <div class="stat-label">总操作数</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card shadow="hover" class="stats-card">
            <div class="stat-item">
              <div class="stat-icon success">
                <el-icon><SuccessFilled /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ basicStats.successRate || 0 }}%</div>
                <div class="stat-label">操作成功率</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card shadow="hover" class="stats-card">
            <div class="stat-item">
              <div class="stat-icon average">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ basicStats.avgDailyOperations || 0 }}</div>
                <div class="stat-label">日均操作数</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card shadow="hover" class="stats-card">
            <div class="stat-item">
              <div class="stat-icon failed">
                <el-icon><CircleCloseFilled /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ basicStats.failedOperations || 0 }}</div>
                <div class="stat-label">失败操作数</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表分析区域 -->
    <div class="charts-section">
      <el-row :gutter="16">
        <!-- 操作趋势图 -->
        <el-col :span="12">
          <el-card shadow="never" class="chart-card">
            <template #header>
              <div class="chart-header">
                <h3>操作趋势分析</h3>
                <el-tag size="small" type="info">{{ analyzeDays }}天</el-tag>
              </div>
            </template>
            <div class="chart-container">
              <div ref="trendChartRef" class="chart" style="height: 300px;"></div>
            </div>
          </el-card>
        </el-col>

        <!-- 操作类型分布 -->
        <el-col :span="12">
          <el-card shadow="never" class="chart-card">
            <template #header>
              <div class="chart-header">
                <h3>操作类型分布</h3>
                <el-button size="small" text @click="toggleChartType">
                  {{ showPieChart ? '切换柱状图' : '切换饼图' }}
                </el-button>
              </div>
            </template>
            <div class="chart-container">
              <div ref="typeChartRef" class="chart" style="height: 300px;"></div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="16" style="margin-top: 16px;">
        <!-- 活跃用户排行 -->
        <el-col :span="12">
          <el-card shadow="never" class="chart-card">
            <template #header>
              <div class="chart-header">
                <h3>活跃用户排行榜</h3>
                <el-tag size="small" type="success">TOP 10</el-tag>
              </div>
            </template>
            <div class="ranking-list">
              <div 
                v-for="(user, index) in topOperators" 
                :key="user.operatorId"
                class="ranking-item"
                :class="getRankingClass(index)"
              >
                <div class="ranking-number">{{ index + 1 }}</div>
                <div class="user-info">
                  <div class="username">{{ user.operatorName }}</div>
                  <div class="user-id">ID: {{ user.operatorId }}</div>
                </div>
                <div class="operation-count">{{ user.operationCount }}次</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 操作热力图 -->
        <el-col :span="12">
          <el-card shadow="never" class="chart-card">
            <template #header>
              <div class="chart-header">
                <h3>操作时间分布</h3>
                <el-tag size="small" type="warning">24小时</el-tag>
              </div>
            </template>
            <div class="chart-container">
              <div ref="heatmapChartRef" class="chart" style="height: 300px;"></div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 详细分析表格 -->
    <div class="table-section">
      <el-card shadow="never">
        <template #header>
          <div class="table-header">
            <h3>详细分析数据</h3>
            <el-button size="small" @click="exportAnalysisData">
              <el-icon><Download /></el-icon>
              导出分析报告
            </el-button>
          </div>
        </template>

        <el-tabs v-model="activeTab" type="card">
          <el-tab-pane label="操作类型统计" name="operationType">
            <el-table :data="operationTypeStats" style="width: 100%">
              <el-table-column prop="operationType" label="操作类型" />
              <el-table-column prop="count" label="操作次数" />
              <el-table-column prop="percentage" label="占比">
                <template #default="scope">
                  {{ scope.row.percentage }}%
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>

          <el-tab-pane label="可疑活动检测" name="suspicious">
            <el-table :data="suspiciousActivities" style="width: 100%">
              <el-table-column prop="operatorName" label="操作者" />
              <el-table-column prop="operationCount" label="操作次数" />
              <el-table-column prop="riskLevel" label="风险等级">
                <template #default="scope">
                  <el-tag :type="getRiskTagType(scope.row.riskLevel)">
                    {{ scope.row.riskLevel }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="suggestion" label="建议" />
            </el-table>
          </el-tab-pane>

          <el-tab-pane label="用户活跃度分析" name="activity">
            <div class="activity-analysis">
              <div class="activity-distribution">
                <h4>活跃度等级分布</h4>
                <div class="distribution-items">
                  <div 
                    v-for="(count, level) in activityLevelDistribution" 
                    :key="level"
                    class="distribution-item"
                  >
                    <div class="level-name">{{ level }}</div>
                    <div class="level-count">{{ count }}人</div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  TrendCharts, Refresh, Operation, SuccessFilled, CircleCloseFilled, Download
} from '@element-plus/icons-vue'
import { request } from '@/utils/request'

// 引入ECharts
import * as echarts from 'echarts'

// 响应式数据
const loading = ref(false)
const dateRange = ref([])
const analyzeDays = ref(30)
const activeTab = ref('operationType')
const showPieChart = ref(true)

// 图表引用
const trendChartRef = ref()
const typeChartRef = ref()
const heatmapChartRef = ref()

// 数据
const basicStats = reactive({
  totalOperations: 0,
  successOperations: 0,
  failedOperations: 0,
  successRate: 0,
  avgDailyOperations: 0
})

const operationTypeStats = ref([])
const topOperators = ref([])
const suspiciousActivities = ref([])
const activityLevelDistribution = ref({})
const dailyTrend = ref([])
const hourlyDistribution = ref([])

// 图表实例
let trendChart: echarts.ECharts
let typeChart: echarts.ECharts  
let heatmapChart: echarts.ECharts

// 生命周期
onMounted(() => {
  initDefaultDateRange()
  loadBehaviorAnalysis()
  initCharts()
})

// 方法
const initDefaultDateRange = () => {
  const endDate = new Date()
  const startDate = new Date()
  startDate.setDate(endDate.getDate() - 30)
  
  dateRange.value = [
    startDate.toISOString().split('T')[0],
    endDate.toISOString().split('T')[0]
  ]
}

const setDateRange = (days: number) => {
  const endDate = new Date()
  const startDate = new Date()
  startDate.setDate(endDate.getDate() - days)
  
  dateRange.value = [
    startDate.toISOString().split('T')[0],
    endDate.toISOString().split('T')[0]
  ]
  
  // 更新分析天数
  analyzeDays.value = days
  
  // 自动刷新数据
  loadBehaviorAnalysis()
}

const isDateRangeActive = (days: number) => {
  if (!dateRange.value || dateRange.value.length !== 2) {
    return days === 30 // 默认30天
  }
  
  const startDate = new Date(dateRange.value[0])
  const endDate = new Date(dateRange.value[1])
  const actualDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
  
  // 检查是否是从今天往前数的指定天数
  const today = new Date()
  const expectedStartDate = new Date()
  expectedStartDate.setDate(today.getDate() - days)
  
  // 允许1天的误差
  const startDiff = Math.abs(startDate.getTime() - expectedStartDate.getTime()) / (1000 * 60 * 60 * 24)
  const endDiff = Math.abs(endDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
  
  return startDiff <= 1 && endDiff <= 1 && Math.abs(actualDays - days) <= 1
}

const handleDateRangeChange = (range: string[]) => {
  if (range && range.length === 2) {
    const start = new Date(range[0])
    const end = new Date(range[1])
    analyzeDays.value = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24))
    loadBehaviorAnalysis()
  }
}

const refreshData = () => {
  loadBehaviorAnalysis()
}

const loadBehaviorAnalysis = async () => {
  try {
    loading.value = true
    
    // 构建请求参数，支持日期范围筛选
    const params: any = {}
    
    if (dateRange.value && dateRange.value.length === 2) {
      // 如果选择了具体的日期范围，使用选择的日期
      params.startDate = dateRange.value[0]
      params.endDate = dateRange.value[1]
    } else {
      // 如果没有选择日期范围，使用默认的天数
      params.days = analyzeDays.value
    }
    
    const response = await request.get('/usermgmt/logs/user-behavior-analysis', {
      params
    })
    
    // 修复：使用code字段判断是否成功（200表示成功）
    const isSuccess = response.success === true || response.code === 200
    
    if (isSuccess) {
      const data = response.data
      
      // 更新基础统计数据
      Object.assign(basicStats, data.basicStats || {})
      
      // 更新操作类型统计
      operationTypeStats.value = (data.operationTypeDistribution || []).map((item: any[]) => ({
        operationType: item[0],
        count: item[1],
        percentage: ((item[1] / basicStats.totalOperations) * 100).toFixed(1)
      }))
      
      // 更新TOP操作者
      topOperators.value = (data.topOperators || []).map((item: any[]) => ({
        operatorId: item[0],
        operatorName: item[1],
        operationCount: item[2]
      }))
      
      // 更新可疑活动（模拟数据）
      suspiciousActivities.value = (data.suspiciousActivities || []).map((item: any[]) => ({
        operatorName: item[1],
        operationCount: item[2],
        riskLevel: item[2] > 50 ? '高' : item[2] > 20 ? '中' : '低',
        suggestion: item[2] > 50 ? '建议重点关注' : '正常范围'
      }))
      
      // 更新活跃度分布
      activityLevelDistribution.value = data.activityLevelDistribution || {}
      
      // 更新趋势数据
      dailyTrend.value = data.dailyOperationTrend || []
      
      hourlyDistribution.value = data.hourlyDistribution || []
      
      // 更新图表
      updateCharts()
      
    } else {
      ElMessage.error(response.message || '加载用户行为分析失败')
    }
  } catch (error) {
    console.error('异常详情:', error)
    ElMessage.error('加载用户行为分析失败，请重试')
  } finally {
    loading.value = false
  }
}

const toggleChartType = () => {
  showPieChart.value = !showPieChart.value
  updateTypeChart()
}

const exportAnalysisData = () => {
  ElMessage.success('分析报告导出功能开发中')
}

const getRankingClass = (index: number) => {
  if (index === 0) return 'rank-first'
  if (index === 1) return 'rank-second'  
  if (index === 2) return 'rank-third'
  return ''
}

const getRiskTagType = (riskLevel: string) => {
  switch (riskLevel) {
    case '高': return 'danger'
    case '中': return 'warning'
    case '低': return 'success'
    default: return 'info'
  }
}

// 图表初始化和更新方法
const initCharts = async () => {
  await nextTick()
  
  if (trendChartRef.value) {
    trendChart = echarts.init(trendChartRef.value)
  }
  if (typeChartRef.value) {
    typeChart = echarts.init(typeChartRef.value)
  }
  if (heatmapChartRef.value) {
    heatmapChart = echarts.init(heatmapChartRef.value)
  }
}

const updateCharts = () => {
  updateTrendChart()
  updateTypeChart()
  updateHeatmapChart()
}

const updateTrendChart = () => {
  if (!trendChart || !dailyTrend.value.length) {
    return
  }
  
  const dates = []
  const values = []
  
  // 处理趋势数据
  dailyTrend.value.forEach((item: any[]) => {
    dates.push(item[0]) // 日期
    values.push(item[1]) // 操作数量
  })
  
  const option = {
    title: {
      text: '操作趋势分析',
      left: 'center',
      textStyle: {
        fontSize: 14,
        color: '#666'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: '{b}: {c}次操作'
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLabel: {
        fontSize: 12
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        fontSize: 12
      }
    },
    series: [{
      data: values,
      type: 'line',
      smooth: true,
      areaStyle: {
        opacity: 0.3
      },
      lineStyle: {
        color: '#1890ff'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0, color: 'rgba(24, 144, 255, 0.6)'
          }, {
            offset: 1, color: 'rgba(24, 144, 255, 0.1)'
          }]
        }
      }
    }]
  }
  
  trendChart.setOption(option)
}

const updateTypeChart = () => {
  if (!typeChart || !operationTypeStats.value.length) return
  
  const data = operationTypeStats.value.map((item: any) => ({
    name: item.operationType,
    value: item.count
  }))
  
  let option
  
  if (showPieChart.value) {
    // 饼图配置
    option = {
      title: {
        text: '操作类型分布',
        left: 'center',
        textStyle: {
          fontSize: 14,
          color: '#666'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      series: [{
        name: '操作类型',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '60%'],
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    }
  } else {
    // 柱状图配置
    option = {
      title: {
        text: '操作类型分布',
        left: 'center',
        textStyle: {
          fontSize: 14,
          color: '#666'
        }
      },
      tooltip: {
        trigger: 'axis',
        formatter: '{b}: {c}次'
      },
      xAxis: {
        type: 'category',
        data: data.map(item => item.name),
        axisLabel: {
          fontSize: 12,
          rotate: 45
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          fontSize: 12
        }
      },
      series: [{
        data: data.map(item => item.value),
        type: 'bar',
        itemStyle: {
          color: '#1890ff'
        }
      }]
    }
  }
  
  typeChart.setOption(option)
}

const updateHeatmapChart = () => {
  if (!heatmapChart || !hourlyDistribution.value.length) {
    return
  }
  
  // 处理24小时数据，创建简单的柱状图展示
  const hourlyData = []
  const hourLabels = []
  
  hourlyDistribution.value.forEach((item: any[]) => {
    const hour = item[0] // 小时
    const count = item[1] // 操作数量
    hourLabels.push(hour + ':00')
    hourlyData.push(count)
  })
  
  const maxValue = Math.max(...hourlyData)
  
  const option = {
    title: {
      text: '操作时间分布',
      left: 'center',
      textStyle: {
        fontSize: 14,
        color: '#666'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: '{b}: {c}次操作'
    },
    xAxis: {
      type: 'category',
      data: hourLabels,
      axisLabel: {
        fontSize: 11,
        rotate: 0,
        interval: 1 // 显示所有标签
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        fontSize: 12
      }
    },
    series: [{
      name: '操作次数',
      type: 'bar',
      data: hourlyData,
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0, color: '#1890ff'
          }, {
            offset: 1, color: '#69c0ff'
          }]
        }
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.3)'
        }
      }
    }]
  }
  
  heatmapChart.setOption(option)
}
</script>

<style scoped>
.user-behavior-analysis {
  padding: 24px;
  background-color: var(--el-bg-color-page);
  min-height: calc(100vh - 64px);
}

.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--el-bg-color);
  padding: 24px;
  border-radius: 8px;
  box-shadow: var(--el-box-shadow-light);
  border: 1px solid var(--el-border-color-light);
}

.header-left {
  display: flex;
  flex-direction: column;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-description {
  margin: 0;
  color: var(--el-text-color-regular);
  font-size: 13px;
}

.header-right {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.date-shortcuts {
  display: flex;
  gap: 6px;
}

.overview-section {
  margin-bottom: 24px;
}

.stats-card {
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  transition: transform 0.3s ease;
  background: var(--el-bg-color);
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--el-box-shadow);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #fff;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.success {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.average {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.failed {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.charts-section {
  margin-bottom: 24px;
}

.chart-card {
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  background: var(--el-bg-color);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-header h3 {
  margin: 0;
  font-size: 16px;
  color: var(--el-text-color-primary);
}

.chart-container {
  padding: 16px 0;
}

.chart {
  background: var(--el-fill-color-lighter);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.ranking-list {
  max-height: 300px;
  overflow-y: auto;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
  transition: background-color 0.3s ease;
}

.ranking-item:hover {
  background-color: var(--el-fill-color-light);
}

.ranking-item:last-child {
  border-bottom: none;
}

.ranking-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--el-fill-color);
  color: var(--el-text-color-regular);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 12px;
}

.ranking-item.rank-first .ranking-number {
  background: linear-gradient(135deg, #ffd700 0%, #ffb700 100%);
  color: #fff;
}

.ranking-item.rank-second .ranking-number {
  background: linear-gradient(135deg, #c0c0c0 0%, #a8a8a8 100%);
  color: #fff;
}

.ranking-item.rank-third .ranking-number {
  background: linear-gradient(135deg, #cd7f32 0%, #b8860b 100%);
  color: #fff;
}

.user-info {
  flex: 1;
}

.username {
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 2px;
}

.user-id {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.operation-count {
  font-weight: 600;
  color: var(--el-color-primary);
}

.table-section {
  background: var(--el-bg-color);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--el-border-color-light);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-header h3 {
  margin: 0;
  font-size: 16px;
  color: var(--el-text-color-primary);
}

.activity-analysis {
  padding: 16px 0;
}

.activity-distribution h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: var(--el-text-color-primary);
}

.distribution-items {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}

.distribution-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  background: var(--el-fill-color-light);
  border-radius: 6px;
  min-width: 120px;
  border: 1px solid var(--el-border-color-lighter);
}

.level-name {
  font-size: 12px;
  color: var(--el-text-color-regular);
  margin-bottom: 8px;
}

.level-count {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

:deep(.el-card__header) {
  padding: 16px 20px;
  background: var(--el-fill-color-lighter);
  border-bottom: 1px solid var(--el-border-color-light);
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-tabs__header) {
  margin-bottom: 16px;
}
</style>