package com.rickpan.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 系统监控告警传输对象
 * 用于告警配置和告警日志数据传输
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysMonitorAlertDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    // ==================== 告警配置相关 ====================

    /**
     * 配置ID
     */
    private Long configId;

    /**
     * 指标类型
     */
    @NotBlank(message = "指标类型不能为空")
    private String metricType;

    /**
     * 指标名称
     */
    @NotBlank(message = "指标名称不能为空")
    private String metricName;

    /**
     * 指标显示名称
     */
    private String metricDisplayName;

    /**
     * 警告阈值
     */
    private BigDecimal warningThreshold;

    /**
     * 严重阈值
     */
    private BigDecimal criticalThreshold;

    /**
     * 紧急阈值
     */
    private BigDecimal emergencyThreshold;

    /**
     * 比较操作符
     */
    @Builder.Default
    private String comparisonOperator = "GT";

    /**
     * 单位
     */
    private String unit;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否启用
     */
    @Builder.Default
    private Boolean enabled = true;

    /**
     * 是否启用通知
     */
    @Builder.Default
    private Boolean notificationEnabled = true;

    // ==================== 告警日志相关 ====================

    /**
     * 告警日志ID
     */
    private Long alertId;

    /**
     * 告警类型
     */
    private String alertType;

    /**
     * 告警级别
     */
    private String alertLevel;

    /**
     * 告警状态
     */
    private String alertStatus;

    /**
     * 告警标题
     */
    private String alertTitle;

    /**
     * 告警消息
     */
    private String alertMessage;

    /**
     * 指标值
     */
    private BigDecimal metricValue;

    /**
     * 阈值
     */
    private BigDecimal thresholdValue;

    /**
     * 告警时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime alertTime;

    /**
     * 首次告警时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime firstAlertTime;

    /**
     * 解决时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime resolvedTime;

    /**
     * 解决人
     */
    private String resolvedBy;

    /**
     * 解决备注
     */
    private String resolveNote;

    /**
     * 发生次数
     */
    @Builder.Default
    private Integer occurrenceCount = 1;

    // ==================== 统计相关 ====================

    /**
     * 告警统计信息
     */
    private AlertStats stats;

    /**
     * 告警统计信息内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AlertStats implements Serializable {
        /**
         * 总告警数
         */
        private Long totalAlerts;

        /**
         * 活跃告警数
         */
        private Long activeAlerts;

        /**
         * 已解决告警数
         */
        private Long resolvedAlerts;

        /**
         * 警告级别告警数
         */
        private Long warningAlerts;

        /**
         * 严重级别告警数
         */
        private Long criticalAlerts;

        /**
         * 紧急级别告警数
         */
        private Long emergencyAlerts;

        /**
         * 统计时间范围
         */
        private String timeRange;

        /**
         * 统计日期
         */
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDateTime statDate;
    }
}