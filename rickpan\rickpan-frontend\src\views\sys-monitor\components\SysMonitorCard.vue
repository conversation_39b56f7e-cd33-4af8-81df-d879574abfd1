<template>
  <div class="sys-monitor-card">
    <el-card class="monitor-card" shadow="hover" :class="[`status-${status}`, { 'card-loading': loading }]">
      <div class="card-content">
        <!-- 卡片头部 -->
        <div class="card-header">
          <div class="header-left">
            <div class="card-icon" :class="`icon-${status}`">
              <el-icon><component :is="icon" /></el-icon>
            </div>
            <div class="card-title">{{ title }}</div>
          </div>
          <div class="header-right">
            <div class="trend-indicator" v-if="trend && !loading">
              <el-icon :class="`trend-${trend}`">
                <ArrowUp v-if="trend === 'up'" />
                <ArrowDown v-if="trend === 'down'" />
                <Minus v-if="trend === 'stable'" />
              </el-icon>
            </div>
          </div>
        </div>

        <!-- 主要数值显示 -->
        <div class="card-body">
          <div class="main-value" v-if="!loading">
            <span class="value-number" :class="`text-${status}`">{{ value }}</span>
            <span class="value-suffix" v-if="suffix">{{ suffix }}</span>
          </div>
          <el-skeleton v-else :rows="1" animated>
            <template #template>
              <el-skeleton-item variant="text" style="width: 60%; height: 36px;" />
            </template>
          </el-skeleton>
        </div>

        <!-- 额外信息 -->
        <div class="card-footer" v-if="$slots.extra && !loading">
          <slot name="extra"></slot>
        </div>
        
        <!-- 进度条 -->
        <div class="progress-bar" v-if="showProgress && !loading">
          <el-progress
            :percentage="Number(value)"
            :color="progressColor"
            :show-text="false"
            :stroke-width="4"
          />
        </div>
      </div>

      <!-- 加载遮罩 -->
      <div class="loading-overlay" v-if="loading">
        <el-icon class="loading-icon"><Loading /></el-icon>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ArrowUp, ArrowDown, Minus, Loading } from '@element-plus/icons-vue'

// 定义Props
interface Props {
  title: string
  icon: any
  value: string | number
  status: 'normal' | 'warning' | 'danger' | 'unknown'
  loading?: boolean
  suffix?: string
  trend?: 'up' | 'down' | 'stable'
  showProgress?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  suffix: '',
  trend: 'stable',
  showProgress: true
})

// 计算属性
const progressColor = computed(() => {
  const colors = {
    normal: '#67C23A',
    warning: '#E6A23C', 
    danger: '#F56C6C',
    unknown: '#909399'
  }
  return colors[props.status]
})
</script>

<style scoped lang="scss">
.sys-monitor-card {
  height: 100%;

  .monitor-card {
    height: 100%;
    position: relative;
    transition: all 0.3s ease;
    border-radius: 12px;
    overflow: hidden;
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color-light);

    &:hover {
      transform: translateY(-2px);
    }

    &.card-loading {
      pointer-events: none;
    }

    // 根据状态设置边框颜色
    &.status-normal {
      border-left: 4px solid #67C23A;
      
      &:hover {
        box-shadow: 0 8px 25px rgba(103, 194, 58, 0.15);
      }
    }

    &.status-warning {
      border-left: 4px solid #E6A23C;
      
      &:hover {
        box-shadow: 0 8px 25px rgba(230, 162, 60, 0.15);
      }
    }

    &.status-danger {
      border-left: 4px solid #F56C6C;
      
      &:hover {
        box-shadow: 0 8px 25px rgba(245, 108, 108, 0.15);
      }
    }

    &.status-unknown {
      border-left: 4px solid #909399;
      
      &:hover {
        box-shadow: 0 8px 25px rgba(144, 147, 153, 0.15);
      }
    }

    :deep(.el-card__body) {
      padding: 20px;
      height: 100%;
      background: var(--el-bg-color);
    }
  }

  .card-content {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .header-left {
      display: flex;
      align-items: center;
      flex: 1;

      .card-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        font-size: 20px;
        transition: all 0.3s ease;

        &.icon-normal {
          background: rgba(103, 194, 58, 0.1);
          color: #67C23A;
        }

        &.icon-warning {
          background: rgba(230, 162, 60, 0.1);
          color: #E6A23C;
        }

        &.icon-danger {
          background: rgba(245, 108, 108, 0.1);
          color: #F56C6C;
        }

        &.icon-unknown {
          background: rgba(144, 147, 153, 0.1);
          color: #909399;
        }
      }

      .card-title {
        font-size: 14px;
        font-weight: 500;
        color: var(--el-text-color-regular);
        line-height: 1.4;
      }
    }

    .header-right {
      .trend-indicator {
        .el-icon {
          font-size: 16px;
          transition: all 0.3s ease;

          &.trend-up {
            color: #F56C6C;
            transform: rotate(0deg);
          }

          &.trend-down {
            color: #67C23A;
            transform: rotate(0deg);
          }

          &.trend-stable {
            color: #909399;
            transform: rotate(0deg);
          }
        }
      }
    }
  }

  .card-body {
    flex: 1;
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .main-value {
      display: flex;
      align-items: baseline;
      gap: 4px;

      .value-number {
        font-size: 32px;
        font-weight: 700;
        line-height: 1;
        transition: color 0.3s ease;

        &.text-normal {
          color: #67C23A;
        }

        &.text-warning {
          color: #E6A23C;
        }

        &.text-danger {
          color: #F56C6C;
        }

        &.text-unknown {
          color: #909399;
        }
      }

      .value-suffix {
        font-size: 16px;
        font-weight: 500;
        color: var(--el-text-color-regular);
      }
    }
  }

  .card-footer {
    margin-bottom: 12px;
    font-size: 12px;
    color: var(--el-text-color-regular);
  }

  .progress-bar {
    margin-top: auto;

    :deep(.el-progress-bar__outer) {
      background-color: var(--el-fill-color);
      border-radius: 2px;
    }

    :deep(.el-progress-bar__inner) {
      border-radius: 2px;
      transition: all 0.8s ease;
    }
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--el-mask-color);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;

    .loading-icon {
      font-size: 24px;
      color: #409eff;
      animation: rotating 2s linear infinite;
    }
  }

  // 动画
  @keyframes rotating {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .card-header {
      .header-left {
        .card-icon {
          width: 32px;
          height: 32px;
          font-size: 16px;
          margin-right: 8px;
        }

        .card-title {
          font-size: 13px;
        }
      }
    }

    .card-body {
      .main-value {
        .value-number {
          font-size: 24px;
        }

        .value-suffix {
          font-size: 14px;
        }
      }
    }

    .monitor-card {
      :deep(.el-card__body) {
        padding: 16px;
      }
    }
  }

  @media (max-width: 480px) {
    .card-header {
      .header-left {
        .card-icon {
          width: 28px;
          height: 28px;
          font-size: 14px;
          margin-right: 6px;
        }

        .card-title {
          font-size: 12px;
        }
      }
    }

    .card-body {
      .main-value {
        .value-number {
          font-size: 20px;
        }

        .value-suffix {
          font-size: 12px;
        }
      }
    }

    .monitor-card {
      :deep(.el-card__body) {
        padding: 12px;
      }
    }
  }
}
</style>