package com.rickpan.controller;

import com.rickpan.common.ApiResponse;
import com.rickpan.dto.usermgmt.UserMgmtOperationLogQueryDTO;
import com.rickpan.dto.usermgmt.UserMgmtOperationLogDetailDTO;
import com.rickpan.dto.usermgmt.UserMgmtPageDTO;
import com.rickpan.service.UserMgmtOperationLogQueryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.Optional;

/**
 * 用户管理操作日志查询控制器
 * Operation Log Query Controller - 管理员专用操作日志查询接口
 * 
 * <AUTHOR> Team
 * @since Phase 3
 */
@Tag(name = "操作日志管理", description = "管理员操作日志查询相关接口")
@RestController
@RequestMapping("/api/usermgmt/logs")
@PreAuthorize("hasRole('ADMIN')")
public class UserMgmtOperationLogController {

    private static final Logger logger = LoggerFactory.getLogger(UserMgmtOperationLogController.class);

    @Autowired
    private UserMgmtOperationLogQueryService operationLogQueryService;

    /**
     * 获取操作日志列表（分页）
     * 
     * @param page 页码（从1开始）
     * @param size 每页大小
     * @param operatorId 操作者ID
     * @param operatorName 操作者用户名
     * @param targetUserId 目标用户ID
     * @param targetUsername 目标用户名
     * @param operationType 操作类型
     * @param operationResult 操作结果
     * @param keyword 关键词搜索
     * @param ipAddress IP地址
     * @param requestUri 请求URI
     * @param requestMethod 请求方法
     * @param startTime 开始时间（格式：2024-01-01T00:00:00）
     * @param endTime 结束时间（格式：2024-01-01T23:59:59）
     * @param sortBy 排序字段
     * @param sortDirection 排序方向
     * @return 分页操作日志列表
     */
    @GetMapping("/list")
    @Operation(summary = "获取操作日志列表", description = "分页获取操作日志列表，支持多条件筛选和排序")
    public ApiResponse<UserMgmtPageDTO<UserMgmtOperationLogDetailDTO>> getOperationLogList(
            @Parameter(description = "页码（从1开始）", example = "1")
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            
            @Parameter(description = "每页大小", example = "20")
            @RequestParam(value = "size", defaultValue = "20") Integer size,
            
            @Parameter(description = "操作者ID", example = "1")
            @RequestParam(value = "operatorId", required = false) Long operatorId,
            
            @Parameter(description = "操作者用户名", example = "admin")
            @RequestParam(value = "operatorName", required = false) String operatorName,
            
            @Parameter(description = "目标用户ID", example = "2")
            @RequestParam(value = "targetUserId", required = false) Long targetUserId,
            
            @Parameter(description = "目标用户名", example = "testuser")
            @RequestParam(value = "targetUsername", required = false) String targetUsername,
            
            @Parameter(description = "操作类型", example = "UPDATE_USER_INFO")
            @RequestParam(value = "operationType", required = false) String operationType,
            
            @Parameter(description = "操作结果", example = "SUCCESS")
            @RequestParam(value = "operationResult", required = false) String operationResult,
            
            @Parameter(description = "关键词搜索（操作描述）", example = "更新用户")
            @RequestParam(value = "keyword", required = false) String keyword,
            
            @Parameter(description = "IP地址", example = "***********")
            @RequestParam(value = "ipAddress", required = false) String ipAddress,
            
            @Parameter(description = "请求URI", example = "/api/usermgmt/users")
            @RequestParam(value = "requestUri", required = false) String requestUri,
            
            @Parameter(description = "请求方法", example = "POST")
            @RequestParam(value = "requestMethod", required = false) String requestMethod,
            
            @Parameter(description = "开始时间", example = "2024-01-01T00:00:00")
            @RequestParam(value = "startTime", required = false) String startTime,
            
            @Parameter(description = "结束时间", example = "2024-01-01T23:59:59")
            @RequestParam(value = "endTime", required = false) String endTime,
            
            @Parameter(description = "排序字段", example = "createdAt")
            @RequestParam(value = "sortBy", defaultValue = "createdAt") String sortBy,
            
            @Parameter(description = "排序方向", example = "DESC")
            @RequestParam(value = "sortDirection", defaultValue = "DESC") String sortDirection) {
        
        try {
            logger.debug("管理员获取操作日志列表请求 - page:{}, size:{}, operatorId:{}, targetUserId:{}", 
                    page, size, operatorId, targetUserId);
            
            // 构建查询参数DTO
            UserMgmtOperationLogQueryDTO queryDTO = new UserMgmtOperationLogQueryDTO();
            queryDTO.setPage(page);
            queryDTO.setSize(size);
            queryDTO.setOperatorId(operatorId);
            queryDTO.setOperatorName(operatorName);
            queryDTO.setTargetUserId(targetUserId);
            queryDTO.setTargetUsername(targetUsername);
            queryDTO.setKeyword(keyword);
            queryDTO.setIpAddress(ipAddress);
            queryDTO.setRequestUri(requestUri);
            queryDTO.setRequestMethod(requestMethod);
            queryDTO.setSortBy(sortBy);
            queryDTO.setSortDirection(sortDirection);
            
            // 设置操作类型和结果（需要转换为枚举）
            if (operationType != null && !operationType.trim().isEmpty()) {
                try {
                    queryDTO.setOperationType(
                        com.rickpan.entity.UserMgmtOperationLog.OperationType.valueOf(operationType));
                } catch (IllegalArgumentException e) {
                    logger.warn("无效的操作类型: {}", operationType);
                }
            }
            
            if (operationResult != null && !operationResult.trim().isEmpty()) {
                try {
                    queryDTO.setOperationResult(
                        com.rickpan.entity.UserMgmtOperationLog.OperationResult.valueOf(operationResult));
                } catch (IllegalArgumentException e) {
                    logger.warn("无效的操作结果: {}", operationResult);
                }
            }
            
            // 设置时间范围
            if (startTime != null && !startTime.trim().isEmpty()) {
                try {
                    queryDTO.setStartTime(java.time.LocalDateTime.parse(startTime));
                } catch (Exception e) {
                    logger.warn("无效的开始时间格式: {}", startTime);
                }
            }
            
            if (endTime != null && !endTime.trim().isEmpty()) {
                try {
                    queryDTO.setEndTime(java.time.LocalDateTime.parse(endTime));
                } catch (Exception e) {
                    logger.warn("无效的结束时间格式: {}", endTime);
                }
            }
            
            // 调用服务层获取操作日志列表
            UserMgmtPageDTO<UserMgmtOperationLogDetailDTO> result = 
                    operationLogQueryService.getOperationLogList(queryDTO);
            
            logger.debug("管理员获取操作日志列表成功 - 总记录数:{}, 当前页面记录数:{}", 
                    result.getTotalElements(), result.getContent().size());
            
            return ApiResponse.success("获取操作日志列表成功", result);
            
        } catch (Exception e) {
            logger.error("管理员获取操作日志列表失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取操作日志列表失败: " + e.getMessage());
        }
    }

    /**
     * 复杂条件搜索操作日志
     * 
     * @param queryDTO 搜索查询参数
     * @return 搜索结果
     */
    @PostMapping("/search")
    @Operation(summary = "搜索操作日志", description = "根据复杂条件搜索操作日志，支持POST方式传递复杂参数")
    public ApiResponse<UserMgmtPageDTO<UserMgmtOperationLogDetailDTO>> searchOperationLogs(
            @Parameter(description = "搜索查询参数") 
            @Valid @RequestBody UserMgmtOperationLogQueryDTO queryDTO) {
        
        try {
            logger.debug("管理员搜索操作日志请求: {}", queryDTO);
            
            // 调用服务层搜索操作日志
            UserMgmtPageDTO<UserMgmtOperationLogDetailDTO> result = 
                    operationLogQueryService.searchOperationLogs(queryDTO);
            
            logger.debug("管理员搜索操作日志成功 - 查询到{}条记录", result.getTotalElements());
            
            return ApiResponse.success("搜索操作日志成功", result);
            
        } catch (Exception e) {
            logger.error("管理员搜索操作日志失败: {}", e.getMessage(), e);
            return ApiResponse.error("搜索操作日志失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取操作日志详情
     * 
     * @param logId 操作日志ID
     * @return 操作日志详细信息
     */
    @GetMapping("/{logId}")
    @Operation(summary = "获取操作日志详情", description = "根据日志ID获取操作日志详细信息")
    public ApiResponse<UserMgmtOperationLogDetailDTO> getOperationLogById(
            @Parameter(description = "操作日志ID", example = "1")
            @PathVariable("logId") Long logId) {
        
        try {
            logger.debug("管理员获取操作日志详情请求 - logId:{}", logId);
            
            // 参数验证
            if (logId == null || logId <= 0) {
                return ApiResponse.badRequest("操作日志ID无效");
            }
            
            // 调用服务层获取操作日志详情
            Optional<UserMgmtOperationLogDetailDTO> logOpt = 
                    operationLogQueryService.getOperationLogById(logId);
            
            if (logOpt.isEmpty()) {
                logger.warn("操作日志不存在 - logId:{}", logId);
                return ApiResponse.notFound("操作日志不存在");
            }
            
            UserMgmtOperationLogDetailDTO log = logOpt.get();
            logger.debug("管理员获取操作日志详情成功 - logId:{}, operationType:{}", 
                    logId, log.getOperationType());
            
            return ApiResponse.success("获取操作日志详情成功", log);
            
        } catch (Exception e) {
            logger.error("管理员获取操作日志详情失败 - logId:{}, error:{}", logId, e.getMessage(), e);
            return ApiResponse.error("获取操作日志详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取操作类型选项
     * 
     * @return 操作类型选项列表
     */
    @GetMapping("/operation-types")
    @Operation(summary = "获取操作类型选项", description = "获取所有可用的操作类型选项")
    public ApiResponse<Map<String, String>> getOperationTypes() {
        
        try {
            logger.debug("获取操作类型选项请求");
            
            // 调用服务层获取操作类型选项
            Map<String, String> operationTypes = operationLogQueryService.getOperationTypes();
            
            return ApiResponse.success("获取操作类型选项成功", operationTypes);
            
        } catch (Exception e) {
            logger.error("获取操作类型选项失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取操作类型选项失败: " + e.getMessage());
        }
    }

    /**
     * 获取操作结果选项
     * 
     * @return 操作结果选项列表
     */
    @GetMapping("/operation-results")
    @Operation(summary = "获取操作结果选项", description = "获取所有可用的操作结果选项")
    public ApiResponse<Map<String, String>> getOperationResults() {
        
        try {
            logger.debug("获取操作结果选项请求");
            
            // 调用服务层获取操作结果选项
            Map<String, String> operationResults = operationLogQueryService.getOperationResults();
            
            return ApiResponse.success("获取操作结果选项成功", operationResults);
            
        } catch (Exception e) {
            logger.error("获取操作结果选项失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取操作结果选项失败: " + e.getMessage());
        }
    }

    /**
     * 清理历史日志
     * 
     * @param beforeDays 保留天数（删除多少天前的日志）
     * @return 清理结果
     */
    @DeleteMapping("/cleanup")
    @Operation(summary = "清理历史日志", description = "删除指定天数之前的历史操作日志")
    public ApiResponse<Map<String, Object>> cleanupOperationLogs(
            @Parameter(description = "保留天数（删除多少天前的日志）", example = "90")
            @RequestParam(value = "beforeDays", defaultValue = "90") Integer beforeDays) {
        
        try {
            logger.debug("管理员清理历史操作日志请求 - beforeDays:{}", beforeDays);
            
            // 参数验证
            if (beforeDays == null || beforeDays < 30) {
                return ApiResponse.badRequest("保留天数不能少于30天");
            }
            
            // 调用服务层清理历史日志
            Map<String, Object> result = operationLogQueryService.cleanupOperationLogs(beforeDays);
            
            logger.info("管理员清理历史操作日志成功 - beforeDays:{}, cleanedCount:{}", 
                    beforeDays, result.get("cleanedCount"));
            
            return ApiResponse.success("清理历史日志成功", result);
            
        } catch (Exception e) {
            logger.error("管理员清理历史操作日志失败 - beforeDays:{}, error:{}", beforeDays, e.getMessage(), e);
            return ApiResponse.error("清理历史日志失败: " + e.getMessage());
        }
    }

    /**
     * 导出操作日志
     * 
     * @param format 导出格式（excel/csv）
     * @param operatorId 操作者ID
     * @param operatorName 操作者用户名
     * @param targetUserId 目标用户ID
     * @param targetUsername 目标用户名
     * @param operationType 操作类型
     * @param operationResult 操作结果
     * @param keyword 关键词搜索
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param maxRecords 最大导出记录数
     * @return 导出文件
     */
    @GetMapping("/export")
    @Operation(summary = "导出操作日志", description = "根据筛选条件导出操作日志到Excel或CSV文件")
    public void exportOperationLogs(
            HttpServletResponse response,
            
            @Parameter(description = "导出格式", example = "excel")
            @RequestParam(value = "format", defaultValue = "excel") String format,
            
            @Parameter(description = "操作者ID", example = "1")
            @RequestParam(value = "operatorId", required = false) Long operatorId,
            
            @Parameter(description = "操作者用户名", example = "admin")
            @RequestParam(value = "operatorName", required = false) String operatorName,
            
            @Parameter(description = "目标用户ID", example = "2")
            @RequestParam(value = "targetUserId", required = false) Long targetUserId,
            
            @Parameter(description = "目标用户名", example = "testuser")
            @RequestParam(value = "targetUsername", required = false) String targetUsername,
            
            @Parameter(description = "操作类型", example = "UPDATE_USER_INFO")
            @RequestParam(value = "operationType", required = false) String operationType,
            
            @Parameter(description = "操作结果", example = "SUCCESS")
            @RequestParam(value = "operationResult", required = false) String operationResult,
            
            @Parameter(description = "关键词搜索", example = "更新用户")
            @RequestParam(value = "keyword", required = false) String keyword,
            
            @Parameter(description = "开始时间", example = "2024-01-01T00:00:00")
            @RequestParam(value = "startTime", required = false) String startTime,
            
            @Parameter(description = "结束时间", example = "2024-01-01T23:59:59")
            @RequestParam(value = "endTime", required = false) String endTime,
            
            @Parameter(description = "最大导出记录数", example = "10000")
            @RequestParam(value = "maxRecords", defaultValue = "10000") Integer maxRecords) {
        
        try {
            logger.debug("管理员导出操作日志请求 - format:{}, maxRecords:{}", format, maxRecords);
            
            // 参数验证
            if (maxRecords > 50000) {
                maxRecords = 50000; // 限制最大导出数量
            }
            
            // 构建查询参数DTO
            UserMgmtOperationLogQueryDTO queryDTO = new UserMgmtOperationLogQueryDTO();
            queryDTO.setPage(1);
            queryDTO.setSize(maxRecords);
            queryDTO.setOperatorId(operatorId);
            queryDTO.setOperatorName(operatorName);
            queryDTO.setTargetUserId(targetUserId);
            queryDTO.setTargetUsername(targetUsername);
            queryDTO.setKeyword(keyword);
            queryDTO.setSortBy("createdAt");
            queryDTO.setSortDirection("DESC");
            
            // 设置操作类型和结果
            if (operationType != null && !operationType.trim().isEmpty()) {
                try {
                    queryDTO.setOperationType(
                        com.rickpan.entity.UserMgmtOperationLog.OperationType.valueOf(operationType));
                } catch (IllegalArgumentException e) {
                    logger.warn("无效的操作类型: {}", operationType);
                }
            }
            
            if (operationResult != null && !operationResult.trim().isEmpty()) {
                try {
                    queryDTO.setOperationResult(
                        com.rickpan.entity.UserMgmtOperationLog.OperationResult.valueOf(operationResult));
                } catch (IllegalArgumentException e) {
                    logger.warn("无效的操作结果: {}", operationResult);
                }
            }
            
            // 设置时间范围
            if (startTime != null && !startTime.trim().isEmpty()) {
                try {
                    queryDTO.setStartTime(java.time.LocalDateTime.parse(startTime));
                } catch (Exception e) {
                    logger.warn("无效的开始时间格式: {}", startTime);
                }
            }
            
            if (endTime != null && !endTime.trim().isEmpty()) {
                try {
                    queryDTO.setEndTime(java.time.LocalDateTime.parse(endTime));
                } catch (Exception e) {
                    logger.warn("无效的结束时间格式: {}", endTime);
                }
            }
            
            // 调用服务层导出操作日志
            operationLogQueryService.exportOperationLogs(response, queryDTO, format);
            
            logger.info("管理员导出操作日志成功 - format:{}, maxRecords:{}", format, maxRecords);
            
        } catch (Exception e) {
            logger.error("管理员导出操作日志失败 - format:{}, error:{}", format, e.getMessage(), e);
            
            // 设置错误响应
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.setContentType("application/json;charset=UTF-8");
            try {
                response.getWriter().write("{\"success\":false,\"message\":\"导出失败: " + e.getMessage() + "\"}");
            } catch (Exception ex) {
                logger.error("写入错误响应失败:", ex);
            }
        }
    }

    /**
     * 用户行为分析统计
     * 
     * @param days 统计天数（当未指定具体日期范围时使用）
     * @param startDate 开始日期（可选，格式：2024-01-01）
     * @param endDate 结束日期（可选，格式：2024-01-31）
     * @param operatorId 指定操作者ID（可选）
     * @return 用户行为分析结果
     */
    @GetMapping("/user-behavior-analysis")
    @Operation(summary = "用户行为分析统计", description = "分析用户操作行为模式和统计数据，支持按天数或具体日期范围查询")
    public ApiResponse<Map<String, Object>> getUserBehaviorAnalysis(
            @Parameter(description = "统计天数（当未指定具体日期范围时使用）", example = "30")
            @RequestParam(value = "days", defaultValue = "30") Integer days,
            
            @Parameter(description = "开始日期", example = "2024-01-01")
            @RequestParam(value = "startDate", required = false) String startDate,
            
            @Parameter(description = "结束日期", example = "2024-01-31")
            @RequestParam(value = "endDate", required = false) String endDate,
            
            @Parameter(description = "指定操作者ID", example = "1")
            @RequestParam(value = "operatorId", required = false) Long operatorId) {
        
        try {
            logger.debug("获取用户行为分析统计 - days:{}, startDate:{}, endDate:{}, operatorId:{}", 
                        days, startDate, endDate, operatorId);
            
            Map<String, Object> behaviorAnalysis;
            
            // 如果指定了具体的日期范围，使用日期范围查询
            if (startDate != null && endDate != null && 
                !startDate.trim().isEmpty() && !endDate.trim().isEmpty()) {
                
                try {
                    // 解析日期（支持YYYY-MM-DD格式）
                    java.time.LocalDate start = java.time.LocalDate.parse(startDate);
                    java.time.LocalDate end = java.time.LocalDate.parse(endDate);
                    
                    // 转换为LocalDateTime（开始日期的00:00:00和结束日期的23:59:59）
                    java.time.LocalDateTime startDateTime = start.atStartOfDay();
                    java.time.LocalDateTime endDateTime = end.atTime(23, 59, 59);
                    
                    logger.info("使用指定日期范围查询 - startDateTime:{}, endDateTime:{}", startDateTime, endDateTime);
                    
                    // 调用服务层获取指定日期范围的用户行为分析
                    behaviorAnalysis = operationLogQueryService.getUserBehaviorAnalysisByDateRange(
                            startDateTime, endDateTime, operatorId);
                    
                } catch (Exception e) {
                    logger.warn("日期格式解析失败，使用默认天数查询 - startDate:{}, endDate:{}, error:{}", 
                               startDate, endDate, e.getMessage());
                    
                    // 日期解析失败，回退到使用天数查询
                    if (days <= 0 || days > 365) {
                        return ApiResponse.error("统计天数必须在1-365之间");
                    }
                    behaviorAnalysis = operationLogQueryService.getUserBehaviorAnalysisNoCache(days, operatorId);
                }
                
            } else {
                // 使用天数查询
                if (days <= 0 || days > 365) {
                    return ApiResponse.error("统计天数必须在1-365之间");
                }
                
                logger.info("使用默认天数查询 - days:{}", days);
                behaviorAnalysis = operationLogQueryService.getUserBehaviorAnalysisNoCache(days, operatorId);
            }
            
            logger.info("获取用户行为分析统计成功 - days:{}, startDate:{}, endDate:{}, operatorId:{}", 
                       days, startDate, endDate, operatorId);
            return ApiResponse.success("获取用户行为分析成功", behaviorAnalysis);
            
        } catch (Exception e) {
            logger.error("获取用户行为分析统计失败 - days:{}, startDate:{}, endDate:{}, operatorId:{}, error:{}", 
                        days, startDate, endDate, operatorId, e.getMessage(), e);
            return ApiResponse.error("获取用户行为分析失败: " + e.getMessage());
        }
    }

    /**
     * 活跃用户分析
     * 
     * @param days 统计天数
     * @param limit 返回用户数量限制
     * @return 活跃用户分析结果
     */
    @GetMapping("/active-users-analysis")
    @Operation(summary = "活跃用户分析", description = "分析最活跃的操作用户")
    public ApiResponse<Map<String, Object>> getActiveUsersAnalysis(
            @Parameter(description = "统计天数", example = "7")
            @RequestParam(value = "days", defaultValue = "7") Integer days,
            
            @Parameter(description = "返回用户数量限制", example = "10")
            @RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        
        try {
            logger.debug("获取活跃用户分析 - days:{}, limit:{}", days, limit);
            
            // 参数验证
            if (days <= 0 || days > 365) {
                return ApiResponse.error("统计天数必须在1-365之间");
            }
            if (limit <= 0 || limit > 100) {
                return ApiResponse.error("用户数量限制必须在1-100之间");
            }
            
            // 调用服务层获取活跃用户分析
            Map<String, Object> activeUsersAnalysis = operationLogQueryService.getActiveUsersAnalysis(days, limit);
            
            logger.info("获取活跃用户分析成功 - days:{}, limit:{}", days, limit);
            return ApiResponse.success("获取活跃用户分析成功", activeUsersAnalysis);
            
        } catch (Exception e) {
            logger.error("获取活跃用户分析失败 - days:{}, limit:{}, error:{}", 
                        days, limit, e.getMessage(), e);
            return ApiResponse.error("获取活跃用户分析失败: " + e.getMessage());
        }
    }

    /**
     * 操作热力图数据
     * 
     * @param days 统计天数
     * @return 操作热力图数据
     */
    @GetMapping("/operation-heatmap")
    @Operation(summary = "操作热力图数据", description = "获取按时间分布的操作热力图数据")
    public ApiResponse<Map<String, Object>> getOperationHeatmap(
            @Parameter(description = "统计天数", example = "30")
            @RequestParam(value = "days", defaultValue = "30") Integer days) {
        
        try {
            logger.debug("获取操作热力图数据 - days:{}", days);
            
            // 参数验证
            if (days <= 0 || days > 365) {
                return ApiResponse.error("统计天数必须在1-365之间");
            }
            
            // 调用服务层获取操作热力图数据
            Map<String, Object> heatmapData = operationLogQueryService.getOperationHeatmap(days);
            
            logger.info("获取操作热力图数据成功 - days:{}", days);
            return ApiResponse.success("获取操作热力图数据成功", heatmapData);
            
        } catch (Exception e) {
            logger.error("获取操作热力图数据失败 - days:{}, error:{}", days, e.getMessage(), e);
            return ApiResponse.error("获取操作热力图数据失败: " + e.getMessage());
        }
    }

    /**
     * 健康检查接口
     * 
     * @return 健康状态
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "操作日志模块健康检查接口")
    public ApiResponse<String> health() {
        try {
            logger.debug("操作日志模块健康检查");
            return ApiResponse.success("操作日志模块运行正常", "OperationLog Service is UP");
        } catch (Exception e) {
            logger.error("操作日志模块健康检查失败: {}", e.getMessage(), e);
            return ApiResponse.error("操作日志模块异常: " + e.getMessage());
        }
    }
}