<template>
  <div class="sys-monitor-alerts">
    <!-- 告警统计概览 -->
    <div class="alert-overview">
      <el-row :gutter="16">
        <el-col :span="6" v-for="stat in alertStats" :key="stat.type">
          <el-card class="stat-card" :class="stat.type">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon :size="24" :color="stat.color">
                  <component :is="stat.icon" />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 告警管理面板 -->
    <el-card class="alert-management">
      <template #header>
        <div class="card-header">
          <span>告警管理</span>
          <div class="header-actions">
            <el-button-group>
              <el-button 
                :type="activeTab === 'logs' ? 'primary' : ''"
                @click="activeTab = 'logs'"
                size="small">
                告警记录
              </el-button>
              <el-button 
                :type="activeTab === 'configs' ? 'primary' : ''"
                @click="activeTab = 'configs'"
                size="small">
                告警配置
              </el-button>
            </el-button-group>
            <el-button @click="refreshData" :loading="loading" size="small">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 告警记录 -->
      <div v-if="activeTab === 'logs'" class="alert-logs">
        <!-- 过滤器 -->
        <div class="filters">
          <el-form :model="logFilters" inline>
            <el-form-item label="告警级别">
              <el-select v-model="logFilters.level" placeholder="全部" clearable style="width: 120px;">
                <el-option label="紧急" value="EMERGENCY" />
                <el-option label="严重" value="CRITICAL" />
                <el-option label="警告" value="WARNING" />
                <el-option label="信息" value="INFO" />
              </el-select>
            </el-form-item>
            <el-form-item label="告警状态">
              <el-select v-model="logFilters.status" placeholder="全部" clearable style="width: 120px;">
                <el-option label="活跃" value="ACTIVE" />
                <el-option label="已解决" value="RESOLVED" />
                <el-option label="已忽略" value="IGNORED" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button @click="loadAlertLogs" type="primary">查询</el-button>
              <el-button @click="resetFilters">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 告警列表 -->
        <el-table 
          :data="alertLogs" 
          v-loading="logsLoading"
          empty-text="暂无告警记录"
          class="alert-table">
          <el-table-column label="告警时间" width="180">
            <template #default="{ row }">
              <div class="time-column">
                <el-icon><Clock /></el-icon>
                {{ formatTime(row.alertTime) }}
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="告警级别" width="100">
            <template #default="{ row }">
              <el-tag 
                :type="getAlertLevelType(row.alertLevel)"
                size="small"
                effect="dark">
                {{ getAlertLevelText(row.alertLevel) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="指标类型" width="120">
            <template #default="{ row }">
              <el-tag size="small" effect="plain">
                {{ getMetricTypeText(row.metricType) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="告警信息" min-width="200">
            <template #default="{ row }">
              <div class="alert-info">
                <div class="alert-title">{{ row.alertTitle }}</div>
                <div class="alert-message">{{ row.alertMessage }}</div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="指标值" width="120">
            <template #default="{ row }">
              <div class="metric-value">
                <span class="value">{{ row.metricValue }}</span>
                <span class="unit">{{ row.unit }}</span>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag 
                :type="getAlertStatusType(row.alertStatus)"
                size="small">
                {{ getAlertStatusText(row.alertStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button
                v-if="row.alertStatus === 'ACTIVE'"
                @click="resolveAlert(row)"
                type="primary"
                size="small"
                text>
                解决
              </el-button>
              <el-button
                @click="viewAlertDetail(row)"
                type="info"
                size="small"
                text>
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            v-model:current-page="logPagination.page"
            v-model:page-size="logPagination.size"
            :total="logPagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadAlertLogs"
            @current-change="loadAlertLogs"
          />
        </div>
      </div>

      <!-- 告警配置 -->
      <div v-if="activeTab === 'configs'" class="alert-configs">
        <el-table 
          :data="alertConfigs" 
          v-loading="configsLoading"
          empty-text="暂无告警配置"
          class="config-table">
          <el-table-column label="指标类型" width="120">
            <template #default="{ row }">
              <el-tag size="small" effect="plain">
                {{ getMetricTypeText(row.metricType) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="指标名称" prop="metricDisplayName" min-width="150" />
          
          <el-table-column label="阈值配置" width="200">
            <template #default="{ row }">
              <div class="threshold-config">
                <div v-if="row.warningThreshold" class="threshold-item warning">
                  <span>警告: {{ row.warningThreshold }}{{ row.unit }}</span>
                </div>
                <div v-if="row.criticalThreshold" class="threshold-item critical">
                  <span>严重: {{ row.criticalThreshold }}{{ row.unit }}</span>
                </div>
                <div v-if="row.emergencyThreshold" class="threshold-item emergency">
                  <span>紧急: {{ row.emergencyThreshold }}{{ row.unit }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="状态" width="80">
            <template #default="{ row }">
              <el-switch
                v-model="row.enabled"
                @change="updateAlertConfig(row)"
                :loading="row.updating"
              />
            </template>
          </el-table-column>
          
          <el-table-column label="通知" width="80">
            <template #default="{ row }">
              <el-switch
                v-model="row.notificationEnabled"
                @change="updateAlertConfig(row)"
                :loading="row.updating"
              />
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button
                @click="editAlertConfig(row)"
                type="primary"
                size="small"
                text>
                编辑
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 解决告警对话框 -->
    <el-dialog 
      v-model="resolveDialog.visible" 
      title="解决告警" 
      width="500px">
      <el-form :model="resolveDialog.form" label-width="80px">
        <el-form-item label="告警信息">
          <div class="alert-detail">
            <p><strong>{{ resolveDialog.alert?.alertTitle }}</strong></p>
            <p>{{ resolveDialog.alert?.alertMessage }}</p>
          </div>
        </el-form-item>
        <el-form-item label="解决备注" required>
          <el-input
            v-model="resolveDialog.form.resolveNote"
            type="textarea"
            :rows="3"
            placeholder="请输入解决情况说明..."
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="resolveDialog.visible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="confirmResolveAlert"
          :loading="resolveDialog.loading">
          确认解决
        </el-button>
      </template>
    </el-dialog>

    <!-- 编辑告警配置对话框 -->
    <el-dialog 
      v-model="editDialog.visible" 
      title="编辑告警配置" 
      width="600px">
      <el-form :model="editDialog.form" label-width="120px">
        <el-form-item label="指标名称">
          <el-input v-model="editDialog.form.metricDisplayName" />
        </el-form-item>
        
        <el-form-item label="警告阈值">
          <el-input-number 
            v-model="editDialog.form.warningThreshold" 
            :precision="2"
            style="width: 200px;" />
        </el-form-item>
        
        <el-form-item label="严重阈值">
          <el-input-number 
            v-model="editDialog.form.criticalThreshold" 
            :precision="2"
            style="width: 200px;" />
        </el-form-item>
        
        <el-form-item label="紧急阈值">
          <el-input-number 
            v-model="editDialog.form.emergencyThreshold" 
            :precision="2"
            style="width: 200px;" />
        </el-form-item>
        
        <el-form-item label="启用状态">
          <el-switch v-model="editDialog.form.enabled" />
        </el-form-item>
        
        <el-form-item label="通知状态">
          <el-switch v-model="editDialog.form.notificationEnabled" />
        </el-form-item>
        
        <el-form-item label="描述">
          <el-input 
            v-model="editDialog.form.description" 
            type="textarea" 
            :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editDialog.visible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="confirmEditConfig"
          :loading="editDialog.loading">
          确认保存
        </el-button>
      </template>
    </el-dialog>

    <!-- 告警详情对话框 -->
    <el-dialog 
      v-model="detailDialog.visible" 
      title="告警详情" 
      width="600px">
      <div v-if="detailDialog.alert" class="alert-detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="告警时间">
            {{ formatTime(detailDialog.alert.alertTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="告警级别">
            <el-tag :type="getAlertLevelType(detailDialog.alert.alertLevel)">
              {{ getAlertLevelText(detailDialog.alert.alertLevel) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="指标类型">
            {{ getMetricTypeText(detailDialog.alert.metricType) }}
          </el-descriptions-item>
          <el-descriptions-item label="指标名称">
            {{ detailDialog.alert.metricName }}
          </el-descriptions-item>
          <el-descriptions-item label="指标值">
            {{ detailDialog.alert.metricValue }} {{ detailDialog.alert.unit }}
          </el-descriptions-item>
          <el-descriptions-item label="阈值">
            {{ detailDialog.alert.thresholdValue }} {{ detailDialog.alert.unit }}
          </el-descriptions-item>
          <el-descriptions-item label="告警状态">
            <el-tag :type="getAlertStatusType(detailDialog.alert.alertStatus)">
              {{ getAlertStatusText(detailDialog.alert.alertStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发生次数">
            {{ detailDialog.alert.occurrenceCount }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="alert-message-section">
          <h4>告警信息</h4>
          <p>{{ detailDialog.alert.alertMessage }}</p>
        </div>
        
        <div v-if="detailDialog.alert.alertStatus === 'RESOLVED'" class="resolve-info">
          <h4>解决信息</h4>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="解决时间">
              {{ formatTime(detailDialog.alert.resolvedTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="解决人">
              {{ detailDialog.alert.resolvedBy }}
            </el-descriptions-item>
            <el-descriptions-item label="解决备注">
              {{ detailDialog.alert.resolveNote }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch, markRaw } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  Clock,
  WarningFilled,
  CircleCheckFilled,
  InfoFilled,
  Platform
} from '@element-plus/icons-vue'
import { request } from '@/utils/request'

// 响应式数据
const loading = ref(false)
const logsLoading = ref(false)
const configsLoading = ref(false)
const activeTab = ref('logs')

// 告警统计 - 使用 markRaw 避免图标组件被响应式化
const alertStats = ref([
  { type: 'total', label: '总告警', value: 0, icon: markRaw(Platform), color: '#909399' },
  { type: 'active', label: '活跃告警', value: 0, icon: markRaw(WarningFilled), color: '#E6A23C' },
  { type: 'resolved', label: '已解决', value: 0, icon: markRaw(CircleCheckFilled), color: '#67C23A' },
  { type: 'rate', label: '解决率', value: '0%', icon: markRaw(InfoFilled), color: '#409EFF' }
])

// 告警日志
const alertLogs = ref([])
const logFilters = reactive({
  level: '',
  status: ''
})
const logPagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 告警配置
const alertConfigs = ref([])

// 解决告警对话框
const resolveDialog = reactive({
  visible: false,
  loading: false,
  alert: null,
  form: {
    resolveNote: ''
  }
})

// 编辑配置对话框
const editDialog = reactive({
  visible: false,
  loading: false,
  config: null,
  form: {
    id: null,
    metricType: '',
    metricName: '',
    metricDisplayName: '',
    warningThreshold: null,
    criticalThreshold: null,
    emergencyThreshold: null,
    comparisonOperator: 'GT',
    unit: '',
    enabled: true,
    notificationEnabled: true,
    description: ''
  }
})

// 告警详情对话框
const detailDialog = reactive({
  visible: false,
  alert: null
})

// 方法
const refreshData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadAlertStatistics(),
      activeTab.value === 'logs' ? loadAlertLogs() : loadAlertConfigs()
    ])
  } finally {
    loading.value = false
  }
}

const loadAlertStatistics = async () => {
  try {
    const response = await request.get('/sys-monitor/alert/statistics', {
      params: { timeRange: '24h' }
    })
    
    if (response && response.success) {
      const stats = response.data
      alertStats.value[0].value = stats.totalAlerts
      alertStats.value[1].value = stats.activeAlerts
      alertStats.value[2].value = stats.resolvedAlerts
      alertStats.value[3].value = Math.round(stats.resolveRate) + '%'
    }
  } catch (error) {
    console.error('加载告警统计失败:', error)
  }
}

const loadAlertLogs = async () => {
  console.log('开始加载告警日志...')
  logsLoading.value = true
  try {
    const params = {
      page: logPagination.page,
      size: logPagination.size,
      ...(logFilters.level && { level: logFilters.level }),
      ...(logFilters.status && { status: logFilters.status })
    }
    
    console.log('查询参数:', params)
    const response = await request.get('/sys-monitor/alert/logs', { params })
    console.log('查询响应:', response)
    
    if (response && response.success) {
      alertLogs.value = response.data
      logPagination.total = response.total
      console.log('更新数据成功，记录数:', response.data.length)
    }
  } catch (error) {
    console.error('加载告警日志失败:', error)
    ElMessage.error('加载告警日志失败')
  } finally {
    logsLoading.value = false
  }
}

const loadAlertConfigs = async () => {
  configsLoading.value = true
  try {
    const response = await request.get('/sys-monitor/alert/configs')
    
    if (response && response.success) {
      alertConfigs.value = response.data.map(config => ({
        ...config,
        updating: false
      }))
    }
  } catch (error) {
    console.error('加载告警配置失败:', error)
    ElMessage.error('加载告警配置失败')
  } finally {
    configsLoading.value = false
  }
}

const updateAlertConfig = async (config) => {
  config.updating = true
  try {
    // 过滤掉前端添加的字段，只发送后端需要的数据
    const { updating, ...configData } = config
    const response = await request.put(`/sys-monitor/alert/configs/${config.id}`, configData)
    
    if (response && response.success) {
      // 静默更新成功，不显示提示
    } else {
      ElMessage.error('配置更新失败')
      // 恢复原状态
      config.enabled = !config.enabled
      config.notificationEnabled = !config.notificationEnabled
    }
  } catch (error) {
    console.error('更新告警配置失败:', error)
    ElMessage.error('更新告警配置失败')
    // 恢复原状态
    config.enabled = !config.enabled
    config.notificationEnabled = !config.notificationEnabled
  } finally {
    config.updating = false
  }
}

const resolveAlert = (alert) => {
  resolveDialog.alert = alert
  resolveDialog.form.resolveNote = ''
  resolveDialog.visible = true
}

const confirmResolveAlert = async () => {
  if (!resolveDialog.form.resolveNote.trim()) {
    ElMessage.warning('请输入解决备注')
    return
  }
  
  resolveDialog.loading = true
  try {
    const response = await request.post(
      `/sys-monitor/alert/logs/${resolveDialog.alert.id}/resolve`,
      { resolveNote: resolveDialog.form.resolveNote }
    )
    
    if (response && response.success) {
      ElMessage.success('告警已解决')
      resolveDialog.visible = false
      await loadAlertLogs()
      await loadAlertStatistics()
    }
  } catch (error) {
    console.error('解决告警失败:', error)
    ElMessage.error('解决告警失败')
  } finally {
    resolveDialog.loading = false
  }
}

const viewAlertDetail = (alert) => {
  detailDialog.alert = alert
  detailDialog.visible = true
}

const resetFilters = () => {
  logFilters.level = ''
  logFilters.status = ''
  logPagination.page = 1
  loadAlertLogs()
}

const editAlertConfig = (config) => {
  editDialog.config = config
  editDialog.form = {
    id: config.id,
    metricType: config.metricType,
    metricName: config.metricName,
    metricDisplayName: config.metricDisplayName,
    warningThreshold: config.warningThreshold,
    criticalThreshold: config.criticalThreshold,
    emergencyThreshold: config.emergencyThreshold,
    comparisonOperator: config.comparisonOperator,
    unit: config.unit,
    enabled: config.enabled,
    notificationEnabled: config.notificationEnabled,
    description: config.description
  }
  editDialog.visible = true
}

const confirmEditConfig = async () => {
  editDialog.loading = true
  try {
    const response = await request.put(`/sys-monitor/alert/configs/${editDialog.form.id}`, editDialog.form)
    
    if (response && response.success) {
      ElMessage.success('配置更新成功')
      editDialog.visible = false
      await loadAlertConfigs()
    }
  } catch (error) {
    console.error('更新告警配置失败:', error)
    ElMessage.error('更新告警配置失败')
  } finally {
    editDialog.loading = false
  }
}

// 格式化方法
const formatTime = (time) => {
  if (!time) return '-'
  return new Date(time).toLocaleString('zh-CN')
}

const getAlertLevelType = (level) => {
  const types = {
    'EMERGENCY': 'danger',
    'CRITICAL': 'danger', 
    'WARNING': 'warning',
    'INFO': 'info'
  }
  return types[level] || 'info'
}

const getAlertLevelText = (level) => {
  const texts = {
    'EMERGENCY': '紧急',
    'CRITICAL': '严重',
    'WARNING': '警告',
    'INFO': '信息'
  }
  return texts[level] || level
}

const getAlertStatusType = (status) => {
  const types = {
    'ACTIVE': 'warning',
    'RESOLVED': 'success',
    'IGNORED': 'info'
  }
  return types[status] || 'info'
}

const getAlertStatusText = (status) => {
  const texts = {
    'ACTIVE': '活跃',
    'RESOLVED': '已解决',
    'IGNORED': '已忽略'
  }
  return texts[status] || status
}

const getMetricTypeText = (type) => {
  const texts = {
    'CPU': 'CPU',
    'MEMORY': '内存',
    'DISK': '磁盘',
    'JVM': 'JVM',
    'THREAD': '线程'
  }
  return texts[type] || type
}

// 生命周期
onMounted(() => {
  refreshData()
})

// 监听tab切换
watch(activeTab, (newTab) => {
  if (newTab === 'logs') {
    loadAlertLogs()
  } else {
    loadAlertConfigs()
  }
})
</script>

<style lang="scss" scoped>
.sys-monitor-alerts {
  .alert-overview {
    margin-bottom: 20px;

    .stat-card {
      border-radius: 8px;
      
      &.total {
        border-left: 4px solid #909399;
      }
      
      &.active {
        border-left: 4px solid #E6A23C;
      }
      
      &.resolved {
        border-left: 4px solid #67C23A;
      }
      
      &.rate {
        border-left: 4px solid #409EFF;
      }
      
      :deep(.el-card__body) {
        padding: 16px;
        background: var(--el-bg-color);
      }
    }

    .stat-content {
      display: flex;
      align-items: center;
      gap: 12px;

      .stat-icon {
        flex-shrink: 0;
      }

      .stat-info {
        flex: 1;

        .stat-value {
          font-size: 24px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          line-height: 1;
        }

        .stat-label {
          font-size: 12px;
          color: var(--el-text-color-regular);
          margin-top: 4px;
        }
      }
    }
  }

  .alert-management {
    border-radius: 8px;
    
    :deep(.el-card__header) {
      background: var(--el-fill-color-light);
      border-bottom: 1px solid var(--el-border-color-lighter);
    }
    
    :deep(.el-card__body) {
      background: var(--el-bg-color);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-actions {
        display: flex;
        gap: 12px;
      }
    }
  }

  .filters {
    margin-bottom: 16px;
    padding: 16px;
    background: var(--el-fill-color-light);
    border-radius: 6px;
  }

  .alert-table,
  .config-table {
    .time-column {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 12px;
    }

    .alert-info {
      .alert-title {
        font-weight: 500;
        color: var(--el-text-color-primary);
        margin-bottom: 4px;
      }

      .alert-message {
        font-size: 12px;
        color: var(--el-text-color-regular);
        line-height: 1.4;
      }
    }

    .metric-value {
      .value {
        font-weight: 500;
        color: var(--el-text-color-primary);
      }

      .unit {
        font-size: 12px;
        color: var(--el-text-color-regular);
        margin-left: 2px;
      }
    }

    .threshold-config {
      .threshold-item {
        font-size: 12px;
        margin-bottom: 2px;

        &.warning {
          color: #E6A23C;
        }

        &.critical {
          color: #F56C6C;
        }

        &.emergency {
          color: #F56C6C;
          font-weight: 600;
        }
      }
    }
  }

  .pagination {
    margin-top: 16px;
    display: flex;
    justify-content: center;
  }

  .alert-detail {
    padding: 12px;
    background: var(--el-fill-color-light);
    border-radius: 6px;
    border: 1px solid var(--el-border-color-lighter);

    p {
      margin: 0 0 8px 0;
      line-height: 1.4;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .alert-detail-content {
    .alert-message-section,
    .resolve-info {
      margin-top: 20px;

      h4 {
        margin: 0 0 12px 0;
        color: var(--el-text-color-primary);
        font-size: 14px;
      }

      p {
        margin: 0;
        line-height: 1.5;
        color: var(--el-text-color-regular);
      }
    }
  }
}
</style>