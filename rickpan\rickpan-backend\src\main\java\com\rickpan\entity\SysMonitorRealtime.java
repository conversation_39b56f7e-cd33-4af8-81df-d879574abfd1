package com.rickpan.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 系统实时监控数据实体类
 * 对应数据库表：sys_monitor_realtime
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "sys_monitor_realtime", indexes = {
    @Index(name = "idx_sys_monitor_realtime_created_at", columnList = "created_at"),
    @Index(name = "idx_sys_monitor_realtime_cpu_usage", columnList = "cpu_usage"),
    @Index(name = "idx_sys_monitor_realtime_memory_usage", columnList = "memory_usage")
})
@EntityListeners(AuditingEntityListener.class)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysMonitorRealtime {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // ==================== CPU相关字段 ====================
    
    /**
     * CPU使用率(%)
     */
    @Column(name = "cpu_usage", precision = 5, scale = 2)
    private BigDecimal cpuUsage;

    /**
     * CPU核心数
     */
    @Column(name = "cpu_cores")
    private Integer cpuCores;

    /**
     * 1分钟平均负载
     */
    @Column(name = "cpu_load_1min", precision = 5, scale = 2)
    private BigDecimal cpuLoad1min;

    /**
     * 5分钟平均负载
     */
    @Column(name = "cpu_load_5min", precision = 5, scale = 2)
    private BigDecimal cpuLoad5min;

    /**
     * 15分钟平均负载
     */
    @Column(name = "cpu_load_15min", precision = 5, scale = 2)
    private BigDecimal cpuLoad15min;

    // ==================== 系统内存相关字段 ====================
    
    /**
     * 系统总内存(字节)
     */
    @Column(name = "memory_total")
    private Long memoryTotal;

    /**
     * 系统已用内存(字节)
     */
    @Column(name = "memory_used")
    private Long memoryUsed;

    /**
     * 系统空闲内存(字节)
     */
    @Column(name = "memory_free")
    private Long memoryFree;

    /**
     * 系统内存使用率(%)
     */
    @Column(name = "memory_usage", precision = 5, scale = 2)
    private BigDecimal memoryUsage;

    // ==================== 磁盘相关字段 ====================
    
    /**
     * 磁盘总空间(字节)
     */
    @Column(name = "disk_total")
    private Long diskTotal;

    /**
     * 磁盘已用空间(字节)
     */
    @Column(name = "disk_used")
    private Long diskUsed;

    /**
     * 磁盘可用空间(字节)
     */
    @Column(name = "disk_available")
    private Long diskAvailable;

    /**
     * 磁盘使用率(%)
     */
    @Column(name = "disk_usage", precision = 5, scale = 2)
    private BigDecimal diskUsage;

    // ==================== JVM内存相关字段 ====================
    
    /**
     * JVM堆内存已用(字节)
     */
    @Column(name = "jvm_heap_used")
    private Long jvmHeapUsed;

    /**
     * JVM堆内存最大(字节)
     */
    @Column(name = "jvm_heap_max")
    private Long jvmHeapMax;

    /**
     * JVM堆内存已分配(字节)
     */
    @Column(name = "jvm_heap_committed")
    private Long jvmHeapCommitted;

    /**
     * JVM堆内存使用率(%)
     */
    @Column(name = "jvm_heap_usage", precision = 5, scale = 2)
    private BigDecimal jvmHeapUsage;

    /**
     * JVM非堆内存已用(字节)
     */
    @Column(name = "jvm_non_heap_used")
    private Long jvmNonHeapUsed;

    /**
     * JVM非堆内存最大(字节)
     */
    @Column(name = "jvm_non_heap_max")
    private Long jvmNonHeapMax;

    /**
     * JVM非堆内存已分配(字节)
     */
    @Column(name = "jvm_non_heap_committed")
    private Long jvmNonHeapCommitted;

    // ==================== 垃圾回收相关字段 ====================
    
    /**
     * GC总次数
     */
    @Column(name = "gc_count")
    private Long gcCount;

    /**
     * GC总耗时(毫秒)
     */
    @Column(name = "gc_time")
    private Long gcTime;

    /**
     * GC平均耗时(毫秒)
     */
    @Column(name = "gc_avg_time", precision = 8, scale = 2)
    private BigDecimal gcAvgTime;

    /**
     * GC最大耗时(毫秒)
     */
    @Column(name = "gc_max_time")
    private Long gcMaxTime;

    // ==================== 线程相关字段 ====================
    
    /**
     * 活跃线程数
     */
    @Column(name = "thread_count")
    private Integer threadCount;

    /**
     * 峰值线程数
     */
    @Column(name = "thread_peak_count")
    private Integer threadPeakCount;

    /**
     * 守护线程数
     */
    @Column(name = "thread_daemon_count")
    private Integer threadDaemonCount;

    /**
     * 总启动线程数
     */
    @Column(name = "thread_total_started")
    private Long threadTotalStarted;

    // ==================== 系统信息字段 ====================
    
    /**
     * 系统运行时间(毫秒)
     */
    @Column(name = "system_uptime")
    private Long systemUptime;

    /**
     * JVM运行时间(毫秒)
     */
    @Column(name = "jvm_uptime")
    private Long jvmUptime;

    // ==================== 时间戳字段 ====================
    
    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
}