package com.rickpan.repository;

import com.rickpan.entity.SysMonitorRealtime;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 实时监控数据Repository单元测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@DataJpaTest
@SpringJUnitConfig
class SysMonitorRealtimeRepositoryTest {

    @Resource
    private TestEntityManager entityManager;

    @Resource
    private SysMonitorRealtimeRepository realtimeRepository;

    @Test
    void testSaveAndFindRealtime() {
        // 创建测试数据
        SysMonitorRealtime realtime = createTestRealtime();

        // 保存数据
        SysMonitorRealtime saved = realtimeRepository.save(realtime);

        // 验证保存结果
        assertNotNull(saved.getId());
        assertNotNull(saved.getCreatedAt());

        // 查询验证
        Optional<SysMonitorRealtime> found = realtimeRepository.findById(saved.getId());
        assertTrue(found.isPresent());
        assertEquals(saved.getCpuUsage(), found.get().getCpuUsage());
        assertEquals(saved.getMemoryUsage(), found.get().getMemoryUsage());
    }

    @Test
    void testDeleteAll() {
        // 创建多条测试数据
        realtimeRepository.save(createTestRealtime());
        realtimeRepository.save(createTestRealtime());

        // 确认数据已保存
        List<SysMonitorRealtime> beforeDelete = realtimeRepository.findAll();
        assertTrue(beforeDelete.size() >= 2);

        // 删除所有数据
        realtimeRepository.deleteAll();

        // 验证删除结果
        List<SysMonitorRealtime> afterDelete = realtimeRepository.findAll();
        assertTrue(afterDelete.isEmpty());
    }

    @Test
    void testFindLatest() {
        // 创建多条测试数据（不同时间）
        SysMonitorRealtime older = createTestRealtime();
        older.setCreatedAt(LocalDateTime.now().minusMinutes(10));
        entityManager.persistAndFlush(older);

        SysMonitorRealtime newer = createTestRealtime();
        newer.setCpuUsage(BigDecimal.valueOf(85.0));
        newer.setCreatedAt(LocalDateTime.now().minusMinutes(5));
        entityManager.persistAndFlush(newer);

        // 查询最新记录
        Optional<SysMonitorRealtime> latest = realtimeRepository.findLatest();

        // 验证结果
        assertTrue(latest.isPresent());
        assertEquals(BigDecimal.valueOf(85.0), latest.get().getCpuUsage());
    }

    @Test
    void testFindLatest_NoData() {
        // 清空数据
        realtimeRepository.deleteAll();

        // 查询最新记录
        Optional<SysMonitorRealtime> latest = realtimeRepository.findLatest();

        // 验证结果
        assertFalse(latest.isPresent());
    }

    @Test
    void testCountRecords() {
        // 初始状态
        long initialCount = realtimeRepository.count();

        // 添加测试数据
        realtimeRepository.save(createTestRealtime());
        realtimeRepository.save(createTestRealtime());

        // 验证计数
        long finalCount = realtimeRepository.count();
        assertEquals(initialCount + 2, finalCount);
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 创建测试用的实时监控数据
     */
    private SysMonitorRealtime createTestRealtime() {
        return SysMonitorRealtime.builder()
                .cpuUsage(BigDecimal.valueOf(65.5))
                .cpuCores(8)
                .memoryTotal(16777216000L)
                .memoryUsed(12079595520L)
                .memoryFree(4697620480L)
                .memoryUsage(BigDecimal.valueOf(72.0))
                .diskTotal(1000204886016L)
                .diskUsed(550000000000L)
                .diskAvailable(450204886016L)
                .diskUsage(BigDecimal.valueOf(55.0))
                .jvmHeapUsed(536870912L)
                .jvmHeapMax(1073741824L)
                .jvmHeapCommitted(1073741824L)
                .jvmHeapUsage(BigDecimal.valueOf(50.0))
                .jvmNonHeapUsed(134217728L)
                .jvmNonHeapMax(-1L)
                .jvmNonHeapCommitted(134217728L)
                .gcCount(100L)
                .gcTime(5000L)
                .gcAvgTime(BigDecimal.valueOf(50.0))
                .gcMaxTime(200L)
                .threadCount(50)
                .threadPeakCount(75)
                .threadDaemonCount(25)
                .threadTotalStarted(1000L)
                .systemUptime(86400000L)
                .jvmUptime(3600000L)
                .createdAt(LocalDateTime.now())
                .build();
    }
}