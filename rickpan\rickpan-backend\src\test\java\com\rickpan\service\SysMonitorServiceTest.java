package com.rickpan.service;

import com.rickpan.dto.SysMonitorDataDTO;
import com.rickpan.dto.SysMonitorQueryDTO;
import com.rickpan.entity.SysMonitorHistory;
import com.rickpan.entity.SysMonitorRealtime;
import com.rickpan.entity.SysMonitorSystemInfo;
import com.rickpan.repository.SysMonitorHistoryRepository;
import com.rickpan.repository.SysMonitorRealtimeRepository;
import com.rickpan.repository.SysMonitorSystemInfoRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 系统监控服务单元测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class SysMonitorServiceTest {

    @Mock
    private SysMonitorRealtimeRepository realtimeRepository;

    @Mock
    private SysMonitorHistoryRepository historyRepository;

    @Mock
    private SysMonitorSystemInfoRepository systemInfoRepository;

    @InjectMocks
    private SysMonitorService sysMonitorService;

    private SysMonitorDataDTO mockMonitorData;

    @BeforeEach
    void setUp() {
        mockMonitorData = createMockMonitorData();
    }

    @Test
    void testGetRealtimeData() {
        // 执行测试
        SysMonitorDataDTO result = sysMonitorService.getRealtimeData();

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getTimestamp());
        assertNotNull(result.getCpu());
        assertNotNull(result.getMemory());
        assertNotNull(result.getDisk());
        assertNotNull(result.getJvm());
        assertNotNull(result.getGc());
        assertNotNull(result.getThreads());
        assertNotNull(result.getSystem());

        // 验证CPU信息
        assertNotNull(result.getCpu().getUsage());
        assertTrue(result.getCpu().getCores() > 0);
        assertNotNull(result.getCpu().getLoadAverage());
        assertEquals(3, result.getCpu().getLoadAverage().length);

        // 验证系统信息
        assertEquals("RickPan", result.getSystem().getAppName());
        assertEquals("2.1.0", result.getSystem().getAppVersion());
    }

    @Test
    void testGetHistoryData() {
        // 准备测试数据
        List<SysMonitorHistory> mockHistoryList = createMockHistoryList();
        Page<SysMonitorHistory> mockPage = new PageImpl<>(mockHistoryList);
        
        SysMonitorQueryDTO queryDTO = new SysMonitorQueryDTO();
        queryDTO.setTimeRange("1h");
        queryDTO.setPage(1);
        queryDTO.setSize(20);
        queryDTO.setSort("desc");

        // 模拟Repository行为
        when(historyRepository.findByRecordTimeBetweenOrderByRecordTimeAsc(
                any(LocalDateTime.class), any(LocalDateTime.class), any(Pageable.class)))
                .thenReturn(mockPage);

        // 执行测试
        Page<SysMonitorHistory> result = sysMonitorService.getHistoryData(queryDTO);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockHistoryList.size(), result.getContent().size());
        
        // 验证Repository调用
        verify(historyRepository).findByRecordTimeBetweenOrderByRecordTimeAsc(
                any(LocalDateTime.class), any(LocalDateTime.class), any(Pageable.class));
    }

    @Test
    void testGetMonitorStatistics() {
        // 准备测试数据
        Object[] mockStats = {
            BigDecimal.valueOf(65.5),  // avgCpu
            BigDecimal.valueOf(80.2),  // maxCpu
            BigDecimal.valueOf(45.1),  // minCpu
            BigDecimal.valueOf(72.8),  // avgMemory
            BigDecimal.valueOf(85.0),  // maxMemory
            BigDecimal.valueOf(60.5),  // minMemory
            BigDecimal.valueOf(55.3),  // avgDisk
            BigDecimal.valueOf(70.0),  // maxDisk
            BigDecimal.valueOf(40.0),  // minDisk
            BigDecimal.valueOf(68.9),  // avgJvm
            BigDecimal.valueOf(75.0),  // maxJvm
            BigDecimal.valueOf(60.0)   // minJvm
        };

        // 模拟Repository行为
        when(historyRepository.getStatisticsByRecordTimeBetween(
                any(LocalDateTime.class), any(LocalDateTime.class)))
                .thenReturn(mockStats);

        // 执行测试
        Object result = sysMonitorService.getMonitorStatistics("1h");

        // 验证结果
        assertNotNull(result);
        assertTrue(result instanceof SysMonitorDataDTO);
        
        SysMonitorDataDTO statsData = (SysMonitorDataDTO) result;
        assertEquals(BigDecimal.valueOf(65.5), statsData.getCpu().getUsage());
        assertEquals(BigDecimal.valueOf(72.8), statsData.getMemory().getUsage());
        
        // 验证Repository调用
        verify(historyRepository).getStatisticsByRecordTimeBetween(
                any(LocalDateTime.class), any(LocalDateTime.class));
    }

    @Test
    void testSaveRealtimeData() {
        // 模拟Repository行为
        when(realtimeRepository.save(any(SysMonitorRealtime.class)))
                .thenAnswer(invocation -> invocation.getArgument(0));

        // 执行测试
        assertDoesNotThrow(() -> sysMonitorService.saveRealtimeData(mockMonitorData));

        // 验证Repository调用
        verify(realtimeRepository).deleteAllRealtimeData();
        verify(realtimeRepository).save(any(SysMonitorRealtime.class));
    }

    @Test
    void testSaveHistoryData() {
        // 模拟Repository行为
        when(historyRepository.save(any(SysMonitorHistory.class)))
                .thenAnswer(invocation -> invocation.getArgument(0));

        // 执行测试
        assertDoesNotThrow(() -> sysMonitorService.saveHistoryData(mockMonitorData));

        // 验证Repository调用
        verify(historyRepository).save(any(SysMonitorHistory.class));
    }

    @Test
    void testUpdateSystemInfo_NewRecord() {
        // 模拟Repository行为 - 没有现有记录
        when(systemInfoRepository.findLatest()).thenReturn(Optional.empty());
        when(systemInfoRepository.save(any(SysMonitorSystemInfo.class)))
                .thenAnswer(invocation -> invocation.getArgument(0));

        // 执行测试
        assertDoesNotThrow(() -> sysMonitorService.updateSystemInfo(mockMonitorData));

        // 验证Repository调用
        verify(systemInfoRepository).findLatest();
        verify(systemInfoRepository).save(any(SysMonitorSystemInfo.class));
    }

    @Test
    void testUpdateSystemInfo_ExistingRecord() {
        // 准备现有系统信息记录
        SysMonitorSystemInfo existingInfo = SysMonitorSystemInfo.builder()
                .id(1L)
                .osName("Windows 10")
                .appName("RickPan")
                .appVersion("2.0.0")
                .build();

        // 模拟Repository行为 - 有现有记录
        when(systemInfoRepository.findLatest()).thenReturn(Optional.of(existingInfo));
        when(systemInfoRepository.save(any(SysMonitorSystemInfo.class)))
                .thenAnswer(invocation -> invocation.getArgument(0));

        // 执行测试
        assertDoesNotThrow(() -> sysMonitorService.updateSystemInfo(mockMonitorData));

        // 验证Repository调用
        verify(systemInfoRepository).findLatest();
        verify(systemInfoRepository).save(any(SysMonitorSystemInfo.class));
    }

    @Test
    void testSaveRealtimeData_Exception() {
        // 模拟Repository抛出异常
        doThrow(new RuntimeException("Database error")).when(realtimeRepository).deleteAllRealtimeData();

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, 
                () -> sysMonitorService.saveRealtimeData(mockMonitorData));
        
        assertTrue(exception.getMessage().contains("保存实时监控数据失败"));
    }

    @Test
    void testSaveHistoryData_Exception() {
        // 模拟Repository抛出异常
        when(historyRepository.save(any(SysMonitorHistory.class)))
                .thenThrow(new RuntimeException("Database error"));

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, 
                () -> sysMonitorService.saveHistoryData(mockMonitorData));
        
        assertTrue(exception.getMessage().contains("保存历史监控数据失败"));
    }

    @Test
    void testGetHistoryData_Exception() {
        // 模拟Repository抛出异常
        when(historyRepository.findByRecordTimeBetweenOrderByRecordTimeAsc(
                any(LocalDateTime.class), any(LocalDateTime.class), any(Pageable.class)))
                .thenThrow(new RuntimeException("Database error"));

        SysMonitorQueryDTO queryDTO = new SysMonitorQueryDTO();
        queryDTO.setTimeRange("1h");
        queryDTO.setPage(1);
        queryDTO.setSize(20);

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, 
                () -> sysMonitorService.getHistoryData(queryDTO));
        
        assertTrue(exception.getMessage().contains("查询历史监控数据失败"));
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 创建模拟监控数据
     */
    private SysMonitorDataDTO createMockMonitorData() {
        return SysMonitorDataDTO.builder()
                .timestamp(LocalDateTime.now())
                .cpu(SysMonitorDataDTO.CpuInfo.builder()
                        .usage(BigDecimal.valueOf(65.5))
                        .cores(8)
                        .loadAverage(new BigDecimal[]{
                                BigDecimal.valueOf(1.2), 
                                BigDecimal.valueOf(1.1), 
                                BigDecimal.valueOf(1.0)
                        })
                        .processUsage(BigDecimal.valueOf(25.3))
                        .build())
                .memory(SysMonitorDataDTO.MemoryInfo.builder()
                        .total(16777216000L)  // 16GB
                        .used(12079595520L)   // ~11.2GB
                        .free(4697620480L)    // ~4.4GB
                        .available(4697620480L)
                        .usage(BigDecimal.valueOf(72.0))
                        .build())
                .disk(SysMonitorDataDTO.DiskInfo.builder()
                        .total(1000204886016L)  // ~1TB
                        .used(550000000000L)    // ~550GB
                        .available(450204886016L)
                        .usage(BigDecimal.valueOf(55.0))
                        .build())
                .jvm(SysMonitorDataDTO.JvmInfo.builder()
                        .heapUsed(536870912L)    // 512MB
                        .heapMax(1073741824L)    // 1GB
                        .heapCommitted(1073741824L)
                        .heapUsage(BigDecimal.valueOf(50.0))
                        .nonHeapUsed(134217728L) // 128MB
                        .nonHeapMax(-1L)
                        .nonHeapCommitted(134217728L)
                        .nonHeapUsage(BigDecimal.valueOf(0.0))
                        .uptime(3600000L)        // 1小时
                        .build())
                .gc(SysMonitorDataDTO.GcInfo.builder()
                        .count(100L)
                        .time(5000L)
                        .avgTime(BigDecimal.valueOf(50.0))
                        .maxTime(200L)
                        .collectors(new SysMonitorDataDTO.GcCollectorInfo[]{
                                SysMonitorDataDTO.GcCollectorInfo.builder()
                                        .name("G1 Young Generation")
                                        .collectionCount(80L)
                                        .collectionTime(3000L)
                                        .avgCollectionTime(BigDecimal.valueOf(37.5))
                                        .build(),
                                SysMonitorDataDTO.GcCollectorInfo.builder()
                                        .name("G1 Old Generation")
                                        .collectionCount(20L)
                                        .collectionTime(2000L)
                                        .avgCollectionTime(BigDecimal.valueOf(100.0))
                                        .build()
                        })
                        .build())
                .threads(SysMonitorDataDTO.ThreadInfo.builder()
                        .active(50)
                        .peak(75)
                        .daemon(25)
                        .totalStarted(1000L)
                        .user(25)
                        .deadlocked(0)
                        .build())
                .system(SysMonitorDataDTO.SystemInfo.builder()
                        .osName("Windows 10")
                        .osVersion("10.0")
                        .osArch("amd64")
                        .javaVersion("17.0.2")
                        .javaVendor("Eclipse Adoptium")
                        .jvmName("OpenJDK 64-Bit Server VM")
                        .jvmVersion("17.0.2+8")
                        .appName("RickPan")
                        .appVersion("2.1.0")
                        .appStartTime(LocalDateTime.now().minusHours(1))
                        .systemUptime(86400000L)  // 1天
                        .jvmUptime(3600000L)      // 1小时
                        .build())
                .build();
    }

    /**
     * 创建模拟历史数据列表
     */
    private List<SysMonitorHistory> createMockHistoryList() {
        List<SysMonitorHistory> historyList = new ArrayList<>();
        
        for (int i = 0; i < 5; i++) {
            SysMonitorHistory history = SysMonitorHistory.builder()
                    .id((long) (i + 1))
                    .recordTime(LocalDateTime.now().minusMinutes(i * 5))
                    .cpuUsage(BigDecimal.valueOf(60 + i * 2))
                    .memoryUsage(BigDecimal.valueOf(70 + i))
                    .diskUsage(BigDecimal.valueOf(50 + i))
                    .jvmHeapUsage(BigDecimal.valueOf(45 + i * 3))
                    .memoryTotal(16777216000L)
                    .memoryUsed(12079595520L + i * 100000000L)
                    .diskTotal(1000204886016L)
                    .diskUsed(550000000000L + i * 1000000000L)
                    .jvmHeapUsed(536870912L + i * 10000000L)
                    .jvmHeapMax(1073741824L)
                    .gcCount(100L + i * 5)
                    .gcTime(5000L + i * 100)
                    .threadCount(50 + i)
                    .cpuCores(8)
                    .build();
            historyList.add(history);
        }
        
        return historyList;
    }
}