<template>
  <el-drawer
    v-model="visible"
    title="用户详情"
    :size="600"
    :before-close="handleClose"
    direction="rtl"
  >
    <div v-if="user" class="user-detail">
      <!-- 用户基本信息 -->
      <el-card shadow="never" class="detail-card">
        <template #header>
          <div class="card-header">
            <el-icon><UserFilled /></el-icon>
            <span>基本信息</span>
          </div>
        </template>

        <div class="user-info-section">
          <div class="user-avatar-section">
            <el-avatar 
              :size="80" 
              :src="user.avatarUrl" 
              :icon="UserFilled"
            >
              {{ user.username.charAt(0).toUpperCase() }}
            </el-avatar>
            <div v-if="user.onlineStatus" class="online-status">
              <el-tag 
                :type="getOnlineStatusTagType(user.onlineStatus.status)"
                size="small"
              >
                {{ getOnlineStatusText(user.onlineStatus.status) }}
              </el-tag>
            </div>
          </div>

          <div class="user-details-section">
            <el-descriptions :column="1" border>
              <el-descriptions-item label="用户ID">
                {{ user.id }}
              </el-descriptions-item>
              <el-descriptions-item label="用户名">
                {{ user.username }}
              </el-descriptions-item>
              <el-descriptions-item label="邮箱">
                {{ user.email }}
              </el-descriptions-item>
              <el-descriptions-item label="真实姓名">
                {{ user.realName || '未设置' }}
              </el-descriptions-item>
              <el-descriptions-item label="用户类型">
                <el-tag :type="getUserTypeTagType(user.userType)">
                  {{ getUserTypeText(user.userType) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="用户状态">
                <el-tag :type="getUserStatusTagType(user.status)">
                  {{ getUserStatusText(user.status) }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
      </el-card>

      <!-- 存储信息 -->
      <el-card shadow="never" class="detail-card">
        <template #header>
          <div class="card-header">
            <el-icon><FolderOpened /></el-icon>
            <span>存储信息</span>
          </div>
        </template>

        <div class="storage-section">
          <el-descriptions :column="1" border>
            <el-descriptions-item label="存储配额">
              {{ formatFileSize(user.storageQuota) }}
            </el-descriptions-item>
            <el-descriptions-item label="已使用">
              {{ formatFileSize(user.storageUsed) }}
            </el-descriptions-item>
            <el-descriptions-item label="使用率">
              <div class="storage-usage">
                <el-progress 
                  :percentage="user.storageUsagePercentage" 
                  :stroke-width="8"
                  :color="getStorageProgressColor(user.storageUsagePercentage)"
                />
                <span class="usage-text">{{ user.storageUsagePercentage }}%</span>
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-card>

      <!-- 时间信息 -->
      <el-card shadow="never" class="detail-card">
        <template #header>
          <div class="card-header">
            <el-icon><Clock /></el-icon>
            <span>时间信息</span>
          </div>
        </template>

        <el-descriptions :column="1" border>
          <el-descriptions-item label="注册时间">
            {{ formatDateTime(user.createdAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDateTime(user.updatedAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="最后登录">
            {{ user.lastLoginAt ? formatDateTime(user.lastLoginAt) : '从未登录' }}
          </el-descriptions-item>
          <el-descriptions-item v-if="user.userType === 'VIP'" label="VIP到期时间">
            {{ user.vipExpireTime ? formatDateTime(user.vipExpireTime) : '永久VIP' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button type="primary" @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { UserFilled, FolderOpened, Clock } from '@element-plus/icons-vue'
import type { 
  UserMgmtUserWithStatus, 
  UserType, 
  UserStatus, 
  OnlineStatus 
} from '@/types/userMgmt'

// ========== Props & Emits ==========
interface Props {
  visible: boolean
  user: UserMgmtUserWithStatus | null
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'refresh'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// ========== 计算属性 ==========
const visible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
})

// ========== 工具方法 ==========

/**
 * 格式化文件大小
 */
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 格式化日期时间
 */
const formatDateTime = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

/**
 * 获取用户类型标签类型
 */
const getUserTypeTagType = (userType: UserType): string => {
  const typeMap = {
    'BASIC': 'info',
    'VIP': 'warning',
    'ADMIN': 'danger'
  }
  return typeMap[userType] || 'info'
}

/**
 * 获取用户类型文本
 */
const getUserTypeText = (userType: UserType): string => {
  const textMap = {
    'BASIC': '普通用户',
    'VIP': 'VIP用户',
    'ADMIN': '管理员'
  }
  return textMap[userType] || '未知'
}

/**
 * 获取用户状态标签类型
 */
const getUserStatusTagType = (status: UserStatus): string => {
  const typeMap = {
    'ACTIVE': 'success',
    'DISABLED': 'danger'
  }
  return typeMap[status] || ''
}

/**
 * 获取用户状态文本
 */
const getUserStatusText = (status: UserStatus): string => {
  const textMap = {
    'ACTIVE': '正常',
    'DISABLED': '禁用'
  }
  return textMap[status] || '未知'
}

/**
 * 获取在线状态标签类型
 */
const getOnlineStatusTagType = (status: OnlineStatus): string => {
  const typeMap = {
    'ONLINE': 'success',
    'OFFLINE': 'info',
    'AWAY': 'warning',
    'BUSY': 'danger'
  }
  return typeMap[status] || 'info'
}

/**
 * 获取在线状态文本
 */
const getOnlineStatusText = (status: OnlineStatus): string => {
  const textMap = {
    'ONLINE': '在线',
    'OFFLINE': '离线',
    'AWAY': '离开',
    'BUSY': '忙碌'
  }
  return textMap[status] || '未知'
}

/**
 * 获取存储进度条颜色
 */
const getStorageProgressColor = (percentage: number): string => {
  if (percentage < 50) return '#67c23a'
  if (percentage < 80) return '#e6a23c'
  return '#f56c6c'
}

// ========== 事件处理 ==========

/**
 * 处理关闭
 */
const handleClose = () => {
  visible.value = false
}
</script>

<style scoped lang="scss">
.user-detail {
  .detail-card {
    margin-bottom: 20px;

    :deep(.el-card__header) {
      padding: 16px 20px;
      border-bottom: 1px solid var(--el-border-color-light);

      .card-header {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
        color: var(--el-text-color-primary);

        .el-icon {
          font-size: 16px;
          color: var(--el-color-primary);
        }
      }
    }

    :deep(.el-card__body) {
      padding: 20px;
    }
  }

  .user-info-section {
    .user-avatar-section {
      text-align: center;
      margin-bottom: 20px;

      .online-status {
        margin-top: 8px;
      }
    }
  }

  .storage-section {
    .storage-usage {
      display: flex;
      align-items: center;
      gap: 12px;

      .el-progress {
        flex: 1;
      }

      .usage-text {
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }
  }

  .phase-notice {
    margin-top: 20px;
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 描述列表样式优化
:deep(.el-descriptions__label) {
  font-weight: 600;
  color: var(--el-text-color-primary);
  width: 120px;
}

:deep(.el-descriptions__content) {
  color: var(--el-text-color-regular);
}
</style>