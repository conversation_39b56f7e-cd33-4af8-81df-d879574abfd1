package com.rickpan.service;

import com.rickpan.entity.SysMonitorAlertLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 邮件服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class EmailService {

    private final JavaMailSender mailSender;

    @Value("${spring.mail.username}")
    private String fromEmail;

    @Value("${app.name:<PERSON><PERSON><PERSON>}")
    private String appName;

    /**
     * 发送密码重置验证码邮件
     */
    public void sendPasswordResetCode(String toEmail, String code) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setFrom(fromEmail);
            helper.setTo(toEmail);
            helper.setSubject(appName + " - 密码重置验证码");

            String htmlContent = buildPasswordResetEmailContent(code);
            helper.setText(htmlContent, true);

            mailSender.send(message);
            log.info("密码重置验证码邮件发送成功: {}", toEmail);

        } catch (MessagingException e) {
            log.error("发送密码重置验证码邮件失败: {}", toEmail, e);
            throw new RuntimeException("邮件发送失败", e);
        }
    }

    /**
     * 构建密码重置邮件内容
     */
    private String buildPasswordResetEmailContent(String code) {
        return String.format("""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>密码重置验证码</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: linear-gradient(135deg, #667eea 0%%, #764ba2 100%%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
                    .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
                    .code-box { background: white; border: 2px dashed #667eea; padding: 20px; text-align: center; margin: 20px 0; border-radius: 8px; }
                    .code { font-size: 32px; font-weight: bold; color: #667eea; letter-spacing: 8px; }
                    .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
                    .footer { text-align: center; margin-top: 30px; color: #666; font-size: 12px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🔐 密码重置验证码</h1>
                        <p>您正在重置 %s 账户密码</p>
                    </div>
                    <div class="content">
                        <p>您好！</p>
                        <p>我们收到了您的密码重置请求。请使用以下验证码来重置您的密码：</p>

                        <div class="code-box">
                            <div class="code">%s</div>
                        </div>

                        <div class="warning">
                            <strong>⚠️ 安全提醒：</strong>
                            <ul>
                                <li>验证码有效期为 <strong>10分钟</strong></li>
                                <li>请勿将验证码告诉他人</li>
                                <li>如果这不是您的操作，请忽略此邮件</li>
                            </ul>
                        </div>

                        <p>如果您没有请求重置密码，请忽略此邮件。您的账户安全不会受到影响。</p>

                        <p>感谢您使用 %s！</p>
                    </div>
                    <div class="footer">
                        <p>此邮件由系统自动发送，请勿回复</p>
                        <p>© 2024 %s. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
            """, appName, code, appName, appName);
    }

    /**
     * 发送简单文本邮件（备用方案）
     */
    public void sendSimplePasswordResetCode(String toEmail, String code) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(toEmail);
            message.setSubject(appName + " - 密码重置验证码");
            message.setText(String.format(
                "您的密码重置验证码是：%s\n\n" +
                "验证码有效期为10分钟，请及时使用。\n" +
                "如果这不是您的操作，请忽略此邮件。\n\n" +
                "感谢您使用 %s！", 
                code, appName
            ));

            mailSender.send(message);
            log.info("简单密码重置验证码邮件发送成功: {}", toEmail);

        } catch (Exception e) {
            log.error("发送简单密码重置验证码邮件失败: {}", toEmail, e);
            throw new RuntimeException("邮件发送失败", e);
        }
    }

    /**
     * 发送告警通知邮件
     * 
     * @param adminEmails 管理员邮箱列表
     * @param alertLog 告警日志信息
     */
    @Async
    public void sendAlertNotification(List<String> adminEmails, SysMonitorAlertLog alertLog) {
        if (adminEmails == null || adminEmails.isEmpty()) {
            log.warn("管理员邮箱列表为空，跳过告警邮件发送");
            return;
        }

        for (String email : adminEmails) {
            try {
                sendSingleAlertEmail(email, alertLog);
                log.info("告警邮件发送成功: {} - {}", email, alertLog.getAlertTitle());
            } catch (Exception e) {
                log.error("告警邮件发送失败: {} - {}", email, alertLog.getAlertTitle(), e);
                // 单个邮件发送失败不影响其他邮件发送
            }
        }
    }

    /**
     * 发送单个告警邮件
     */
    private void sendSingleAlertEmail(String toEmail, SysMonitorAlertLog alertLog) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setFrom(fromEmail);
            helper.setTo(toEmail);
            helper.setSubject(buildAlertEmailSubject(alertLog));

            String htmlContent = buildAlertEmailContent(alertLog);
            helper.setText(htmlContent, true);

            mailSender.send(message);

        } catch (MessagingException e) {
            throw new RuntimeException("告警邮件发送失败", e);
        }
    }

    /**
     * 构建告警邮件主题
     */
    private String buildAlertEmailSubject(SysMonitorAlertLog alertLog) {
        return String.format("%s - %s [%s]", 
            appName, alertLog.getAlertTitle(), alertLog.getAlertLevel());
    }

    /**
     * 构建告警邮件内容
     */
    private String buildAlertEmailContent(SysMonitorAlertLog alertLog) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        
        String levelColor = switch (alertLog.getAlertLevel()) {
            case "EMERGENCY" -> "#dc3545"; // 红色
            case "CRITICAL" -> "#fd7e14";  // 橙色
            case "WARNING" -> "#ffc107";   // 黄色
            default -> "#6c757d";         // 灰色
        };
        
        String levelText = switch (alertLog.getAlertLevel()) {
            case "EMERGENCY" -> "紧急告警";
            case "CRITICAL" -> "严重告警";
            case "WARNING" -> "警告告警";
            default -> "系统告警";
        };

        return String.format("""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>系统监控告警</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: %s; color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
                    .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
                    .alert-box { background: white; border-left: 5px solid %s; padding: 20px; margin: 20px 0; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
                    .metric-info { background: #e9ecef; padding: 15px; border-radius: 5px; margin: 15px 0; }
                    .metric-row { display: flex; justify-content: space-between; padding: 5px 0; border-bottom: 1px solid #dee2e6; }
                    .metric-row:last-child { border-bottom: none; }
                    .metric-label { font-weight: bold; color: #495057; }
                    .metric-value { color: #212529; }
                    .critical-value { color: %s; font-weight: bold; }
                    .timestamp { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 5px; margin: 15px 0; text-align: center; }
                    .footer { text-align: center; margin-top: 30px; color: #666; font-size: 12px; }
                    .action-required { background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin: 20px 0; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>%s</h1>
                        <p>%s 系统监控告警通知</p>
                    </div>
                    <div class="content">
                        <div class="alert-box">
                            <h2 style="margin-top: 0; color: %s;">%s</h2>
                            <p><strong>告警消息：</strong>%s</p>
                        </div>

                        <div class="metric-info">
                            <h3 style="margin-top: 0;">监控指标详情</h3>
                            <div class="metric-row">
                                <span class="metric-label">指标类型:</span>
                                <span class="metric-value">%s</span>
                            </div>
                            <div class="metric-row">
                                <span class="metric-label">指标名称:</span>
                                <span class="metric-value">%s</span>
                            </div>
                            <div class="metric-row">
                                <span class="metric-label">当前值:</span>
                                <span class="metric-value critical-value">%s %s</span>
                            </div>
                            <div class="metric-row">
                                <span class="metric-label">触发阈值:</span>
                                <span class="metric-value">%s %s</span>
                            </div>
                            <div class="metric-row">
                                <span class="metric-label">告警级别:</span>
                                <span class="metric-value critical-value">%s</span>
                            </div>
                        </div>

                        <div class="timestamp">
                            <strong>告警时间:</strong> %s
                        </div>

                        <div class="action-required">
                            <h4 style="margin-top: 0;">建议处理措施:</h4>
                            <ul>
                                <li>立即检查系统资源使用情况</li>
                                <li>查看应用程序日志排查异常</li>
                                <li>评估是否需要扩容或优化</li>
                                <li>通过系统监控面板查看详细趋势</li>
                            </ul>
                        </div>

                        <p style="margin-top: 30px;">
                            您可以登录 <strong>%s</strong> 系统监控面板查看更多详细信息和历史趋势。
                        </p>
                    </div>
                    <div class="footer">
                        <p>此邮件由系统自动发送，请勿回复</p>
                        <p>© 2024 %s. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
            """, 
            levelColor, levelColor, levelColor,  // CSS颜色
            levelText, appName,                  // header
            levelColor, alertLog.getAlertTitle(), alertLog.getAlertMessage(), // alert-box
            alertLog.getMetricType(), alertLog.getMetricName(), // 指标信息
            alertLog.getMetricValue() != null ? alertLog.getMetricValue().toString() : "N/A",
            alertLog.getUnit() != null ? alertLog.getUnit() : "",
            alertLog.getThresholdValue() != null ? alertLog.getThresholdValue().toString() : "N/A",
            alertLog.getUnit() != null ? alertLog.getUnit() : "",
            alertLog.getAlertLevel(),
            alertLog.getAlertTime() != null ? alertLog.getAlertTime().format(formatter) : "N/A", // 时间
            appName, appName  // footer
        );
    }
}
