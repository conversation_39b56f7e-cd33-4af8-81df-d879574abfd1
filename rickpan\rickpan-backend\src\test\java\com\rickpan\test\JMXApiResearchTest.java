package com.rickpan.test;

import com.sun.management.OperatingSystemMXBean;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.lang.management.*;
import java.util.List;

/**
 * JMX API调研和测试类
 * 用于验证JMX API在当前环境下的可用性和数据准确性
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class JMXApiResearchTest {

    public static void main(String[] args) {
        JMXApiResearchTest test = new JMXApiResearchTest();
        test.runAllTests();
    }

    /**
     * 运行所有JMX API测试
     */
    public void runAllTests() {
        log.info("=== JMX API调研测试开始 ===");
        
        testOperatingSystemMXBean();
        testMemoryMXBean();
        testGarbageCollectorMXBean();
        testThreadMXBean();
        testRuntimeMXBean();
        testFileSystemInfo();
        
        log.info("=== JMX API调研测试完成 ===");
    }

    /**
     * 测试操作系统MXBean - CPU和系统内存信息
     */
    public void testOperatingSystemMXBean() {
        log.info("--- 测试 OperatingSystemMXBean ---");
        
        try {
            // 获取操作系统MXBean
            OperatingSystemMXBean osBean = 
                (OperatingSystemMXBean) ManagementFactory.getOperatingSystemMXBean();
            
            // 基础系统信息
            log.info("操作系统名称: {}", osBean.getName());
            log.info("操作系统版本: {}", osBean.getVersion());
            log.info("系统架构: {}", osBean.getArch());
            
            // CPU信息
            int processors = osBean.getAvailableProcessors();
            log.info("CPU核心数: {}", processors);
            
            // CPU使用率 (需要调用两次，第一次可能返回负值)
            double cpuLoad1 = osBean.getCpuLoad();
            try {
                Thread.sleep(1000); // 等待1秒
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            double cpuLoad2 = osBean.getCpuLoad();
            log.info("系统CPU使用率(第一次): {}", formatPercentage(cpuLoad1));
            log.info("系统CPU使用率(第二次): {}", formatPercentage(cpuLoad2));
            
            // 进程CPU使用率
            double processCpuLoad = osBean.getProcessCpuLoad();
            log.info("进程CPU使用率: {}", formatPercentage(processCpuLoad));
            
            // 系统内存信息
            long totalPhysicalMemory = osBean.getTotalPhysicalMemorySize();
            long freePhysicalMemory = osBean.getFreePhysicalMemorySize();
            long usedPhysicalMemory = totalPhysicalMemory - freePhysicalMemory;
            double memoryUsage = (double) usedPhysicalMemory / totalPhysicalMemory * 100;
            
            log.info("系统总内存: {}", formatBytes(totalPhysicalMemory));
            log.info("系统空闲内存: {}", formatBytes(freePhysicalMemory));
            log.info("系统已用内存: {}", formatBytes(usedPhysicalMemory));
            log.info("系统内存使用率: {:.2f}%", memoryUsage);
            
            // 系统负载平均值
            double systemLoadAverage = osBean.getSystemLoadAverage();
            log.info("系统负载平均值: {}", systemLoadAverage >= 0 ? systemLoadAverage : "不支持");
            
        } catch (Exception e) {
            log.error("测试OperatingSystemMXBean失败", e);
        }
    }

    /**
     * 测试内存MXBean - JVM内存信息
     */
    public void testMemoryMXBean() {
        log.info("--- 测试 MemoryMXBean ---");
        
        try {
            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
            
            // 堆内存使用情况
            MemoryUsage heapMemoryUsage = memoryBean.getHeapMemoryUsage();
            log.info("堆内存 - 初始大小: {}", formatBytes(heapMemoryUsage.getInit()));
            log.info("堆内存 - 已使用: {}", formatBytes(heapMemoryUsage.getUsed()));
            log.info("堆内存 - 已分配: {}", formatBytes(heapMemoryUsage.getCommitted()));
            log.info("堆内存 - 最大值: {}", formatBytes(heapMemoryUsage.getMax()));
            log.info("堆内存使用率: {:.2f}%", 
                (double) heapMemoryUsage.getUsed() / heapMemoryUsage.getMax() * 100);
            
            // 非堆内存使用情况
            MemoryUsage nonHeapMemoryUsage = memoryBean.getNonHeapMemoryUsage();
            log.info("非堆内存 - 初始大小: {}", formatBytes(nonHeapMemoryUsage.getInit()));
            log.info("非堆内存 - 已使用: {}", formatBytes(nonHeapMemoryUsage.getUsed()));
            log.info("非堆内存 - 已分配: {}", formatBytes(nonHeapMemoryUsage.getCommitted()));
            log.info("非堆内存 - 最大值: {}", 
                nonHeapMemoryUsage.getMax() >= 0 ? formatBytes(nonHeapMemoryUsage.getMax()) : "无限制");
            
            // 待回收对象数量
            int pendingObjects = memoryBean.getObjectPendingFinalizationCount();
            log.info("待回收对象数量: {}", pendingObjects);
            
        } catch (Exception e) {
            log.error("测试MemoryMXBean失败", e);
        }
    }

    /**
     * 测试垃圾回收器MXBean - GC信息
     */
    public void testGarbageCollectorMXBean() {
        log.info("--- 测试 GarbageCollectorMXBean ---");
        
        try {
            List<GarbageCollectorMXBean> gcBeans = ManagementFactory.getGarbageCollectorMXBeans();
            
            long totalGcCount = 0;
            long totalGcTime = 0;
            double maxAvgGcTime = 0;
            
            for (GarbageCollectorMXBean gcBean : gcBeans) {
                String gcName = gcBean.getName();
                long gcCount = gcBean.getCollectionCount();
                long gcTime = gcBean.getCollectionTime();
                
                log.info("GC收集器: {}", gcName);
                log.info("  - GC次数: {}", gcCount);
                log.info("  - GC总耗时: {} ms", gcTime);
                
                if (gcCount > 0) {
                    double avgGcTime = (double) gcTime / gcCount;
                    log.info("  - GC平均耗时: {:.2f} ms", avgGcTime);
                    
                    if (avgGcTime > maxAvgGcTime) {
                        maxAvgGcTime = avgGcTime;
                    }
                }
                
                totalGcCount += gcCount;
                totalGcTime += gcTime;
                
                // 内存池信息
                String[] poolNames = gcBean.getMemoryPoolNames();
                log.info("  - 管理的内存池: {}", String.join(", ", poolNames));
            }
            
            log.info("GC汇总统计:");
            log.info("  - 总GC次数: {}", totalGcCount);
            log.info("  - 总GC耗时: {} ms", totalGcTime);
            if (totalGcCount > 0) {
                log.info("  - 平均GC耗时: {:.2f} ms", (double) totalGcTime / totalGcCount);
            }
            log.info("  - 最大平均GC耗时: {:.2f} ms", maxAvgGcTime);
            
        } catch (Exception e) {
            log.error("测试GarbageCollectorMXBean失败", e);
        }
    }

    /**
     * 测试线程MXBean - 线程信息
     */
    public void testThreadMXBean() {
        log.info("--- 测试 ThreadMXBean ---");
        
        try {
            ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
            
            // 线程统计
            int threadCount = threadBean.getThreadCount();
            int peakThreadCount = threadBean.getPeakThreadCount();
            int daemonThreadCount = threadBean.getDaemonThreadCount();
            long totalStartedThreadCount = threadBean.getTotalStartedThreadCount();
            
            log.info("当前线程数: {}", threadCount);
            log.info("峰值线程数: {}", peakThreadCount);
            log.info("守护线程数: {}", daemonThreadCount);
            log.info("总启动线程数: {}", totalStartedThreadCount);
            log.info("用户线程数: {}", threadCount - daemonThreadCount);
            
            // 死锁检测
            long[] deadlockedThreads = threadBean.findDeadlockedThreads();
            log.info("死锁线程数: {}", deadlockedThreads != null ? deadlockedThreads.length : 0);
            
            // CPU时间支持检测
            boolean cpuTimeSupported = threadBean.isCurrentThreadCpuTimeSupported();
            log.info("支持线程CPU时间测量: {}", cpuTimeSupported);
            
            if (cpuTimeSupported) {
                long currentThreadCpuTime = threadBean.getCurrentThreadCpuTime();
                log.info("当前线程CPU时间: {} ns", currentThreadCpuTime);
            }
            
        } catch (Exception e) {
            log.error("测试ThreadMXBean失败", e);
        }
    }

    /**
     * 测试运行时MXBean - JVM运行时信息
     */
    public void testRuntimeMXBean() {
        log.info("--- 测试 RuntimeMXBean ---");
        
        try {
            RuntimeMXBean runtimeBean = ManagementFactory.getRuntimeMXBean();
            
            // JVM信息
            log.info("JVM名称: {}", runtimeBean.getVmName());
            log.info("JVM版本: {}", runtimeBean.getVmVersion());
            log.info("JVM供应商: {}", runtimeBean.getVmVendor());
            
            // 运行时信息
            long startTime = runtimeBean.getStartTime();
            long uptime = runtimeBean.getUptime();
            log.info("JVM启动时间: {}", new java.util.Date(startTime));
            log.info("JVM运行时间: {} ms ({:.2f} 小时)", uptime, uptime / 1000.0 / 3600.0);
            
            // 系统属性
            log.info("Java版本: {}", System.getProperty("java.version"));
            log.info("Java厂商: {}", System.getProperty("java.vendor"));
            log.info("Java安装路径: {}", System.getProperty("java.home"));
            log.info("操作系统: {} {} {}", 
                System.getProperty("os.name"),
                System.getProperty("os.version"),
                System.getProperty("os.arch"));
            
            // JVM参数
            List<String> jvmArgs = runtimeBean.getInputArguments();
            log.info("JVM启动参数数量: {}", jvmArgs.size());
            for (String arg : jvmArgs) {
                log.info("  - {}", arg);
            }
            
        } catch (Exception e) {
            log.error("测试RuntimeMXBean失败", e);
        }
    }

    /**
     * 测试文件系统信息 - 磁盘空间
     */
    public void testFileSystemInfo() {
        log.info("--- 测试文件系统信息 ---");
        
        try {
            // 获取根目录信息
            File[] roots = File.listRoots();
            long totalSpace = 0;
            long freeSpace = 0;
            long usableSpace = 0;
            
            for (File root : roots) {
                long rootTotal = root.getTotalSpace();
                long rootFree = root.getFreeSpace();
                long rootUsable = root.getUsableSpace();
                long rootUsed = rootTotal - rootFree;
                
                log.info("磁盘根目录: {}", root.getPath());
                log.info("  - 总空间: {}", formatBytes(rootTotal));
                log.info("  - 空闲空间: {}", formatBytes(rootFree));
                log.info("  - 可用空间: {}", formatBytes(rootUsable));
                log.info("  - 已用空间: {}", formatBytes(rootUsed));
                if (rootTotal > 0) {
                    log.info("  - 使用率: {:.2f}%", (double) rootUsed / rootTotal * 100);
                }
                
                totalSpace += rootTotal;
                freeSpace += rootFree;
                usableSpace += rootUsable;
            }
            
            // 系统总计
            long totalUsed = totalSpace - freeSpace;
            log.info("系统磁盘总计:");
            log.info("  - 总空间: {}", formatBytes(totalSpace));
            log.info("  - 空闲空间: {}", formatBytes(freeSpace));
            log.info("  - 可用空间: {}", formatBytes(usableSpace));
            log.info("  - 已用空间: {}", formatBytes(totalUsed));
            if (totalSpace > 0) {
                log.info("  - 使用率: {:.2f}%", (double) totalUsed / totalSpace * 100);
            }
            
        } catch (Exception e) {
            log.error("测试文件系统信息失败", e);
        }
    }

    /**
     * 格式化字节数为可读格式
     */
    private String formatBytes(long bytes) {
        if (bytes < 0) return "N/A";
        
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double size = bytes;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.2f %s", size, units[unitIndex]);
    }

    /**
     * 格式化百分比
     */
    private String formatPercentage(double value) {
        if (value < 0) return "N/A";
        return String.format("%.2f%%", value * 100);
    }
}