package com.rickpan.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * 系统基础信息实体类
 * 对应数据库表：sys_monitor_system_info
 * 用于缓存系统基础信息，避免频繁获取
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "sys_monitor_system_info")
@EntityListeners(AuditingEntityListener.class)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysMonitorSystemInfo {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // ==================== 操作系统信息 ====================
    
    /**
     * 操作系统名称
     */
    @Column(name = "os_name", length = 100)
    private String osName;

    /**
     * 操作系统版本
     */
    @Column(name = "os_version", length = 100)
    private String osVersion;

    /**
     * 系统架构
     */
    @Column(name = "os_arch", length = 50)
    private String osArch;

    // ==================== Java信息 ====================
    
    /**
     * Java版本
     */
    @Column(name = "java_version", length = 100)
    private String javaVersion;

    /**
     * Java厂商
     */
    @Column(name = "java_vendor", length = 100)
    private String javaVendor;

    /**
     * Java安装路径
     */
    @Column(name = "java_home", length = 500)
    private String javaHome;

    /**
     * JVM名称
     */
    @Column(name = "jvm_name", length = 100)
    private String jvmName;

    /**
     * JVM版本
     */
    @Column(name = "jvm_version", length = 100)
    private String jvmVersion;

    // ==================== 应用信息 ====================
    
    /**
     * 应用名称
     */
    @Column(name = "app_name", length = 100)
    @Builder.Default
    private String appName = "RickPan";

    /**
     * 应用版本
     */
    @Column(name = "app_version", length = 50)
    private String appVersion;

    /**
     * 应用启动时间
     */
    @Column(name = "app_start_time")
    private LocalDateTime appStartTime;

    // ==================== 硬件信息 ====================
    
    /**
     * 物理内存总量(字节)
     */
    @Column(name = "total_physical_memory")
    private Long totalPhysicalMemory;

    /**
     * 磁盘总空间(字节)
     */
    @Column(name = "total_disk_space")
    private Long totalDiskSpace;

    /**
     * 处理器数量
     */
    @Column(name = "processor_count")
    private Integer processorCount;

    // ==================== 时间字段 ====================
    
    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
}