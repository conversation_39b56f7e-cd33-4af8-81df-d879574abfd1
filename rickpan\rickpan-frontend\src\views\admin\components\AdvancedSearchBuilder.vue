<template>
  <div class="advanced-search-builder">
    <el-card shadow="never" class="search-builder-card">
      <template #header>
        <div class="card-header">
          <h3>高级搜索构建器</h3>
          <div class="header-actions">
            <el-button text @click="resetSearchBuilder">
              <el-icon><RefreshLeft /></el-icon>
              重置
            </el-button>
            <el-button text @click="toggleJsonView">
              <el-icon><View /></el-icon>
              {{ showJsonView ? '隐藏' : '查看' }}JSON
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索条件组 -->
      <div class="search-groups">
        <div 
          v-for="(group, groupIndex) in searchGroups" 
          :key="group.id"
          class="search-group"
          :class="{ 'has-multiple-groups': searchGroups.length > 1 }"
        >
          <!-- 组逻辑操作符 -->
          <div v-if="groupIndex > 0" class="group-operator">
            <el-select 
              v-model="group.operator" 
              size="small"
              style="width: 80px"
            >
              <el-option label="AND" value="AND" />
              <el-option label="OR" value="OR" />
            </el-select>
          </div>

          <!-- 搜索条件列表 -->
          <div class="conditions-container">
            <div class="group-header">
              <span class="group-title">条件组 {{ groupIndex + 1 }}</span>
              <div class="group-actions">
                <el-button 
                  text 
                  size="small" 
                  @click="addCondition(groupIndex)"
                >
                  <el-icon><Plus /></el-icon>
                  添加条件
                </el-button>
                <el-button 
                  v-if="searchGroups.length > 1"
                  text 
                  size="small" 
                  type="danger"
                  @click="removeGroup(groupIndex)"
                >
                  <el-icon><Delete /></el-icon>
                  删除组
                </el-button>
              </div>
            </div>

            <div class="conditions-list">
              <div 
                v-for="(condition, conditionIndex) in group.conditions" 
                :key="condition.id"
                class="search-condition"
              >
                <!-- 条件逻辑操作符 -->
                <div v-if="conditionIndex > 0" class="condition-operator">
                  <el-select 
                    v-model="condition.operator" 
                    size="small"
                    style="width: 80px"
                  >
                    <el-option label="AND" value="AND" />
                    <el-option label="OR" value="OR" />
                  </el-select>
                </div>

                <!-- 搜索字段 -->
                <div class="condition-field">
                  <el-select 
                    v-model="condition.field" 
                    placeholder="选择字段"
                    @change="onFieldChange(condition)"
                  >
                    <el-option-group
                      v-for="group in fieldGroups"
                      :key="group.label"
                      :label="group.label"
                    >
                      <el-option
                        v-for="field in group.options"
                        :key="field.value"
                        :label="field.label"
                        :value="field.value"
                      />
                    </el-option-group>
                  </el-select>
                </div>

                <!-- 比较操作符 -->
                <div class="condition-comparison">
                  <el-select 
                    v-model="condition.comparison" 
                    placeholder="操作符"
                    :disabled="!condition.field"
                  >
                    <el-option
                      v-for="op in getAvailableOperators(condition.field)"
                      :key="op.value"
                      :label="op.label"
                      :value="op.value"
                    />
                  </el-select>
                </div>

                <!-- 搜索值 -->
                <div class="condition-value">
                  <!-- 文本输入 -->
                  <el-input
                    v-if="getFieldType(condition.field) === 'text'"
                    v-model="condition.value"
                    placeholder="输入搜索值"
                  />
                  
                  <!-- 数字输入 -->
                  <el-input-number
                    v-else-if="getFieldType(condition.field) === 'number'"
                    v-model="condition.value"
                    :min="0"
                    style="width: 100%"
                  />
                  
                  <!-- 日期时间选择 -->
                  <el-date-picker
                    v-else-if="getFieldType(condition.field) === 'datetime'"
                    v-model="condition.value"
                    type="datetime"
                    placeholder="选择日期时间"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DDTHH:mm:ss"
                    style="width: 100%"
                  />
                  
                  <!-- 日期范围选择 -->
                  <el-date-picker
                    v-else-if="getFieldType(condition.field) === 'daterange'"
                    v-model="condition.value"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DDTHH:mm:ss"
                    style="width: 100%"
                  />
                  
                  <!-- 枚举选择 -->
                  <el-select
                    v-else-if="getFieldType(condition.field) === 'enum'"
                    v-model="condition.value"
                    placeholder="选择选项"
                    :multiple="condition.comparison === 'IN'"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="option in getEnumOptions(condition.field)"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                  
                  <!-- 多选输入（用于IP地址和URI的多值搜索） -->
                  <el-select
                    v-else-if="getFieldType(condition.field) === 'multitext' || 
                               (condition.comparison === 'IN' && 
                                ['ipAddress', 'requestUri'].includes(condition.field))"
                    v-model="condition.value"
                    placeholder="输入多个值"
                    multiple
                    filterable
                    allow-create
                    style="width: 100%"
                  />
                  
                  <!-- 默认文本输入 -->
                  <el-input
                    v-else
                    v-model="condition.value"
                    placeholder="输入搜索值"
                  />
                </div>

                <!-- 删除条件按钮 -->
                <div class="condition-actions">
                  <el-button 
                    text 
                    type="danger" 
                    size="small"
                    @click="removeCondition(groupIndex, conditionIndex)"
                    :disabled="group.conditions.length === 1 && searchGroups.length === 1"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 添加组按钮 -->
      <div class="add-group-section">
        <el-button @click="addGroup" class="add-group-btn">
          <el-icon><Plus /></el-icon>
          添加条件组
        </el-button>
      </div>

      <!-- JSON视图 -->
      <el-collapse-transition>
        <div v-show="showJsonView" class="json-view">
          <h4>搜索条件JSON:</h4>
          <el-scrollbar height="200px">
            <pre class="json-content">{{ JSON.stringify(searchQuery, null, 2) }}</pre>
          </el-scrollbar>
        </div>
      </el-collapse-transition>

      <!-- 搜索动作 -->
      <div class="search-actions">
        <el-button type="primary" @click="executeSearch" :loading="searching">
          <el-icon><Search /></el-icon>
          执行搜索
        </el-button>
        <el-button @click="saveSearchTemplate">
          <el-icon><Collection /></el-icon>
          保存模板
        </el-button>
        <el-button @click="loadSearchTemplate">
          <el-icon><FolderOpened /></el-icon>
          加载模板
        </el-button>
      </div>
    </el-card>

    <!-- 搜索模板管理对话框 -->
    <SearchTemplateDialog 
      v-model="templateDialogVisible"
      :mode="templateDialogMode"
      :templates="savedTemplates"
      :current-query="searchQuery"
      @save="handleSaveTemplate"
      @load="handleLoadTemplate"
      @delete="handleDeleteTemplate"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Plus, Delete, Search, RefreshLeft, View, Collection, FolderOpened
} from '@element-plus/icons-vue'
import SearchTemplateDialog from './SearchTemplateDialog.vue'

// Props
interface Props {
  modelValue?: any
  operationTypes?: Record<string, string>
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: null,
  operationTypes: () => ({})
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: any]
  'search': [query: any]
}>()

// 响应式数据
const showJsonView = ref(false)
const searching = ref(false)
const templateDialogVisible = ref(false)
const templateDialogMode = ref<'save' | 'load'>('save')

// 搜索条件组
const searchGroups = ref([
  {
    id: Date.now(),
    operator: 'AND',
    conditions: [
      {
        id: Date.now() + 1,
        operator: 'AND',
        field: '',
        comparison: 'EQUALS',
        value: null
      }
    ]
  }
])

// 保存的搜索模板
const savedTemplates = ref<any[]>([
  {
    id: 1,
    name: '今日失败操作',
    description: '查询今天的失败操作记录',
    query: {
      groups: [
        {
          operator: 'AND',
          conditions: [
            { field: 'operationResult', comparison: 'EQUALS', value: 'FAILED' },
            { field: 'createdAt', comparison: 'DATE_RANGE', value: 'TODAY' }
          ]
        }
      ]
    }
  },
  {
    id: 2,
    name: '批量操作记录',
    description: '查询所有批量操作记录',
    query: {
      groups: [
        {
          operator: 'AND',
          conditions: [
            { field: 'operationType', comparison: 'STARTS_WITH', value: 'BATCH_' }
          ]
        }
      ]
    }
  }
])

// 字段配置
const fieldGroups = [
  {
    label: '操作者信息',
    options: [
      { label: '操作者ID', value: 'operatorId' },
      { label: '操作者用户名', value: 'operatorName' }
    ]
  },
  {
    label: '目标对象',
    options: [
      { label: '目标用户ID', value: 'targetUserId' },
      { label: '目标用户名', value: 'targetUsername' }
    ]
  },
  {
    label: '操作信息',
    options: [
      { label: '操作类型', value: 'operationType' },
      { label: '操作结果', value: 'operationResult' },
      { label: '操作描述', value: 'operationDesc' }
    ]
  },
  {
    label: '请求信息',
    options: [
      { label: 'IP地址', value: 'ipAddress' },
      { label: '请求URI', value: 'requestUri' },
      { label: '请求方法', value: 'requestMethod' }
    ]
  },
  {
    label: '时间信息',
    options: [
      { label: '操作时间', value: 'createdAt' }
    ]
  }
]

// 比较操作符配置
const comparisonOperators = {
  text: [
    { label: '等于', value: 'EQUALS' },
    { label: '不等于', value: 'NOT_EQUALS' },
    { label: '包含', value: 'CONTAINS' },
    { label: '不包含', value: 'NOT_CONTAINS' },
    { label: '开始于', value: 'STARTS_WITH' },
    { label: '结束于', value: 'ENDS_WITH' },
    { label: '为空', value: 'IS_NULL' },
    { label: '不为空', value: 'IS_NOT_NULL' },
    { label: '在列表中', value: 'IN' }
  ],
  textlike: [
    { label: '包含', value: 'CONTAINS' }
  ],
  textmulti: [
    { label: '包含', value: 'CONTAINS' },
    { label: '在列表中', value: 'IN' }
  ],
  number: [
    { label: '等于', value: 'EQUALS' }
  ],
  datetime: [
    { label: '在范围内', value: 'DATE_RANGE' }
  ],
  enum: [
    { label: '等于', value: 'EQUALS' }
  ],
  enummulti: [
    { label: '等于', value: 'EQUALS' },
    { label: '在列表中', value: 'IN' }
  ]
}

// 字段类型映射
const fieldTypeMap = {
  operatorId: 'number',
  operatorName: 'textlike',
  targetUserId: 'number',
  targetUsername: 'textlike',
  operationType: 'enum',
  operationResult: 'enum',
  operationDesc: 'textlike',
  ipAddress: 'textmulti', 
  requestUri: 'textmulti',
  requestMethod: 'enummulti',
  createdAt: 'datetime'
}

// 计算属性
const searchQuery = computed(() => {
  return {
    groups: searchGroups.value.map(group => ({
      operator: group.operator,
      conditions: group.conditions.filter(c => c.field && c.comparison).map(condition => ({
        field: condition.field,
        comparison: condition.comparison,
        value: condition.value,
        operator: condition.operator
      }))
    })).filter(group => group.conditions.length > 0)
  }
})

// 监听器
watch(searchQuery, (newQuery) => {
  emit('update:modelValue', newQuery)
}, { deep: true })

// 方法
const getFieldType = (field: string) => {
  return fieldTypeMap[field] || 'text'
}

const getAvailableOperators = (field: string) => {
  const fieldType = getFieldType(field)
  return comparisonOperators[fieldType] || comparisonOperators.text
}

const getEnumOptions = (field: string) => {
  switch (field) {
    case 'operationType':
      return Object.entries(props.operationTypes).map(([value, label]) => ({
        value,
        label
      }))
    case 'operationResult':
      return [
        { label: '成功', value: 'SUCCESS' },
        { label: '失败', value: 'FAILED' },
        { label: '部分成功', value: 'PARTIAL_SUCCESS' }
      ]
    case 'requestMethod':
      return [
        { label: 'GET', value: 'GET' },
        { label: 'POST', value: 'POST' },
        { label: 'PUT', value: 'PUT' },
        { label: 'DELETE', value: 'DELETE' },
        { label: 'PATCH', value: 'PATCH' }
      ]
    default:
      return []
  }
}

const onFieldChange = (condition: any) => {
  // 重置比较操作符和值
  condition.comparison = 'EQUALS'
  condition.value = null
}

const addGroup = () => {
  searchGroups.value.push({
    id: Date.now(),
    operator: 'AND',
    conditions: [
      {
        id: Date.now() + 1,
        operator: 'AND',
        field: '',
        comparison: 'EQUALS',
        value: null
      }
    ]
  })
}

const removeGroup = (groupIndex: number) => {
  if (searchGroups.value.length > 1) {
    searchGroups.value.splice(groupIndex, 1)
  }
}

const addCondition = (groupIndex: number) => {
  searchGroups.value[groupIndex].conditions.push({
    id: Date.now(),
    operator: 'AND',
    field: '',
    comparison: 'EQUALS',
    value: null
  })
}

const removeCondition = (groupIndex: number, conditionIndex: number) => {
  const group = searchGroups.value[groupIndex]
  if (group.conditions.length > 1) {
    group.conditions.splice(conditionIndex, 1)
  } else if (searchGroups.value.length > 1) {
    // 如果是最后一个条件，删除整个组
    removeGroup(groupIndex)
  }
}

const resetSearchBuilder = () => {
  searchGroups.value = [
    {
      id: Date.now(),
      operator: 'AND',
      conditions: [
        {
          id: Date.now() + 1,
          operator: 'AND',
          field: '',
          comparison: 'EQUALS',
          value: null
        }
      ]
    }
  ]
}

const toggleJsonView = () => {
  showJsonView.value = !showJsonView.value
}

const executeSearch = () => {
  if (searchQuery.value.groups.length === 0) {
    ElMessage.warning('请至少添加一个搜索条件')
    return
  }
  
  searching.value = true
  emit('search', searchQuery.value)
  
  // 模拟搜索延迟
  setTimeout(() => {
    searching.value = false
  }, 1000)
}

const saveSearchTemplate = () => {
  templateDialogMode.value = 'save'
  templateDialogVisible.value = true
}

const loadSearchTemplate = () => {
  templateDialogMode.value = 'load'
  templateDialogVisible.value = true
}

const handleSaveTemplate = (template: any) => {
  savedTemplates.value.push({
    id: Date.now(),
    ...template,
    query: searchQuery.value
  })
  ElMessage.success('搜索模板保存成功')
}

const handleLoadTemplate = (template: any) => {
  // 重建搜索条件
  searchGroups.value = template.query.groups.map((group: any) => ({
    id: Date.now() + Math.random(),
    operator: group.operator,
    conditions: group.conditions.map((condition: any) => ({
      id: Date.now() + Math.random(),
      ...condition
    }))
  }))
  ElMessage.success('搜索模板加载成功')
}

const handleDeleteTemplate = (templateId: number) => {
  const index = savedTemplates.value.findIndex(t => t.id === templateId)
  if (index > -1) {
    savedTemplates.value.splice(index, 1)
    ElMessage.success('搜索模板删除成功')
  }
}
</script>

<style scoped>
.advanced-search-builder {
  margin-bottom: 24px;
}

.search-builder-card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  color: #262626;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.search-groups {
  margin-bottom: 24px;
}

.search-group {
  margin-bottom: 16px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background: #fafbfc;
}

.search-group.has-multiple-groups {
  position: relative;
  margin-left: 0;
  margin-top: 24px;
}

.group-operator {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
  padding: 8px 0;
}

.group-operator::before {
  content: '';
  flex: 1;
  height: 1px;
  background: #e8e8e8;
  margin-right: 12px;
}

.group-operator::after {
  content: '';
  flex: 1;
  height: 1px;
  background: #e8e8e8;
  margin-left: 12px;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.group-title {
  font-weight: 600;
  color: #262626;
}

.group-actions {
  display: flex;
  gap: 8px;
}

.conditions-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.search-condition {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  position: relative;
}

.condition-operator {
  width: 80px;
  flex-shrink: 0;
}

.condition-field {
  width: 200px;
  flex-shrink: 0;
}

.condition-comparison {
  width: 120px;
  flex-shrink: 0;
}

.condition-value {
  flex: 1;
  min-width: 200px;
}

.condition-actions {
  width: 40px;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
}

.add-group-section {
  text-align: center;
  margin-bottom: 24px;
}

.add-group-btn {
  border-style: dashed;
}

.json-view {
  margin: 24px 0;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.json-view h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #262626;
}

.json-content {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #262626;
  background: #fff;
  padding: 12px;
  border-radius: 4px;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.search-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-card__header) {
  padding: 16px 20px;
  background: #fafbfc;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-card__body) {
  padding: 20px;
}

@media (max-width: 768px) {
  .search-condition {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .condition-field,
  .condition-comparison,
  .condition-value {
    width: 100%;
  }
}
</style>