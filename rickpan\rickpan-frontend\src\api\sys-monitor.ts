import request from '@/utils/request'
import type {
  ApiResponse,
  PageResponse,
  SysMonitorData,
  HistoryRecord,
  HistoryQueryParams,
  SimpleQueryParams,
  CollectionStatistics,
  HealthStatus,
  MonitorStatistics,
  TimeRange
} from '@/types/sys-monitor'

/**
 * 系统监控API服务
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

// ==================== 实时数据API ====================

/**
 * 获取实时监控数据
 * 
 * @returns 实时监控数据
 */
export const getRealtimeData = (): Promise<ApiResponse<SysMonitorData>> => {
  return request.get('/sys-monitor/realtime')
}

// ==================== 历史数据API ====================

/**
 * 查询历史监控数据 (复杂查询)
 * 
 * @param params 查询参数
 * @returns 历史监控数据分页结果
 */
export const getHistoryData = (params: HistoryQueryParams): Promise<PageResponse<HistoryRecord>> => {
  return request.post('/sys-monitor/history', params)
}

/**
 * 查询历史监控数据 (简化查询)
 * 
 * @param params 简化查询参数
 * @returns 历史监控数据分页结果
 */
export const getHistoryDataSimple = (params?: SimpleQueryParams): Promise<PageResponse<HistoryRecord>> => {
  return request.get('/sys-monitor/history', { params })
}

/**
 * 根据时间范围快速查询历史数据
 * 
 * @param timeRange 时间范围
 * @param page 页码
 * @param size 每页大小
 * @returns 历史监控数据
 */
export const getHistoryByTimeRange = (
  timeRange: TimeRange = '1h',
  page: number = 1,
  size: number = 20
): Promise<PageResponse<HistoryRecord>> => {
  return getHistoryDataSimple({ timeRange, page, size })
}

// ==================== 统计数据API ====================

/**
 * 获取监控数据统计信息
 * 
 * @param timeRange 时间范围，默认24小时
 * @returns 统计信息
 */
export const getStatistics = (timeRange: TimeRange = '24h'): Promise<ApiResponse<MonitorStatistics>> => {
  return request.get('/sys-monitor/statistics', {
    params: { timeRange }
  })
}

/**
 * 获取多个时间范围的统计对比
 * 
 * @param timeRanges 时间范围数组
 * @returns 统计对比数据
 */
export const getStatisticsComparison = async (
  timeRanges: TimeRange[]
): Promise<Record<TimeRange, MonitorStatistics>> => {
  const promises = timeRanges.map(range => 
    getStatistics(range).then(response => ({ range, data: response.data }))
  )
  
  const results = await Promise.all(promises)
  
  return results.reduce((acc, { range, data }) => {
    acc[range] = data
    return acc
  }, {} as Record<TimeRange, MonitorStatistics>)
}

// ==================== 采集控制API ====================

/**
 * 获取采集服务状态
 * 
 * @returns 采集服务状态信息
 */
export const getCollectorStatus = (): Promise<ApiResponse<CollectionStatistics>> => {
  return request.get('/sys-monitor/collector/status')
}

/**
 * 启用数据采集
 * 
 * @returns 操作结果
 */
export const enableCollection = (): Promise<ApiResponse<void>> => {
  return request.post('/sys-monitor/collector/enable')
}

/**
 * 禁用数据采集
 * 
 * @returns 操作结果
 */
export const disableCollection = (): Promise<ApiResponse<void>> => {
  return request.post('/sys-monitor/collector/disable')
}

/**
 * 手动触发数据采集
 * 
 * @returns 采集结果数据
 */
export const manualCollection = (): Promise<ApiResponse<SysMonitorData>> => {
  return request.post('/sys-monitor/collector/manual')
}

/**
 * 重置采集统计
 * 
 * @returns 操作结果
 */
export const resetStatistics = (): Promise<ApiResponse<void>> => {
  return request.post('/sys-monitor/collector/reset-stats')
}

/**
 * 切换采集状态 (启用/禁用)
 * 
 * @param enabled 是否启用
 * @returns 操作结果
 */
export const toggleCollection = (enabled: boolean): Promise<ApiResponse<void>> => {
  return enabled ? enableCollection() : disableCollection()
}

// ==================== 健康检查API ====================

/**
 * 系统监控健康检查
 * 
 * @returns 健康状态
 */
export const healthCheck = (): Promise<ApiResponse<HealthStatus>> => {
  return request.get('/sys-monitor/health')
}

/**
 * 检查监控模块是否正常工作
 * 
 * @returns 是否正常
 */
export const isMonitorHealthy = async (): Promise<boolean> => {
  try {
    const response = await healthCheck()
    return response.success && response.data?.status === 'UP'
  } catch (error) {
    console.error('健康检查失败:', error)
    return false
  }
}

// ==================== 批量数据API ====================

/**
 * 批量获取监控数据 (实时数据 + 统计信息)
 * 
 * @param timeRange 统计时间范围
 * @returns 包含实时数据和统计信息的组合数据
 */
export const getBatchMonitorData = async (timeRange: TimeRange = '24h') => {
  try {
    const [realtimeResponse, statisticsResponse, statusResponse] = await Promise.all([
      getRealtimeData(),
      getStatistics(timeRange),
      getCollectorStatus()
    ])

    return {
      realtime: realtimeResponse.data,
      statistics: statisticsResponse.data,
      collectorStatus: statusResponse.data,
      success: true
    }
  } catch (error) {
    console.error('批量获取监控数据失败:', error)
    throw error
  }
}

/**
 * 获取监控概览数据
 * 包含实时数据、近期趋势、采集状态等
 * 
 * @returns 监控概览数据
 */
export const getMonitorOverview = async () => {
  try {
    const [realtimeData, historyData, collectorStatus, healthStatus] = await Promise.all([
      getRealtimeData(),
      getHistoryByTimeRange('1h', 1, 12), // 获取最近1小时，12个数据点
      getCollectorStatus(),
      healthCheck()
    ])

    return {
      realtime: realtimeData.data,
      history: historyData.data,
      trend: {
        totalPoints: historyData.total,
        timeRange: '1h'
      },
      collector: collectorStatus.data,
      health: healthStatus.data,
      lastUpdated: new Date().toISOString()
    }
  } catch (error) {
    console.error('获取监控概览数据失败:', error)
    throw error
  }
}

// ==================== 实用工具API ====================

/**
 * 轮询获取实时数据
 * 
 * @param callback 数据回调函数
 * @param interval 轮询间隔(毫秒)，默认5秒
 * @returns 停止轮询的函数
 */
export const pollRealtimeData = (
  callback: (data: SysMonitorData | null, error?: Error) => void,
  interval: number = 5000
): (() => void) => {
  let timerId: number | null = null
  let isPolling = true

  const poll = async () => {
    if (!isPolling) return

    try {
      const response = await getRealtimeData()
      if (response.success && response.data) {
        callback(response.data)
      } else {
        callback(null, new Error(response.message || '获取数据失败'))
      }
    } catch (error) {
      callback(null, error as Error)
    }

    if (isPolling) {
      timerId = window.setTimeout(poll, interval)
    }
  }

  // 立即执行一次
  poll()

  // 返回停止函数
  return () => {
    isPolling = false
    if (timerId) {
      clearTimeout(timerId)
      timerId = null
    }
  }
}

/**
 * 获取监控数据趋势
 * 
 * @param timeRange 时间范围
 * @param metrics 指标类型数组
 * @returns 趋势数据
 */
export const getMonitorTrend = async (
  timeRange: TimeRange = '24h',
  metrics: string[] = ['cpu', 'memory', 'disk']
) => {
  try {
    const queryParams: HistoryQueryParams = {
      timeRange,
      metrics: metrics as any[],
      sort: 'asc',
      page: 1,
      size: 100 // 获取更多数据点用于趋势分析
    }

    const response = await getHistoryData(queryParams)
    
    if (!response.success || !response.data) {
      throw new Error(response.message || '获取趋势数据失败')
    }

    // 处理趋势数据
    const trendData = response.data.map(record => ({
      timestamp: record.recordTime,
      cpu: record.cpuUsage,
      memory: record.memoryUsage,
      disk: record.diskUsage,
      jvm: record.jvmHeapUsage
    }))

    return {
      data: trendData,
      timeRange,
      metrics,
      totalPoints: response.total,
      success: true
    }
  } catch (error) {
    console.error('获取监控趋势失败:', error)
    throw error
  }
}

/**
 * 导出历史数据 (前端实现)
 * 
 * @param params 查询参数
 * @param format 导出格式
 */
export const exportHistoryData = async (
  params: HistoryQueryParams,
  format: 'csv' | 'json' = 'csv'
) => {
  try {
    // 获取所有数据（不分页）
    const exportParams = { ...params, page: 1, size: 10000 }
    const response = await getHistoryData(exportParams)
    
    if (!response.success || !response.data) {
      throw new Error('导出数据获取失败')
    }

    const data = response.data
    const filename = `monitor-history-${Date.now()}.${format}`

    if (format === 'csv') {
      // CSV格式导出
      const csvContent = convertToCSV(data)
      downloadFile(csvContent, filename, 'text/csv')
    } else {
      // JSON格式导出
      const jsonContent = JSON.stringify(data, null, 2)
      downloadFile(jsonContent, filename, 'application/json')
    }

    return { success: true, filename }
  } catch (error) {
    console.error('导出历史数据失败:', error)
    throw error
  }
}

// ==================== 辅助函数 ====================

/**
 * 转换为CSV格式
 */
function convertToCSV(data: HistoryRecord[]): string {
  if (data.length === 0) return ''

  const headers = Object.keys(data[0]).join(',')
  const rows = data.map(record => Object.values(record).join(','))
  
  return [headers, ...rows].join('\n')
}

/**
 * 下载文件
 */
function downloadFile(content: string, filename: string, mimeType: string) {
  const blob = new Blob([content], { type: mimeType })
  const url = URL.createObjectURL(blob)
  
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}