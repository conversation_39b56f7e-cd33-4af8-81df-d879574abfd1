/**
 * 系统监控数据类型定义
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

// ==================== 基础响应类型 ====================

/**
 * API响应基础接口
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message: string;
  timestamp: string;
  errorCode?: string;
  errorType?: string;
  fieldErrors?: Record<string, string>;
}

/**
 * 分页响应接口
 */
export interface PageResponse<T = any> extends ApiResponse<T[]> {
  total: number;
  totalPages: number;
  currentPage: number;
  size: number;
}

// ==================== 监控数据类型 ====================

/**
 * CPU信息
 */
export interface CpuInfo {
  usage: number;          // CPU使用率 (%)
  cores: number;          // CPU核心数
  loadAverage: number[];  // 系统负载 [1分钟, 5分钟, 15分钟]
  processUsage: number;   // 进程CPU使用率 (%)
}

/**
 * 内存信息
 */
export interface MemoryInfo {
  total: number;     // 总内存 (字节)
  used: number;      // 已使用内存 (字节)
  free: number;      // 空闲内存 (字节)
  available: number; // 可用内存 (字节)
  usage: number;     // 内存使用率 (%)
}

/**
 * 磁盘信息
 */
export interface DiskInfo {
  total: number;     // 总磁盘空间 (字节)
  used: number;      // 已使用磁盘空间 (字节)
  available: number; // 可用磁盘空间 (字节)
  usage: number;     // 磁盘使用率 (%)
}

/**
 * JVM信息
 */
export interface JvmInfo {
  heapUsed: number;         // 堆内存已使用 (字节)
  heapMax: number;          // 堆内存最大值 (字节)
  heapCommitted: number;    // 堆内存已提交 (字节)
  heapUsage: number;        // 堆内存使用率 (%)
  nonHeapUsed: number;      // 非堆内存已使用 (字节)
  nonHeapMax: number;       // 非堆内存最大值 (字节)
  nonHeapCommitted: number; // 非堆内存已提交 (字节)
  nonHeapUsage: number;     // 非堆内存使用率 (%)
  uptime: number;           // JVM运行时间 (毫秒)
}

/**
 * GC收集器信息
 */
export interface GcCollectorInfo {
  name: string;               // 收集器名称
  collectionCount: number;    // 收集次数
  collectionTime: number;     // 收集总时间 (毫秒)
  avgCollectionTime: number;  // 平均收集时间 (毫秒)
}

/**
 * GC信息
 */
export interface GcInfo {
  count: number;              // 总GC次数
  time: number;               // 总GC时间 (毫秒)
  avgTime: number;            // 平均GC时间 (毫秒)
  maxTime: number;            // 最大GC时间 (毫秒)
  collectors: GcCollectorInfo[]; // 收集器详情
}

/**
 * 线程信息
 */
export interface ThreadInfo {
  active: number;       // 活动线程数
  peak: number;         // 峰值线程数
  daemon: number;       // 守护线程数
  totalStarted: number; // 总启动线程数
  user: number;         // 用户线程数
  deadlocked: number;   // 死锁线程数
}

/**
 * 系统信息
 */
export interface SystemInfo {
  osName: string;         // 操作系统名称
  osVersion: string;      // 操作系统版本
  osArch: string;         // 系统架构
  javaVersion: string;    // Java版本
  javaVendor: string;     // Java厂商
  jvmName: string;        // JVM名称
  jvmVersion: string;     // JVM版本
  appName: string;        // 应用名称
  appVersion: string;     // 应用版本
  appStartTime: string;   // 应用启动时间
  systemUptime: number;   // 系统运行时间 (毫秒)
  jvmUptime: number;      // JVM运行时间 (毫秒)
}

/**
 * 完整监控数据
 */
export interface SysMonitorData {
  timestamp: string;    // 数据时间戳
  cpu: CpuInfo;        // CPU信息
  memory: MemoryInfo;  // 内存信息
  disk: DiskInfo;      // 磁盘信息
  jvm: JvmInfo;        // JVM信息
  gc: GcInfo;          // GC信息
  threads: ThreadInfo; // 线程信息
  system: SystemInfo;  // 系统信息
}

// ==================== 查询参数类型 ====================

/**
 * 时间范围类型
 */
export type TimeRange = '1h' | '6h' | '24h' | '7d' | '30d' | 'custom';

/**
 * 监控指标类型
 */
export type MetricType = 'cpu' | 'memory' | 'disk' | 'jvm' | 'gc' | 'threads';

/**
 * 排序方式
 */
export type SortOrder = 'asc' | 'desc';

/**
 * 数据聚合间隔
 */
export type AggregateInterval = '1m' | '5m' | '15m' | '1h';

/**
 * 历史数据查询参数
 */
export interface HistoryQueryParams {
  timeRange: TimeRange;           // 时间范围
  startTime?: string;             // 自定义开始时间
  endTime?: string;               // 自定义结束时间
  metrics?: MetricType[];         // 监控指标数组
  interval?: AggregateInterval;   // 数据聚合间隔
  maxDataPoints?: number;         // 最大返回数据点数
  includeDetails?: boolean;       // 是否包含详细信息
  sort?: SortOrder;               // 排序方式
  page?: number;                  // 页码
  size?: number;                  // 每页大小
}

/**
 * 简化查询参数
 */
export interface SimpleQueryParams {
  timeRange?: TimeRange;
  page?: number;
  size?: number;
  sort?: SortOrder;
}

// ==================== 历史数据类型 ====================

/**
 * 历史监控数据记录
 */
export interface HistoryRecord {
  id: number;
  recordTime: string;       // 记录时间
  cpuUsage: number;         // CPU使用率
  memoryUsage: number;      // 内存使用率
  diskUsage: number;        // 磁盘使用率
  jvmHeapUsage: number;     // JVM堆使用率
  memoryTotal: number;      // 总内存
  memoryUsed: number;       // 已使用内存
  diskTotal: number;        // 总磁盘空间
  diskUsed: number;         // 已使用磁盘
  jvmHeapUsed: number;      // JVM堆已使用
  jvmHeapMax: number;       // JVM堆最大值
  gcCount: number;          // GC次数
  gcTime: number;           // GC时间
  threadCount: number;      // 线程数
  cpuCores: number;         // CPU核心数
}

// ==================== 采集服务类型 ====================

/**
 * 采集服务统计信息
 */
export interface CollectionStatistics {
  enabled: boolean;               // 是否启用
  collecting: boolean;            // 是否正在采集
  totalCollections: number;       // 总采集次数
  successCollections: number;     // 成功采集次数
  errorCollections: number;       // 失败采集次数
  successRate: number;            // 成功率 (%)
  lastCollectionTime?: string;    // 最后采集时间
  lastSuccessTime?: string;       // 最后成功时间
}

/**
 * 健康检查状态
 */
export interface HealthStatus {
  status: 'UP' | 'DOWN';              // 健康状态
  collectorEnabled: boolean;          // 采集器是否启用
  dataCollectionWorking: boolean;     // 数据采集是否正常
  lastSuccessTime?: string;           // 最后成功时间
  successRate: number;                // 成功率
  error?: string;                     // 错误信息
}

// ==================== 统计数据类型 ====================

/**
 * 监控统计数据
 */
export interface MonitorStatistics {
  timeRange: TimeRange;       // 统计时间范围
  cpu: {
    avg: number;              // 平均CPU使用率
    max: number;              // 最大CPU使用率
    min: number;              // 最小CPU使用率
  };
  memory: {
    avg: number;              // 平均内存使用率
    max: number;              // 最大内存使用率
    min: number;              // 最小内存使用率
  };
  disk: {
    avg: number;              // 平均磁盘使用率
    max: number;              // 最大磁盘使用率
    min: number;              // 最小磁盘使用率
  };
  jvm: {
    avg: number;              // 平均JVM堆使用率
    max: number;              // 最大JVM堆使用率
    min: number;              // 最小JVM堆使用率
  };
}

// ==================== 图表数据类型 ====================

/**
 * 图表数据点
 */
export interface ChartDataPoint {
  timestamp: string;        // 时间戳
  value: number;           // 数值
  label?: string;          // 标签
}

/**
 * 图表系列数据
 */
export interface ChartSeries {
  name: string;                    // 系列名称
  data: ChartDataPoint[];          // 数据点数组
  color?: string;                  // 颜色
  type?: 'line' | 'bar' | 'area'; // 图表类型
}

/**
 * 图表配置
 */
export interface ChartConfig {
  title: string;              // 图表标题
  subtitle?: string;          // 子标题
  series: ChartSeries[];      // 数据系列
  xAxis: {
    type: 'datetime' | 'category';
    title: string;
  };
  yAxis: {
    title: string;
    unit?: string;            // 单位
    min?: number;             // 最小值
    max?: number;             // 最大值
  };
  legend?: boolean;           // 是否显示图例
  tooltip?: boolean;          // 是否显示提示框
}

// ==================== 工具函数类型 ====================

/**
 * 字节格式化选项
 */
export interface ByteFormatOptions {
  binary?: boolean;     // 是否使用二进制 (1024) 而不是十进制 (1000)
  precision?: number;   // 小数位数
  unit?: string;        // 单位后缀
}

/**
 * 时间格式化选项
 */
export interface TimeFormatOptions {
  format?: string;      // 时间格式
  locale?: string;      // 本地化
  relative?: boolean;   // 是否显示相对时间
}