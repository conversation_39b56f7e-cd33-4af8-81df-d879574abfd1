package com.rickpan.repository;

import com.rickpan.entity.SysMonitorRealtime;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 系统实时监控数据访问层
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface SysMonitorRealtimeRepository extends JpaRepository<SysMonitorRealtime, Long> {

    /**
     * 获取最新的监控数据
     * 
     * @return 最新的监控数据记录
     */
    @Query("SELECT s FROM SysMonitorRealtime s ORDER BY s.createdAt DESC")
    Optional<SysMonitorRealtime> findLatest();

    /**
     * 获取指定时间范围内的监控数据数量
     * 
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 数据数量
     */
    @Query("SELECT COUNT(s) FROM SysMonitorRealtime s WHERE s.createdAt BETWEEN :startTime AND :endTime")
    Long countByCreatedAtBetween(@Param("startTime") LocalDateTime startTime, 
                                @Param("endTime") LocalDateTime endTime);

    /**
     * 删除指定时间之前的监控数据
     * 
     * @param beforeTime 指定时间
     * @return 删除的记录数
     */
    @Modifying
    @Query("DELETE FROM SysMonitorRealtime s WHERE s.createdAt < :beforeTime")
    int deleteByCreatedAtBefore(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 清空所有实时监控数据
     * 
     * @return 删除的记录数
     */
    @Modifying
    @Query("DELETE FROM SysMonitorRealtime")
    int deleteAllRealtimeData();

    /**
     * 获取指定时间段内CPU使用率超过阈值的记录数
     * 
     * @param threshold 阈值
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 记录数
     */
    @Query("SELECT COUNT(s) FROM SysMonitorRealtime s WHERE s.cpuUsage > :threshold AND s.createdAt BETWEEN :startTime AND :endTime")
    Long countByCpuUsageGreaterThanAndCreatedAtBetween(@Param("threshold") Double threshold,
                                                      @Param("startTime") LocalDateTime startTime,
                                                      @Param("endTime") LocalDateTime endTime);

    /**
     * 获取指定时间段内内存使用率超过阈值的记录数
     * 
     * @param threshold 阈值
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 记录数
     */
    @Query("SELECT COUNT(s) FROM SysMonitorRealtime s WHERE s.memoryUsage > :threshold AND s.createdAt BETWEEN :startTime AND :endTime")
    Long countByMemoryUsageGreaterThanAndCreatedAtBetween(@Param("threshold") Double threshold,
                                                         @Param("startTime") LocalDateTime startTime,
                                                         @Param("endTime") LocalDateTime endTime);
}