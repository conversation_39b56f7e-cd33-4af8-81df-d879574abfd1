package com.rickpan.repository;

import com.rickpan.entity.SysMonitorAlertLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统监控告警日志数据访问层
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface SysMonitorAlertLogRepository extends JpaRepository<SysMonitorAlertLog, Long> {

    /**
     * 根据时间范围查询告警日志
     * 
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param pageable  分页参数
     * @return 告警日志分页结果
     */
    Page<SysMonitorAlertLog> findByAlertTimeBetweenOrderByAlertTimeDesc(
            LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * 根据告警级别查询日志
     * 
     * @param alertLevel 告警级别
     * @param pageable   分页参数
     * @return 告警日志分页结果
     */
    Page<SysMonitorAlertLog> findByAlertLevelOrderByAlertTimeDesc(String alertLevel, Pageable pageable);

    /**
     * 根据告警状态查询日志
     * 
     * @param alertStatus 告警状态
     * @param pageable    分页参数
     * @return 告警日志分页结果
     */
    Page<SysMonitorAlertLog> findByAlertStatusOrderByAlertTimeDesc(String alertStatus, Pageable pageable);

    /**
     * 根据告警级别和状态查询日志
     * 
     * @param alertLevel  告警级别
     * @param alertStatus 告警状态
     * @param pageable    分页参数
     * @return 告警日志分页结果
     */
    Page<SysMonitorAlertLog> findByAlertLevelAndAlertStatusOrderByAlertTimeDesc(String alertLevel, String alertStatus, Pageable pageable);

    /**
     * 根据指标类型查询日志
     * 
     * @param metricType 指标类型
     * @param pageable   分页参数
     * @return 告警日志分页结果
     */
    Page<SysMonitorAlertLog> findByMetricTypeOrderByAlertTimeDesc(String metricType, Pageable pageable);

    /**
     * 查询指定状态的告警日志
     * 
     * @param alertStatus 告警状态
     * @param alertLevel 告警级别
     * @param pageable 分页参数
     * @return 告警日志
     */
    Page<SysMonitorAlertLog> findByAlertStatusAndAlertLevelOrderByAlertTimeDesc(String alertStatus, String alertLevel, Pageable pageable);

    /**
     * 查询最近的告警日志
     * 
     * @param limit 限制数量
     * @return 最近的告警日志
     */
    @Query("SELECT a FROM SysMonitorAlertLog a ORDER BY a.alertTime DESC")
    List<SysMonitorAlertLog> findRecentAlerts(Pageable pageable);

    /**
     * 统计指定时间范围内的告警数量
     * 
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 告警数量
     */
    Long countByAlertTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据告警级别统计数量
     * 
     * @param alertLevel 告警级别
     * @return 告警数量
     */
    Long countByAlertLevel(String alertLevel);

    /**
     * 根据告警状态统计数量
     * 
     * @param alertStatus 告警状态
     * @return 告警数量
     */
    Long countByAlertStatus(String alertStatus);

    /**
     * 统计指定指标类型的告警数量
     * 
     * @param metricType 指标类型
     * @return 告警数量
     */
    Long countByMetricType(String metricType);

    /**
     * 查询告警统计信息（按级别分组）
     * 
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计结果 [alertLevel, count]
     */
    @Query("SELECT a.alertLevel, COUNT(a) FROM SysMonitorAlertLog a " +
           "WHERE a.alertTime BETWEEN :startTime AND :endTime " +
           "GROUP BY a.alertLevel " +
           "ORDER BY a.alertLevel")
    List<Object[]> getAlertStatsByLevel(@Param("startTime") LocalDateTime startTime,
                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 查询告警统计信息（按状态分组）
     * 
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计结果 [alertStatus, count]
     */
    @Query("SELECT a.alertStatus, COUNT(a) FROM SysMonitorAlertLog a " +
           "WHERE a.alertTime BETWEEN :startTime AND :endTime " +
           "GROUP BY a.alertStatus " +
           "ORDER BY a.alertStatus")
    List<Object[]> getAlertStatsByStatus(@Param("startTime") LocalDateTime startTime,
                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 查询告警统计信息（按指标类型分组）
     * 
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计结果 [metricType, count]
     */
    @Query("SELECT a.metricType, COUNT(a) FROM SysMonitorAlertLog a " +
           "WHERE a.alertTime BETWEEN :startTime AND :endTime " +
           "GROUP BY a.metricType " +
           "ORDER BY a.metricType")
    List<Object[]> getAlertStatsByMetricType(@Param("startTime") LocalDateTime startTime,
                                            @Param("endTime") LocalDateTime endTime);

    /**
     * 查询每日告警趋势
     * 
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计结果 [date, count]
     */
    @Query("SELECT DATE(a.alertTime), COUNT(a) FROM SysMonitorAlertLog a " +
           "WHERE a.alertTime BETWEEN :startTime AND :endTime " +
           "GROUP BY DATE(a.alertTime) " +
           "ORDER BY DATE(a.alertTime)")
    List<Object[]> getDailyAlertTrend(@Param("startTime") LocalDateTime startTime,
                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 删除指定时间之前的已解决告警日志
     * 
     * @param beforeTime  指定时间
     * @param alertStatus 告警状态（RESOLVED）
     * @return 删除的记录数
     */
    @Modifying
    @Query("DELETE FROM SysMonitorAlertLog a WHERE a.resolvedTime < :beforeTime AND a.alertStatus = :alertStatus")
    int deleteResolvedAlertsBefore(@Param("beforeTime") LocalDateTime beforeTime,
                                  @Param("alertStatus") String alertStatus);

    /**
     * 查找指定配置ID的告警日志
     * 
     * @param alertConfigId 告警配置ID
     * @param pageable      分页参数
     * @return 告警日志分页结果
     */
    Page<SysMonitorAlertLog> findByAlertConfigIdOrderByAlertTimeDesc(Long alertConfigId, Pageable pageable);

    /**
     * 查找相同指标和时间窗口内的重复告警（用于告警聚合）
     * 
     * @param metricType 指标类型
     * @param metricName 指标名称
     * @param startTime  时间窗口开始
     * @param endTime    时间窗口结束
     * @return 重复告警列表
     */
    List<SysMonitorAlertLog> findByMetricTypeAndMetricNameAndAlertTimeBetween(
            String metricType, String metricName, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据状态和时间范围统计告警数量
     * 
     * @param alertStatus 告警状态
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return 告警数量
     */
    Long countByAlertStatusAndAlertTimeBetween(String alertStatus, LocalDateTime startTime, LocalDateTime endTime);
}