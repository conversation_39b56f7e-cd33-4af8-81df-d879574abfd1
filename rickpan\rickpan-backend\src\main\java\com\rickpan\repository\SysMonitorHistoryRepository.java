package com.rickpan.repository;

import com.rickpan.entity.SysMonitorHistory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统历史监控数据访问层
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface SysMonitorHistoryRepository extends JpaRepository<SysMonitorHistory, Long> {

    /**
     * 根据时间范围查询历史监控数据
     * 
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param pageable  分页参数
     * @return 历史监控数据分页结果
     */
    Page<SysMonitorHistory> findByRecordTimeBetweenOrderByRecordTimeAsc(
            LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * 根据时间范围查询历史监控数据列表
     * 
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 历史监控数据列表
     */
    List<SysMonitorHistory> findByRecordTimeBetweenOrderByRecordTimeAsc(
            LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取指定时间范围内的统计数据
     * 
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计结果
     */
    @Query("SELECT " +
           "AVG(h.cpuUsage) as avgCpu, " +
           "MAX(h.cpuUsage) as maxCpu, " +
           "MIN(h.cpuUsage) as minCpu, " +
           "AVG(h.memoryUsage) as avgMemory, " +
           "MAX(h.memoryUsage) as maxMemory, " +
           "MIN(h.memoryUsage) as minMemory, " +
           "AVG(h.diskUsage) as avgDisk, " +
           "MAX(h.diskUsage) as maxDisk, " +
           "MIN(h.diskUsage) as minDisk, " +
           "AVG(h.jvmHeapUsage) as avgJvm, " +
           "MAX(h.jvmHeapUsage) as maxJvm, " +
           "MIN(h.jvmHeapUsage) as minJvm, " +
           "COUNT(h) as totalRecords " +
           "FROM SysMonitorHistory h " +
           "WHERE h.recordTime BETWEEN :startTime AND :endTime")
    Object[] getStatisticsByRecordTimeBetween(@Param("startTime") LocalDateTime startTime,
                                            @Param("endTime") LocalDateTime endTime);

    /**
     * 按小时聚合查询监控数据
     * 
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 按小时聚合的数据
     */
    @Query("SELECT " +
           "FUNCTION('DATE_FORMAT', h.recordTime, '%Y-%m-%d %H:00:00') as timeHour, " +
           "AVG(h.cpuUsage) as avgCpu, " +
           "AVG(h.memoryUsage) as avgMemory, " +
           "AVG(h.diskUsage) as avgDisk, " +
           "AVG(h.jvmHeapUsage) as avgJvm, " +
           "MAX(h.cpuUsage) as maxCpu, " +
           "MAX(h.memoryUsage) as maxMemory, " +
           "MAX(h.diskUsage) as maxDisk, " +
           "MAX(h.jvmHeapUsage) as maxJvm, " +
           "COUNT(h) as dataPoints " +
           "FROM SysMonitorHistory h " +
           "WHERE h.recordTime BETWEEN :startTime AND :endTime " +
           "GROUP BY FUNCTION('DATE_FORMAT', h.recordTime, '%Y-%m-%d %H:00:00') " +
           "ORDER BY timeHour")
    List<Object[]> getHourlyStatistics(@Param("startTime") LocalDateTime startTime,
                                      @Param("endTime") LocalDateTime endTime);

    /**
     * 按分钟聚合查询监控数据（用于短时间范围）
     * 
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 按分钟聚合的数据
     */
    @Query("SELECT " +
           "FUNCTION('DATE_FORMAT', h.recordTime, '%Y-%m-%d %H:%i:00') as timeMinute, " +
           "AVG(h.cpuUsage) as avgCpu, " +
           "AVG(h.memoryUsage) as avgMemory, " +
           "AVG(h.diskUsage) as avgDisk, " +
           "AVG(h.jvmHeapUsage) as avgJvm " +
           "FROM SysMonitorHistory h " +
           "WHERE h.recordTime BETWEEN :startTime AND :endTime " +
           "GROUP BY FUNCTION('DATE_FORMAT', h.recordTime, '%Y-%m-%d %H:%i:00') " +
           "ORDER BY timeMinute")
    List<Object[]> getMinutelyStatistics(@Param("startTime") LocalDateTime startTime,
                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 查询CPU使用率超过阈值的历史记录
     * 
     * @param threshold 阈值
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 超过阈值的记录
     */
    List<SysMonitorHistory> findByCpuUsageGreaterThanAndRecordTimeBetween(
            BigDecimal threshold, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询内存使用率超过阈值的历史记录
     * 
     * @param threshold 阈值
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 超过阈值的记录
     */
    List<SysMonitorHistory> findByMemoryUsageGreaterThanAndRecordTimeBetween(
            BigDecimal threshold, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 删除指定时间之前的历史数据
     * 
     * @param beforeTime 指定时间
     * @return 删除的记录数
     */
    @Modifying
    @Query("DELETE FROM SysMonitorHistory h WHERE h.recordTime < :beforeTime")
    int deleteByRecordTimeBefore(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 获取最新的监控记录
     * 
     * @return 最新的历史记录
     */
    @Query("SELECT h FROM SysMonitorHistory h ORDER BY h.recordTime DESC")
    List<SysMonitorHistory> findTopByOrderByRecordTimeDesc(Pageable pageable);

    /**
     * 统计指定时间范围内的数据数量
     * 
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 数据数量
     */
    Long countByRecordTimeBetween(LocalDateTime startTime, LocalDateTime endTime);
}