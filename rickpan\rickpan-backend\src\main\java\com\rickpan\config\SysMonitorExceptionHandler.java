package com.rickpan.config;

import com.rickpan.exception.SysMonitorException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 系统监控模块全局异常处理器
 * 统一处理系统监控相关的异常，提供友好的错误响应
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@RestControllerAdvice(basePackages = "com.rickpan.controller")
@Slf4j
public class SysMonitorExceptionHandler {

    /**
     * 处理系统监控模块特定异常
     * 
     * @param e 系统监控异常
     * @return 错误响应
     */
    @ExceptionHandler(SysMonitorException.class)
    public ResponseEntity<Map<String, Object>> handleSysMonitorException(SysMonitorException e) {
        log.error("系统监控异常: [{}] {}", e.getErrorCode(), e.getMessage(), e);
        
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("errorCode", e.getErrorCode());
        errorResponse.put("errorType", e.getErrorType().name());
        errorResponse.put("message", e.getMessage());
        errorResponse.put("timestamp", LocalDateTime.now());
        
        // 根据错误类型返回不同的HTTP状态码
        HttpStatus status = getHttpStatusForErrorType(e.getErrorType());
        
        return ResponseEntity.status(status).body(errorResponse);
    }

    /**
     * 处理参数验证异常 - @RequestBody参数验证失败
     * 
     * @param e 参数验证异常
     * @return 错误响应
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<Map<String, Object>> handleValidationException(MethodArgumentNotValidException e) {
        log.warn("请求参数验证失败: {}", e.getMessage());
        
        Map<String, String> fieldErrors = new HashMap<>();
        for (FieldError error : e.getBindingResult().getFieldErrors()) {
            fieldErrors.put(error.getField(), error.getDefaultMessage());
        }
        
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("errorCode", "VALIDATION_ERROR");
        errorResponse.put("message", "请求参数验证失败");
        errorResponse.put("fieldErrors", fieldErrors);
        errorResponse.put("timestamp", LocalDateTime.now());
        
        return ResponseEntity.badRequest().body(errorResponse);
    }

    /**
     * 处理参数绑定异常 - @ModelAttribute参数验证失败
     * 
     * @param e 参数绑定异常
     * @return 错误响应
     */
    @ExceptionHandler(BindException.class)
    public ResponseEntity<Map<String, Object>> handleBindException(BindException e) {
        log.warn("参数绑定失败: {}", e.getMessage());
        
        Map<String, String> fieldErrors = new HashMap<>();
        for (FieldError error : e.getBindingResult().getFieldErrors()) {
            fieldErrors.put(error.getField(), error.getDefaultMessage());
        }
        
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("errorCode", "BIND_ERROR");
        errorResponse.put("message", "参数绑定失败");
        errorResponse.put("fieldErrors", fieldErrors);
        errorResponse.put("timestamp", LocalDateTime.now());
        
        return ResponseEntity.badRequest().body(errorResponse);
    }

    /**
     * 处理约束验证异常 - @Validated参数验证失败
     * 
     * @param e 约束验证异常
     * @return 错误响应
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<Map<String, Object>> handleConstraintViolationException(ConstraintViolationException e) {
        log.warn("约束验证失败: {}", e.getMessage());
        
        String errorMessages = e.getConstraintViolations().stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining(", "));
        
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("errorCode", "CONSTRAINT_VIOLATION");
        errorResponse.put("message", "约束验证失败: " + errorMessages);
        errorResponse.put("timestamp", LocalDateTime.now());
        
        return ResponseEntity.badRequest().body(errorResponse);
    }

    /**
     * 处理非法参数异常
     * 
     * @param e 非法参数异常
     * @return 错误响应
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<Map<String, Object>> handleIllegalArgumentException(IllegalArgumentException e) {
        log.warn("非法参数异常: {}", e.getMessage());
        
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("errorCode", "ILLEGAL_ARGUMENT");
        errorResponse.put("message", "参数错误: " + e.getMessage());
        errorResponse.put("timestamp", LocalDateTime.now());
        
        return ResponseEntity.badRequest().body(errorResponse);
    }

    /**
     * 处理运行时异常
     * 
     * @param e 运行时异常
     * @return 错误响应
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<Map<String, Object>> handleRuntimeException(RuntimeException e) {
        log.error("运行时异常: {}", e.getMessage(), e);
        
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("errorCode", "RUNTIME_ERROR");
        errorResponse.put("message", "服务异常: " + e.getMessage());
        errorResponse.put("timestamp", LocalDateTime.now());
        
        return ResponseEntity.internalServerError().body(errorResponse);
    }

    /**
     * 处理通用异常
     * 
     * @param e 通用异常
     * @return 错误响应
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, Object>> handleGenericException(Exception e) {
        log.error("未知异常: {}", e.getMessage(), e);
        
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("errorCode", "INTERNAL_ERROR");
        errorResponse.put("message", "系统内部错误，请稍后重试");
        errorResponse.put("timestamp", LocalDateTime.now());
        
        return ResponseEntity.internalServerError().body(errorResponse);
    }

    // ==================== 私有方法 ====================

    /**
     * 根据错误类型获取HTTP状态码
     * 
     * @param errorType 错误类型
     * @return HTTP状态码
     */
    private HttpStatus getHttpStatusForErrorType(SysMonitorException.ErrorType errorType) {
        switch (errorType) {
            case VALIDATION_ERROR:
                return HttpStatus.BAD_REQUEST;
            case PERMISSION_ERROR:
                return HttpStatus.FORBIDDEN;
            case TIMEOUT_ERROR:
                return HttpStatus.REQUEST_TIMEOUT;
            case CONFIGURATION_ERROR:
            case DATA_COLLECTION_ERROR:
            case JMX_CONNECTION_ERROR:
            case DATABASE_ERROR:
            case ALERT_ERROR:
            case EXPORT_ERROR:
            case GENERAL:
            default:
                return HttpStatus.INTERNAL_SERVER_ERROR;
        }
    }
}