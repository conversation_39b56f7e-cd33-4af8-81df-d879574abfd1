import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  SysMonitorData,
  HistoryRecord,
  CollectionStatistics,
  HealthStatus,
  MonitorStatistics,
  TimeRange,
  HistoryQueryParams
} from '@/types/sys-monitor'
import {
  getRealtimeData,
  getHistoryData,
  getCollectorStatus,
  healthCheck,
  getStatistics,
  pollRealtimeData,
  getMonitorOverview
} from '@/api/sys-monitor'
import { monitorCache } from '@/utils/sys-monitor'

/**
 * 系统监控状态管理
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
export const useSysMonitorStore = defineStore('sysMonitor', () => {
  
  // ==================== 状态定义 ====================
  
  // 实时监控数据
  const realtimeData = ref<SysMonitorData | null>(null)
  const realtimeLoading = ref(false)
  const realtimeError = ref<string | null>(null)
  
  // 历史数据
  const historyData = ref<HistoryRecord[]>([])
  const historyLoading = ref(false)
  const historyError = ref<string | null>(null)
  const historyTotal = ref(0)
  const historyPage = ref(1)
  const historySize = ref(20)
  
  // 采集服务状态
  const collectorStatus = ref<CollectionStatistics | null>(null)
  const collectorLoading = ref(false)
  
  // 健康状态
  const healthStatus = ref<HealthStatus | null>(null)
  const healthLoading = ref(false)
  
  // 统计数据
  const statistics = ref<MonitorStatistics | null>(null)
  const statisticsLoading = ref(false)
  
  // 轮询控制
  const pollingEnabled = ref(false)
  const pollingInterval = ref(5000) // 5秒
  let stopPolling: (() => void) | null = null
  
  // 最后更新时间
  const lastUpdateTime = ref<string | null>(null)
  
  // ==================== 计算属性 ====================
  
  // 是否有实时数据
  const hasRealtimeData = computed(() => realtimeData.value !== null)
  
  // 系统整体健康状态
  const isSystemHealthy = computed(() => {
    return healthStatus.value?.status === 'UP' && 
           collectorStatus.value?.enabled && 
           collectorStatus.value?.successRate > 80
  })
  
  // CPU状态
  const cpuStatus = computed(() => {
    if (!realtimeData.value) return 'unknown'
    const usage = realtimeData.value.cpu.usage
    if (usage < 70) return 'normal'
    if (usage < 90) return 'warning'
    return 'danger'
  })
  
  // 内存状态
  const memoryStatus = computed(() => {
    if (!realtimeData.value) return 'unknown'
    const usage = realtimeData.value.memory.usage
    if (usage < 80) return 'normal'
    if (usage < 95) return 'warning'
    return 'danger'
  })
  
  // 磁盘状态
  const diskStatus = computed(() => {
    if (!realtimeData.value) return 'unknown'
    const usage = realtimeData.value.disk.usage
    if (usage < 80) return 'normal'
    if (usage < 90) return 'warning'
    return 'danger'
  })
  
  // JVM状态
  const jvmStatus = computed(() => {
    if (!realtimeData.value) return 'unknown'
    const usage = realtimeData.value.jvm.heapUsage
    if (usage < 70) return 'normal'
    if (usage < 85) return 'warning'
    return 'danger'
  })
  
  // 采集服务运行状态
  const collectorRunning = computed(() => {
    return collectorStatus.value?.enabled && collectorStatus.value?.collecting
  })
  
  // ==================== 基础操作 ====================
  
  /**
   * 获取实时监控数据
   */
  const fetchRealtimeData = async (useCache: boolean = true) => {
    const cacheKey = 'realtime-data'
    
    // 检查缓存
    if (useCache) {
      const cached = monitorCache.get(cacheKey)
      if (cached) {
        realtimeData.value = cached
        return cached
      }
    }
    
    try {
      realtimeLoading.value = true
      realtimeError.value = null
      
      const response = await getRealtimeData()
      if (response.success && response.data) {
        realtimeData.value = response.data
        lastUpdateTime.value = new Date().toISOString()
        
        // 缓存数据
        monitorCache.set(cacheKey, response.data, 3000) // 3秒缓存
        
        return response.data
      } else {
        throw new Error(response.message || '获取实时数据失败')
      }
    } catch (error) {
      console.error('获取实时监控数据失败:', error)
      realtimeError.value = error instanceof Error ? error.message : '获取数据失败'
      throw error
    } finally {
      realtimeLoading.value = false
    }
  }
  
  /**
   * 获取历史监控数据
   */
  const fetchHistoryData = async (params: HistoryQueryParams) => {
    try {
      historyLoading.value = true
      historyError.value = null
      
      const response = await getHistoryData(params)
      if (response.success && response.data) {
        historyData.value = response.data
        historyTotal.value = response.total
        historyPage.value = response.currentPage
        historySize.value = response.size
        
        return response
      } else {
        throw new Error(response.message || '获取历史数据失败')
      }
    } catch (error) {
      console.error('获取历史监控数据失败:', error)
      historyError.value = error instanceof Error ? error.message : '获取数据失败'
      throw error
    } finally {
      historyLoading.value = false
    }
  }
  
  /**
   * 获取采集服务状态
   */
  const fetchCollectorStatus = async () => {
    try {
      collectorLoading.value = true
      
      const response = await getCollectorStatus()
      if (response.success && response.data) {
        collectorStatus.value = response.data
        return response.data
      } else {
        throw new Error(response.message || '获取采集状态失败')
      }
    } catch (error) {
      console.error('获取采集服务状态失败:', error)
      throw error
    } finally {
      collectorLoading.value = false
    }
  }
  
  /**
   * 获取健康状态
   */
  const fetchHealthStatus = async () => {
    try {
      healthLoading.value = true
      
      const response = await healthCheck()
      if (response.data) {
        healthStatus.value = response.data
        return response.data
      } else {
        throw new Error(response.message || '健康检查失败')
      }
    } catch (error) {
      console.error('健康检查失败:', error)
      // 健康检查失败时设置为DOWN状态
      healthStatus.value = {
        status: 'DOWN',
        collectorEnabled: false,
        dataCollectionWorking: false,
        successRate: 0,
        error: error instanceof Error ? error.message : '健康检查失败'
      }
      throw error
    } finally {
      healthLoading.value = false
    }
  }
  
  /**
   * 获取统计数据
   */
  const fetchStatistics = async (timeRange: TimeRange = '24h') => {
    try {
      statisticsLoading.value = true
      
      const response = await getStatistics(timeRange)
      if (response.success && response.data) {
        statistics.value = response.data
        return response.data
      } else {
        throw new Error(response.message || '获取统计数据失败')
      }
    } catch (error) {
      console.error('获取统计数据失败:', error)
      throw error
    } finally {
      statisticsLoading.value = false
    }
  }
  
  // ==================== 高级操作 ====================
  
  /**
   * 获取监控概览数据
   */
  const fetchOverviewData = async () => {
    try {
      const overview = await getMonitorOverview()
      
      // 更新各个状态
      realtimeData.value = overview.realtime
      historyData.value = overview.history
      collectorStatus.value = overview.collector
      healthStatus.value = overview.health
      lastUpdateTime.value = overview.lastUpdated
      
      return overview
    } catch (error) {
      console.error('获取监控概览数据失败:', error)
      throw error
    }
  }
  
  /**
   * 刷新所有数据
   */
  const refreshAllData = async () => {
    try {
      await Promise.allSettled([
        fetchRealtimeData(false), // 不使用缓存
        fetchCollectorStatus(),
        fetchHealthStatus()
      ])
    } catch (error) {
      console.error('刷新数据失败:', error)
    }
  }
  
  // ==================== 轮询控制 ====================
  
  /**
   * 启动实时数据轮询
   */
  const startPolling = (interval?: number) => {
    if (pollingEnabled.value) {
      console.warn('轮询已经启动')
      return
    }
    
    if (interval) {
      pollingInterval.value = interval
    }
    
    pollingEnabled.value = true
    
    stopPolling = pollRealtimeData(
      (data, error) => {
        if (error) {
          console.error('轮询获取数据失败:', error)
          realtimeError.value = error.message
        } else if (data) {
          realtimeData.value = data
          lastUpdateTime.value = new Date().toISOString()
          realtimeError.value = null
        }
      },
      pollingInterval.value
    )
    
    console.log('实时数据轮询已启动，间隔:', pollingInterval.value, 'ms')
  }
  
  /**
   * 停止实时数据轮询
   */
  const stopPollingData = () => {
    if (!pollingEnabled.value) {
      console.warn('轮询未启动')
      return
    }
    
    pollingEnabled.value = false
    
    if (stopPolling) {
      stopPolling()
      stopPolling = null
    }
    
    console.log('实时数据轮询已停止')
  }
  
  /**
   * 切换轮询状态
   */
  const togglePolling = () => {
    if (pollingEnabled.value) {
      stopPollingData()
    } else {
      startPolling()
    }
  }
  
  // ==================== 缓存管理 ====================
  
  /**
   * 清除缓存
   */
  const clearCache = () => {
    monitorCache.clear()
    console.log('监控数据缓存已清除')
  }
  
  /**
   * 重置状态
   */
  const resetState = () => {
    // 停止轮询
    if (pollingEnabled.value) {
      stopPollingData()
    }
    
    // 清除所有数据
    realtimeData.value = null
    historyData.value = []
    collectorStatus.value = null
    healthStatus.value = null
    statistics.value = null
    
    // 重置加载状态
    realtimeLoading.value = false
    historyLoading.value = false
    collectorLoading.value = false
    healthLoading.value = false
    statisticsLoading.value = false
    
    // 清除错误
    realtimeError.value = null
    historyError.value = null
    
    // 重置分页
    historyTotal.value = 0
    historyPage.value = 1
    historySize.value = 20
    
    lastUpdateTime.value = null
    
    // 清除缓存
    clearCache()
    
    console.log('监控状态已重置')
  }
  
  // ==================== 导出状态和方法 ====================
  
  return {
    // 状态
    realtimeData,
    realtimeLoading,
    realtimeError,
    historyData,
    historyLoading,
    historyError,
    historyTotal,
    historyPage,
    historySize,
    collectorStatus,
    collectorLoading,
    healthStatus,
    healthLoading,
    statistics,
    statisticsLoading,
    pollingEnabled,
    pollingInterval,
    lastUpdateTime,
    
    // 计算属性
    hasRealtimeData,
    isSystemHealthy,
    cpuStatus,
    memoryStatus,
    diskStatus,
    jvmStatus,
    collectorRunning,
    
    // 方法
    fetchRealtimeData,
    fetchHistoryData,
    fetchCollectorStatus,
    fetchHealthStatus,
    fetchStatistics,
    fetchOverviewData,
    refreshAllData,
    startPolling,
    stopPollingData,
    togglePolling,
    clearCache,
    resetState
  }
})