package com.rickpan.service;

import com.rickpan.entity.SysMonitorAlertConfig;
import com.rickpan.entity.SysMonitorAlertLog;
import com.rickpan.entity.User;
import com.rickpan.repository.SysMonitorAlertConfigRepository;
import com.rickpan.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 告警通知服务
 * 负责处理告警通知的发送逻辑
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AlertNotificationService {

    private final EmailService emailService;
    private final UserRepository userRepository;
    private final SysMonitorAlertConfigRepository alertConfigRepository;

    /**
     * 发送告警通知
     * 
     * @param alertLog 告警日志
     */
    @Async
    public void sendAlertNotification(SysMonitorAlertLog alertLog) {
        try {
            log.debug("开始发送告警通知: {}", alertLog.getAlertTitle());
            
            // 检查告警配置的通知开关
            if (!shouldSendNotification(alertLog)) {
                log.debug("告警配置未启用通知，跳过发送: configId={}", alertLog.getAlertConfigId());
                return;
            }

            // 获取管理员邮箱列表
            List<String> adminEmails = getAdminEmails();
            if (adminEmails.isEmpty()) {
                log.warn("未找到管理员用户，无法发送告警邮件");
                return;
            }

            log.info("向 {} 位管理员发送告警通知: {}", adminEmails.size(), alertLog.getAlertTitle());

            // 发送邮件通知
            emailService.sendAlertNotification(adminEmails, alertLog);

            log.info("告警通知发送完成: {}", alertLog.getAlertTitle());

        } catch (Exception e) {
            log.error("发送告警通知失败: {}", alertLog.getAlertTitle(), e);
        }
    }

    /**
     * 批量发送告警通知
     * 
     * @param alertLogs 告警日志列表
     */
    @Async
    public void sendBatchAlertNotifications(List<SysMonitorAlertLog> alertLogs) {
        if (alertLogs == null || alertLogs.isEmpty()) {
            return;
        }

        log.info("开始批量发送 {} 个告警通知", alertLogs.size());

        for (SysMonitorAlertLog alertLog : alertLogs) {
            try {
                sendAlertNotification(alertLog);
            } catch (Exception e) {
                log.error("批量发送告警通知失败: {}", alertLog.getAlertTitle(), e);
                // 单个通知失败不影响其他通知发送
            }
        }

        log.info("批量告警通知发送完成");
    }

    /**
     * 检查是否应该发送通知
     * 
     * @param alertLog 告警日志
     * @return 是否发送通知
     */
    private boolean shouldSendNotification(SysMonitorAlertLog alertLog) {
        if (alertLog.getAlertConfigId() == null) {
            log.warn("告警日志缺少配置ID，无法检查通知开关");
            return true; // 默认发送
        }

        try {
            SysMonitorAlertConfig config = alertConfigRepository.findById(alertLog.getAlertConfigId())
                .orElse(null);
            
            if (config == null) {
                log.warn("未找到告警配置: configId={}", alertLog.getAlertConfigId());
                return true; // 默认发送
            }

            // 检查告警配置是否启用
            if (config.getEnabled() == null || !config.getEnabled()) {
                log.debug("告警配置已禁用: configId={}", alertLog.getAlertConfigId());
                return false;
            }

            // 检查通知开关
            if (config.getNotificationEnabled() == null || !config.getNotificationEnabled()) {
                log.debug("告警配置的通知功能已禁用: configId={}", alertLog.getAlertConfigId());
                return false;
            }

            return true;

        } catch (Exception e) {
            log.error("检查通知开关失败: configId={}", alertLog.getAlertConfigId(), e);
            return true; // 出错时默认发送
        }
    }

    /**
     * 获取管理员邮箱列表
     * 
     * @return 管理员邮箱列表
     */
    private List<String> getAdminEmails() {
        try {
            List<User> adminUsers = userRepository.findByUserType(User.UserType.ADMIN);
            
            return adminUsers.stream()
                .filter(user -> user.getEmail() != null && !user.getEmail().trim().isEmpty())
                .map(User::getEmail)
                .distinct()
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取管理员邮箱列表失败", e);
            return List.of();
        }
    }

    /**
     * 获取管理员用户数量
     * 
     * @return 管理员数量
     */
    public long getAdminUserCount() {
        try {
            return userRepository.countByUserType(User.UserType.ADMIN);
        } catch (Exception e) {
            log.error("获取管理员用户数量失败", e);
            return 0;
        }
    }

    /**
     * 验证邮件服务可用性
     * 
     * @return 是否可用
     */
    public boolean isEmailServiceAvailable() {
        try {
            // 这里可以添加邮件服务的健康检查逻辑
            // 比如检查邮件服务器连接状态
            return true;
        } catch (Exception e) {
            log.error("检查邮件服务可用性失败", e);
            return false;
        }
    }

    /**
     * 测试发送告警通知（用于系统测试）
     * 
     * @param testMessage 测试消息
     * @return 发送结果
     */
    public boolean sendTestAlertNotification(String testMessage) {
        try {
            List<String> adminEmails = getAdminEmails();
            if (adminEmails.isEmpty()) {
                log.warn("未找到管理员用户，无法发送测试邮件");
                return false;
            }

            // 创建测试告警日志
            SysMonitorAlertLog testAlert = SysMonitorAlertLog.builder()
                .alertTitle("系统监控测试告警")
                .alertMessage(testMessage)
                .alertLevel("WARNING")
                .metricType("SYSTEM")
                .metricName("test_metric")
                .alertTime(java.time.LocalDateTime.now())
                .build();

            emailService.sendAlertNotification(adminEmails, testAlert);
            
            log.info("测试告警通知发送成功，接收人数: {}", adminEmails.size());
            return true;

        } catch (Exception e) {
            log.error("发送测试告警通知失败", e);
            return false;
        }
    }
}